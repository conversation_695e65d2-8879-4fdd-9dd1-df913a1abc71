name: r2
description: "A new Flutter project."
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  json_annotation: ^4.9.0
  freezed: ^2.5.7
  freezed_annotation: ^2.4.4
  retrofit: ^4.4.1
  dio: ^5.7.0
  pretty_dio_logger: ^1.4.0
  flutter_svg: ^2.0.16  
  auto_route: ^9.2.2
  flutter_bloc: ^8.1.6
  get_it: ^8.0.2
  shared_preferences: ^2.3.3
  flutter_secure_storage: ^9.2.2
  intl: ^0.19.0
  flutter_screenutil: ^5.9.3
  flutter_easyloading: ^3.0.5
  country_code_picker: ^3.1.0
  pin_code_fields: ^8.0.1
  timer_count_down: ^2.2.2
  equatable: ^2.0.6
  dotted_border: ^2.1.0
  toggle_switch: ^2.3.0
  image_picker: ^1.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  flutter_gen_runner: ^5.8.0 
  json_serializable: ^6.9.0
  retrofit_generator: ^9.1.5
  auto_route_generator: ^9.0.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Cairo-Black.ttf
          weight: 900    

    - family: DS-Digital
      fonts:
        - asset: assets/fonts/DS-DIGIB.TTF
    

flutter_gen:
  output: lib/gen/
  integrations:
    flutter_svg: true
    flare_flutter: true
    rive: true
    lottie: true
