// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AddBranchScreen]
class AddBranchRoute extends PageRouteInfo<AddBranchRouteArgs> {
  AddBranchRoute({
    Key? key,
    HallModel? hall,
    List<PageRouteInfo>? children,
  }) : super(
          AddBranchRoute.name,
          args: AddBranchRouteArgs(
            key: key,
            hall: hall,
          ),
          initialChildren: children,
        );

  static const String name = 'AddBranchRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddBranchRouteArgs>(
          orElse: () => const AddBranchRouteArgs());
      return WrappedRoute(
          child: AddBranchScreen(
        key: args.key,
        hall: args.hall,
      ));
    },
  );
}

class AddBranchRouteArgs {
  const AddBranchRouteArgs({
    this.key,
    this.hall,
  });

  final Key? key;

  final HallModel? hall;

  @override
  String toString() {
    return 'AddBranchRouteArgs{key: $key, hall: $hall}';
  }
}

/// generated route for
/// [AddProductScreen]
class AddProductRoute extends PageRouteInfo<AddProductRouteArgs> {
  AddProductRoute({
    Key? key,
    CafeCategoryModel? cafeCategoryModel,
    CafeProductModel? cafeProductModel,
    List<PageRouteInfo>? children,
  }) : super(
          AddProductRoute.name,
          args: AddProductRouteArgs(
            key: key,
            cafeCategoryModel: cafeCategoryModel,
            cafeProductModel: cafeProductModel,
          ),
          initialChildren: children,
        );

  static const String name = 'AddProductRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddProductRouteArgs>(
          orElse: () => const AddProductRouteArgs());
      return WrappedRoute(
          child: AddProductScreen(
        key: args.key,
        cafeCategoryModel: args.cafeCategoryModel,
        cafeProductModel: args.cafeProductModel,
      ));
    },
  );
}

class AddProductRouteArgs {
  const AddProductRouteArgs({
    this.key,
    this.cafeCategoryModel,
    this.cafeProductModel,
  });

  final Key? key;

  final CafeCategoryModel? cafeCategoryModel;

  final CafeProductModel? cafeProductModel;

  @override
  String toString() {
    return 'AddProductRouteArgs{key: $key, cafeCategoryModel: $cafeCategoryModel, cafeProductModel: $cafeProductModel}';
  }
}

/// generated route for
/// [AddRoomScreen]
class AddRoomRoute extends PageRouteInfo<AddRoomRouteArgs> {
  AddRoomRoute({
    Key? key,
    required RoomModel? room,
    List<PageRouteInfo>? children,
  }) : super(
          AddRoomRoute.name,
          args: AddRoomRouteArgs(
            key: key,
            room: room,
          ),
          initialChildren: children,
        );

  static const String name = 'AddRoomRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddRoomRouteArgs>();
      return WrappedRoute(
          child: AddRoomScreen(
        key: args.key,
        room: args.room,
      ));
    },
  );
}

class AddRoomRouteArgs {
  const AddRoomRouteArgs({
    this.key,
    required this.room,
  });

  final Key? key;

  final RoomModel? room;

  @override
  String toString() {
    return 'AddRoomRouteArgs{key: $key, room: $room}';
  }
}

/// generated route for
/// [AddWorkerScreen]
class AddWorkerRoute extends PageRouteInfo<AddWorkerRouteArgs> {
  AddWorkerRoute({
    Key? key,
    UserModel? worker,
    List<PageRouteInfo>? children,
  }) : super(
          AddWorkerRoute.name,
          args: AddWorkerRouteArgs(
            key: key,
            worker: worker,
          ),
          initialChildren: children,
        );

  static const String name = 'AddWorkerRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddWorkerRouteArgs>(
          orElse: () => const AddWorkerRouteArgs());
      return WrappedRoute(
          child: AddWorkerScreen(
        key: args.key,
        worker: args.worker,
      ));
    },
  );
}

class AddWorkerRouteArgs {
  const AddWorkerRouteArgs({
    this.key,
    this.worker,
  });

  final Key? key;

  final UserModel? worker;

  @override
  String toString() {
    return 'AddWorkerRouteArgs{key: $key, worker: $worker}';
  }
}

/// generated route for
/// [BottomNavBarScreen]
class BottomNavBarRoute extends PageRouteInfo<void> {
  const BottomNavBarRoute({List<PageRouteInfo>? children})
      : super(
          BottomNavBarRoute.name,
          initialChildren: children,
        );

  static const String name = 'BottomNavBarRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const BottomNavBarScreen());
    },
  );
}

/// generated route for
/// [BranchesScreen]
class BranchesRoute extends PageRouteInfo<void> {
  const BranchesRoute({List<PageRouteInfo>? children})
      : super(
          BranchesRoute.name,
          initialChildren: children,
        );

  static const String name = 'BranchesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const BranchesScreen());
    },
  );
}

/// generated route for
/// [CafeCategoriesScreen]
class CafeCategoriesRoute extends PageRouteInfo<void> {
  const CafeCategoriesRoute({List<PageRouteInfo>? children})
      : super(
          CafeCategoriesRoute.name,
          initialChildren: children,
        );

  static const String name = 'CafeCategoriesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const CafeCategoriesScreen());
    },
  );
}

/// generated route for
/// [ConfirmPaymentScreen]
class ConfirmPaymentRoute extends PageRouteInfo<void> {
  const ConfirmPaymentRoute({List<PageRouteInfo>? children})
      : super(
          ConfirmPaymentRoute.name,
          initialChildren: children,
        );

  static const String name = 'ConfirmPaymentRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const ConfirmPaymentScreen();
    },
  );
}

/// generated route for
/// [LoginScreen]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const LoginScreen());
    },
  );
}

/// generated route for
/// [MenuCategoriesScreen]
class MenuCategoriesRoute extends PageRouteInfo<void> {
  const MenuCategoriesRoute({List<PageRouteInfo>? children})
      : super(
          MenuCategoriesRoute.name,
          initialChildren: children,
        );

  static const String name = 'MenuCategoriesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const MenuCategoriesScreen());
    },
  );
}

/// generated route for
/// [MenuProductsScreen]
class MenuProductsRoute extends PageRouteInfo<MenuProductsRouteArgs> {
  MenuProductsRoute({
    Key? key,
    CafeCategoryModel? cafeCategoryModel,
    List<PageRouteInfo>? children,
  }) : super(
          MenuProductsRoute.name,
          args: MenuProductsRouteArgs(
            key: key,
            cafeCategoryModel: cafeCategoryModel,
          ),
          initialChildren: children,
        );

  static const String name = 'MenuProductsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MenuProductsRouteArgs>(
          orElse: () => const MenuProductsRouteArgs());
      return WrappedRoute(
          child: MenuProductsScreen(
        key: args.key,
        cafeCategoryModel: args.cafeCategoryModel,
      ));
    },
  );
}

class MenuProductsRouteArgs {
  const MenuProductsRouteArgs({
    this.key,
    this.cafeCategoryModel,
  });

  final Key? key;

  final CafeCategoryModel? cafeCategoryModel;

  @override
  String toString() {
    return 'MenuProductsRouteArgs{key: $key, cafeCategoryModel: $cafeCategoryModel}';
  }
}

/// generated route for
/// [MySubscriptionScreen]
class MySubscriptionRoute extends PageRouteInfo<void> {
  const MySubscriptionRoute({List<PageRouteInfo>? children})
      : super(
          MySubscriptionRoute.name,
          initialChildren: children,
        );

  static const String name = 'MySubscriptionRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const MySubscriptionScreen();
    },
  );
}

/// generated route for
/// [OtpScreen]
class OtpRoute extends PageRouteInfo<void> {
  const OtpRoute({List<PageRouteInfo>? children})
      : super(
          OtpRoute.name,
          initialChildren: children,
        );

  static const String name = 'OtpRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const OtpScreen());
    },
  );
}

/// generated route for
/// [PackagesScreen]
class PackagesRoute extends PageRouteInfo<void> {
  const PackagesRoute({List<PageRouteInfo>? children})
      : super(
          PackagesRoute.name,
          initialChildren: children,
        );

  static const String name = 'PackagesRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PackagesScreen();
    },
  );
}

/// generated route for
/// [PaymentScreen]
class PaymentRoute extends PageRouteInfo<void> {
  const PaymentRoute({List<PageRouteInfo>? children})
      : super(
          PaymentRoute.name,
          initialChildren: children,
        );

  static const String name = 'PaymentRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const PaymentScreen();
    },
  );
}

/// generated route for
/// [ProductsScreen]
class ProductsRoute extends PageRouteInfo<ProductsRouteArgs> {
  ProductsRoute({
    Key? key,
    CafeCategoryModel? category,
    List<PageRouteInfo>? children,
  }) : super(
          ProductsRoute.name,
          args: ProductsRouteArgs(
            key: key,
            category: category,
          ),
          initialChildren: children,
        );

  static const String name = 'ProductsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProductsRouteArgs>(
          orElse: () => const ProductsRouteArgs());
      return WrappedRoute(
          child: ProductsScreen(
        key: args.key,
        category: args.category,
      ));
    },
  );
}

class ProductsRouteArgs {
  const ProductsRouteArgs({
    this.key,
    this.category,
  });

  final Key? key;

  final CafeCategoryModel? category;

  @override
  String toString() {
    return 'ProductsRouteArgs{key: $key, category: $category}';
  }
}

/// generated route for
/// [RegisterScreen]
class RegisterRoute extends PageRouteInfo<void> {
  const RegisterRoute({List<PageRouteInfo>? children})
      : super(
          RegisterRoute.name,
          initialChildren: children,
        );

  static const String name = 'RegisterRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const RegisterScreen();
    },
  );
}

/// generated route for
/// [ReserveRoomScreen]
class ReserveRoomRoute extends PageRouteInfo<ReserveRoomRouteArgs> {
  ReserveRoomRoute({
    Key? key,
    RoomModel? room,
    List<PageRouteInfo>? children,
  }) : super(
          ReserveRoomRoute.name,
          args: ReserveRoomRouteArgs(
            key: key,
            room: room,
          ),
          initialChildren: children,
        );

  static const String name = 'ReserveRoomRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ReserveRoomRouteArgs>(
          orElse: () => const ReserveRoomRouteArgs());
      return WrappedRoute(
          child: ReserveRoomScreen(
        key: args.key,
        room: args.room,
      ));
    },
  );
}

class ReserveRoomRouteArgs {
  const ReserveRoomRouteArgs({
    this.key,
    this.room,
  });

  final Key? key;

  final RoomModel? room;

  @override
  String toString() {
    return 'ReserveRoomRouteArgs{key: $key, room: $room}';
  }
}

/// generated route for
/// [RoomDetailsScreen]
class RoomDetailsRoute extends PageRouteInfo<RoomDetailsRouteArgs> {
  RoomDetailsRoute({
    Key? key,
    required RoomModel room,
    List<PageRouteInfo>? children,
  }) : super(
          RoomDetailsRoute.name,
          args: RoomDetailsRouteArgs(
            key: key,
            room: room,
          ),
          initialChildren: children,
        );

  static const String name = 'RoomDetailsRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<RoomDetailsRouteArgs>();
      return WrappedRoute(
          child: RoomDetailsScreen(
        key: args.key,
        room: args.room,
      ));
    },
  );
}

class RoomDetailsRouteArgs {
  const RoomDetailsRouteArgs({
    this.key,
    required this.room,
  });

  final Key? key;

  final RoomModel room;

  @override
  String toString() {
    return 'RoomDetailsRouteArgs{key: $key, room: $room}';
  }
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const SplashScreen());
    },
  );
}

/// generated route for
/// [WelcomeScreen]
class WelcomeRoute extends PageRouteInfo<void> {
  const WelcomeRoute({List<PageRouteInfo>? children})
      : super(
          WelcomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'WelcomeRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return WrappedRoute(child: const WelcomeScreen());
    },
  );
}
