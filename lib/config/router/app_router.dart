import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
import 'package:r2/src/branches/presentation/screens/add_branch_screen.dart';
import 'package:r2/src/auth/presentation/screens/login_screen.dart';
import 'package:r2/src/auth/presentation/screens/otp_screen.dart';
import 'package:r2/src/auth/presentation/screens/register_screen.dart';
import 'package:r2/src/auth/presentation/screens/welcome_screen.dart';
import 'package:r2/src/bottom_nav_bar/presentation/screens/bottom_nav_bar_screen.dart';
import 'package:r2/src/branches/presentation/screens/branches_screen.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/menu/presentation/screens/add_product_screen.dart';
import 'package:r2/src/menu/presentation/screens/menu_categories_screen.dart';
import 'package:r2/src/menu/presentation/screens/menu_products_screen.dart';
import 'package:r2/src/orders/presentation/screens/cafe_categories_screen.dart';
import 'package:r2/src/orders/presentation/screens/products_screen.dart';
import 'package:r2/src/packages/presentation/screens/confirm_payment_screen.dart';
import 'package:r2/src/packages/presentation/screens/my_subscription_screen.dart';
import 'package:r2/src/packages/presentation/screens/packages_screen.dart';
import 'package:r2/src/packages/presentation/screens/payment_screen.dart';
import 'package:r2/src/reservation/presentation/screens/room_details_screen.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/screens/add_room_screen.dart';
import 'package:r2/src/reservation/presentation/screens/reserve_room_screen.dart';
import 'package:r2/src/splash/presentation/screens/splash_screen.dart';
import 'package:r2/src/worker/presentation/screens/add_worker_screen.dart';

part 'app_router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: SplashRoute.page,
          initial: true,
        ),
        AutoRoute(page: LoginRoute.page),
        AutoRoute(page: RegisterRoute.page),
        AutoRoute(page: OtpRoute.page),
        AutoRoute(page: WelcomeRoute.page),
        AutoRoute(page: AddBranchRoute.page),
        AutoRoute(page: BottomNavBarRoute.page),
        AutoRoute(page: AddRoomRoute.page),
        AutoRoute(page: ReserveRoomRoute.page),
        AutoRoute(page: PackagesRoute.page),
        AutoRoute(page: PaymentRoute.page),
        AutoRoute(page: CafeCategoriesRoute.page),
        AutoRoute(page: ProductsRoute.page),
        AutoRoute(page: RoomDetailsRoute.page),
        AutoRoute(page: BranchesRoute.page),
        AutoRoute(page: MenuCategoriesRoute.page),
        AutoRoute(page: MenuProductsRoute.page),
        AutoRoute(page: AddProductRoute.page),
        AutoRoute(page: AddWorkerRoute.page),
        AutoRoute(page: ConfirmPaymentRoute.page),
        AutoRoute(page: MySubscriptionRoute.page),
      ];
}
