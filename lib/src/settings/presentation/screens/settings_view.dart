import 'package:flutter/material.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/settings/presentation/widgets/settings_view/settings_profile_section.dart';
import 'package:r2/src/settings/presentation/widgets/settings_view/settings_section.dart';

class SettingsView extends StatelessWidget {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: DefaultScaffoldGradient(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SettingsProfileSection(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 36),
                child: Column(
                  children: [
                    SettingsSection(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
