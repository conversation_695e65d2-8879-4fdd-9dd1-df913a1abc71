import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/gen/assets.gen.dart';

class SettingsSection extends StatelessWidget {
  const SettingsSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Column(
        children: [
          ListTile(
            onTap: () => context.router.push(const PackagesRoute()),
            contentPadding: EdgeInsets.zero,
            leading: Assets.icons.correctIconPurple.svg(),
            title: Text(
              context.l10n.packages,
              style: TextStyles.title16.copyWith(
                fontSize: 18,
              ),
            ),
          ),
          ListTile(
            onTap: () => context.router.push(const MySubscriptionRoute()),
            contentPadding: EdgeInsets.zero,
            leading: Assets.icons.subscripIcon.svg(),
            title: Text(
              context.l10n.mySubscription,
              style: TextStyles.title16.copyWith(
                fontSize: 18,
              ),
            ),
          ),
          ListTile(
            onTap: () => context.router.push(const MenuCategoriesRoute()),
            contentPadding: EdgeInsets.zero,
            leading: Assets.icons.menuIcon.svg(),
            title: Text(
              context.l10n.menu,
              style: TextStyles.title16.copyWith(
                fontSize: 18,
              ),
            ),
          ),
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Assets.icons.langIcon.svg(),
            title: Text(
              context.l10n.language,
              style: TextStyles.title16.copyWith(
                fontSize: 18,
              ),
            ),
          ),
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: Assets.icons.exitIcon.svg(),
            title: Text(
              context.l10n.exit,
              style: TextStyles.title16.copyWith(
                fontSize: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
