import 'package:dio/dio.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/reservation/data/models/reservation_params.dart';
import 'package:r2/src/reservation/data/models/reservation_response.dart';
import 'package:retrofit/retrofit.dart';

part 'reservation_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class ReservationApiService {
  factory ReservationApiService(
    Dio dio, {
    String baseUrl,
  }) = _ReservationApiService;
  @POST(EndPoints.reservations)
  Future<ReservationResponse> reserveRoom(@Body() ReserveRoomParams params);
  @GET('${EndPoints.reservations}/{id}')
  Future<ReservationResponse> getReservation(@Path('id') int roomId);
  @POST('${EndPoints.reservations}/{id}/stop')
  Future<ReservationResponse> stopReservation(@Path('id') int reservationId);
  @POST('${EndPoints.reservations}/{id}/end')
  Future<ReservationResponse> endReservation(@Path('id') int reservationId);
  @POST('${EndPoints.reservations}/{id}')
  Future<ReservationResponse> toggleReservation(
      @Body() ReserveRoomParams params, @Path('id') int reservationId);
  @POST(EndPoints.makeOrder)
  Future<BaseResponse> makeOrder(@Body() MakeOrderParams params);
}
