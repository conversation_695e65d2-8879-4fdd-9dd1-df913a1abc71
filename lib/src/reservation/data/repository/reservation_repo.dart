import 'package:r2/core/api/base_response.dart';
import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/reservation/data/api_service/reservation_api_service.dart';
import 'package:r2/src/reservation/data/models/reservation_params.dart';
import 'package:r2/src/reservation/data/models/reservation_response.dart';

class ReservationRepo {
  final ReservationApiService _reservationApiService;

  ReservationRepo(this._reservationApiService);

  Future<Result<ReservationResponse>> reserveRoom(
          ReserveRoomParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _reservationApiService.reserveRoom(params);
        return result;
      });

  Future<Result<ReservationResponse>> getReservation(int roomId) async =>
      await errorHandlerAsync(() async {
        final result = await _reservationApiService.getReservation(roomId);
        return result;
      });
  Future<Result<ReservationResponse>> stopReservation(
          int reservationId) async =>
      await errorHandlerAsync(() async {
        final result =
            await _reservationApiService.stopReservation(reservationId);
        return result;
      });
  Future<Result<ReservationResponse>> endReservation(int reservationId) async =>
      await errorHandlerAsync(() async {
        final result =
            await _reservationApiService.endReservation(reservationId);
        return result;
      });

  Future<Result<ReservationResponse>> toggleReservation(
          ReserveRoomParams params, int reservationId) async =>
      await errorHandlerAsync(() async {
        final result = await _reservationApiService.toggleReservation(
            params, reservationId);
        return result;
      });

  Future<Result<BaseResponse>> makeOrder(MakeOrderParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _reservationApiService.makeOrder(params);
        return result;
      });
}
