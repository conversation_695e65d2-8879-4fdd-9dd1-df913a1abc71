// To parse this JSON data, do
//
//     final reserveRoomParams = reserveRoomParamsFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'reservation_params.freezed.dart';
part 'reservation_params.g.dart';

ReserveRoomParams reserveRoomParamsFromJson(String str) =>
    ReserveRoomParams.fromJson(json.decode(str));

String reserveRoomParamsToJson(ReserveRoomParams data) =>
    json.encode(data.toJson());

@unfreezed
class ReserveRoomParams with _$ReserveRoomParams {
  factory ReserveRoomParams({
    @JsonKey(name: "room_id") int? roomId,
    @JsonKey(name: "reservation_type") String? reservationType,
    @JsonKey(name: "additions") List<int>? additions,
  }) = _ReserveRoomParams;

  factory ReserveRoomParams.fromJson(Map<String, dynamic> json) =>
      _$ReserveRoomParamsFromJson(json);
}

// To parse this JSON data, do
//
//     final makeOrderParams = makeOrderParamsFromJson(jsonString);

MakeOrderParams makeOrderParamsFromJson(String str) =>
    MakeOrderParams.fromJson(json.decode(str));

String makeOrderParamsToJson(MakeOrderParams data) =>
    json.encode(data.toJson());

@unfreezed
class MakeOrderParams with _$MakeOrderParams {
  factory MakeOrderParams({
    @JsonKey(name: "reservation_id") int? reservationId,
    @JsonKey(name: "product_id") int? productId,
    @JsonKey(name: "quantity") int? quantity,
    @JsonKey(name: "notes") String? notes,
  }) = _MakeOrderParams;

  factory MakeOrderParams.fromJson(Map<String, dynamic> json) =>
      _$MakeOrderParamsFromJson(json);
}
