// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invoice_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InvoiceModel _$InvoiceModelFromJson(Map<String, dynamic> json) {
  return _InvoiceModel.fromJson(json);
}

/// @nodoc
mixin _$InvoiceModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "single_time")
  double? get singleTime => throw _privateConstructorUsedError;
  @JsonKey(name: "single_price")
  double? get singlePrice => throw _privateConstructorUsedError;
  @JsonKey(name: "multi_time")
  double? get multiTime => throw _privateConstructorUsedError;
  @JsonKey(name: "multi_price")
  double? get multiPrice => throw _privateConstructorUsedError;
  @JsonKey(name: "empty_time")
  double? get emptyTime => throw _privateConstructorUsedError;
  @JsonKey(name: "empty_price")
  double? get emptyPrice => throw _privateConstructorUsedError;
  @JsonKey(name: "additions_price")
  double? get additionsPrice => throw _privateConstructorUsedError;
  @JsonKey(name: "order")
  Order? get order => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;

  /// Serializes this InvoiceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InvoiceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InvoiceModelCopyWith<InvoiceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvoiceModelCopyWith<$Res> {
  factory $InvoiceModelCopyWith(
          InvoiceModel value, $Res Function(InvoiceModel) then) =
      _$InvoiceModelCopyWithImpl<$Res, InvoiceModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "single_time") double? singleTime,
      @JsonKey(name: "single_price") double? singlePrice,
      @JsonKey(name: "multi_time") double? multiTime,
      @JsonKey(name: "multi_price") double? multiPrice,
      @JsonKey(name: "empty_time") double? emptyTime,
      @JsonKey(name: "empty_price") double? emptyPrice,
      @JsonKey(name: "additions_price") double? additionsPrice,
      @JsonKey(name: "order") Order? order,
      @JsonKey(name: "total") double? total});

  $OrderCopyWith<$Res>? get order;
}

/// @nodoc
class _$InvoiceModelCopyWithImpl<$Res, $Val extends InvoiceModel>
    implements $InvoiceModelCopyWith<$Res> {
  _$InvoiceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InvoiceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? singleTime = freezed,
    Object? singlePrice = freezed,
    Object? multiTime = freezed,
    Object? multiPrice = freezed,
    Object? emptyTime = freezed,
    Object? emptyPrice = freezed,
    Object? additionsPrice = freezed,
    Object? order = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      singleTime: freezed == singleTime
          ? _value.singleTime
          : singleTime // ignore: cast_nullable_to_non_nullable
              as double?,
      singlePrice: freezed == singlePrice
          ? _value.singlePrice
          : singlePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      multiTime: freezed == multiTime
          ? _value.multiTime
          : multiTime // ignore: cast_nullable_to_non_nullable
              as double?,
      multiPrice: freezed == multiPrice
          ? _value.multiPrice
          : multiPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      emptyTime: freezed == emptyTime
          ? _value.emptyTime
          : emptyTime // ignore: cast_nullable_to_non_nullable
              as double?,
      emptyPrice: freezed == emptyPrice
          ? _value.emptyPrice
          : emptyPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      additionsPrice: freezed == additionsPrice
          ? _value.additionsPrice
          : additionsPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as Order?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  /// Create a copy of InvoiceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderCopyWith<$Res>? get order {
    if (_value.order == null) {
      return null;
    }

    return $OrderCopyWith<$Res>(_value.order!, (value) {
      return _then(_value.copyWith(order: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InvoiceModelImplCopyWith<$Res>
    implements $InvoiceModelCopyWith<$Res> {
  factory _$$InvoiceModelImplCopyWith(
          _$InvoiceModelImpl value, $Res Function(_$InvoiceModelImpl) then) =
      __$$InvoiceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "single_time") double? singleTime,
      @JsonKey(name: "single_price") double? singlePrice,
      @JsonKey(name: "multi_time") double? multiTime,
      @JsonKey(name: "multi_price") double? multiPrice,
      @JsonKey(name: "empty_time") double? emptyTime,
      @JsonKey(name: "empty_price") double? emptyPrice,
      @JsonKey(name: "additions_price") double? additionsPrice,
      @JsonKey(name: "order") Order? order,
      @JsonKey(name: "total") double? total});

  @override
  $OrderCopyWith<$Res>? get order;
}

/// @nodoc
class __$$InvoiceModelImplCopyWithImpl<$Res>
    extends _$InvoiceModelCopyWithImpl<$Res, _$InvoiceModelImpl>
    implements _$$InvoiceModelImplCopyWith<$Res> {
  __$$InvoiceModelImplCopyWithImpl(
      _$InvoiceModelImpl _value, $Res Function(_$InvoiceModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of InvoiceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? singleTime = freezed,
    Object? singlePrice = freezed,
    Object? multiTime = freezed,
    Object? multiPrice = freezed,
    Object? emptyTime = freezed,
    Object? emptyPrice = freezed,
    Object? additionsPrice = freezed,
    Object? order = freezed,
    Object? total = freezed,
  }) {
    return _then(_$InvoiceModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      singleTime: freezed == singleTime
          ? _value.singleTime
          : singleTime // ignore: cast_nullable_to_non_nullable
              as double?,
      singlePrice: freezed == singlePrice
          ? _value.singlePrice
          : singlePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      multiTime: freezed == multiTime
          ? _value.multiTime
          : multiTime // ignore: cast_nullable_to_non_nullable
              as double?,
      multiPrice: freezed == multiPrice
          ? _value.multiPrice
          : multiPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      emptyTime: freezed == emptyTime
          ? _value.emptyTime
          : emptyTime // ignore: cast_nullable_to_non_nullable
              as double?,
      emptyPrice: freezed == emptyPrice
          ? _value.emptyPrice
          : emptyPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      additionsPrice: freezed == additionsPrice
          ? _value.additionsPrice
          : additionsPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as Order?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InvoiceModelImpl implements _InvoiceModel {
  const _$InvoiceModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "single_time") this.singleTime,
      @JsonKey(name: "single_price") this.singlePrice,
      @JsonKey(name: "multi_time") this.multiTime,
      @JsonKey(name: "multi_price") this.multiPrice,
      @JsonKey(name: "empty_time") this.emptyTime,
      @JsonKey(name: "empty_price") this.emptyPrice,
      @JsonKey(name: "additions_price") this.additionsPrice,
      @JsonKey(name: "order") this.order,
      @JsonKey(name: "total") this.total});

  factory _$InvoiceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$InvoiceModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "single_time")
  final double? singleTime;
  @override
  @JsonKey(name: "single_price")
  final double? singlePrice;
  @override
  @JsonKey(name: "multi_time")
  final double? multiTime;
  @override
  @JsonKey(name: "multi_price")
  final double? multiPrice;
  @override
  @JsonKey(name: "empty_time")
  final double? emptyTime;
  @override
  @JsonKey(name: "empty_price")
  final double? emptyPrice;
  @override
  @JsonKey(name: "additions_price")
  final double? additionsPrice;
  @override
  @JsonKey(name: "order")
  final Order? order;
  @override
  @JsonKey(name: "total")
  final double? total;

  @override
  String toString() {
    return 'InvoiceModel(id: $id, singleTime: $singleTime, singlePrice: $singlePrice, multiTime: $multiTime, multiPrice: $multiPrice, emptyTime: $emptyTime, emptyPrice: $emptyPrice, additionsPrice: $additionsPrice, order: $order, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvoiceModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.singleTime, singleTime) ||
                other.singleTime == singleTime) &&
            (identical(other.singlePrice, singlePrice) ||
                other.singlePrice == singlePrice) &&
            (identical(other.multiTime, multiTime) ||
                other.multiTime == multiTime) &&
            (identical(other.multiPrice, multiPrice) ||
                other.multiPrice == multiPrice) &&
            (identical(other.emptyTime, emptyTime) ||
                other.emptyTime == emptyTime) &&
            (identical(other.emptyPrice, emptyPrice) ||
                other.emptyPrice == emptyPrice) &&
            (identical(other.additionsPrice, additionsPrice) ||
                other.additionsPrice == additionsPrice) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      singleTime,
      singlePrice,
      multiTime,
      multiPrice,
      emptyTime,
      emptyPrice,
      additionsPrice,
      order,
      total);

  /// Create a copy of InvoiceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvoiceModelImplCopyWith<_$InvoiceModelImpl> get copyWith =>
      __$$InvoiceModelImplCopyWithImpl<_$InvoiceModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InvoiceModelImplToJson(
      this,
    );
  }
}

abstract class _InvoiceModel implements InvoiceModel {
  const factory _InvoiceModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "single_time") final double? singleTime,
      @JsonKey(name: "single_price") final double? singlePrice,
      @JsonKey(name: "multi_time") final double? multiTime,
      @JsonKey(name: "multi_price") final double? multiPrice,
      @JsonKey(name: "empty_time") final double? emptyTime,
      @JsonKey(name: "empty_price") final double? emptyPrice,
      @JsonKey(name: "additions_price") final double? additionsPrice,
      @JsonKey(name: "order") final Order? order,
      @JsonKey(name: "total") final double? total}) = _$InvoiceModelImpl;

  factory _InvoiceModel.fromJson(Map<String, dynamic> json) =
      _$InvoiceModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "single_time")
  double? get singleTime;
  @override
  @JsonKey(name: "single_price")
  double? get singlePrice;
  @override
  @JsonKey(name: "multi_time")
  double? get multiTime;
  @override
  @JsonKey(name: "multi_price")
  double? get multiPrice;
  @override
  @JsonKey(name: "empty_time")
  double? get emptyTime;
  @override
  @JsonKey(name: "empty_price")
  double? get emptyPrice;
  @override
  @JsonKey(name: "additions_price")
  double? get additionsPrice;
  @override
  @JsonKey(name: "order")
  Order? get order;
  @override
  @JsonKey(name: "total")
  double? get total;

  /// Create a copy of InvoiceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvoiceModelImplCopyWith<_$InvoiceModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Order _$OrderFromJson(Map<String, dynamic> json) {
  return _Order.fromJson(json);
}

/// @nodoc
mixin _$Order {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "products")
  List<OrderProductModel>? get products => throw _privateConstructorUsedError;

  /// Serializes this Order to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderCopyWith<Order> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCopyWith<$Res> {
  factory $OrderCopyWith(Order value, $Res Function(Order) then) =
      _$OrderCopyWithImpl<$Res, Order>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "products") List<OrderProductModel>? products});
}

/// @nodoc
class _$OrderCopyWithImpl<$Res, $Val extends Order>
    implements $OrderCopyWith<$Res> {
  _$OrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? total = freezed,
    Object? products = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      products: freezed == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<OrderProductModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderImplCopyWith<$Res> implements $OrderCopyWith<$Res> {
  factory _$$OrderImplCopyWith(
          _$OrderImpl value, $Res Function(_$OrderImpl) then) =
      __$$OrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "products") List<OrderProductModel>? products});
}

/// @nodoc
class __$$OrderImplCopyWithImpl<$Res>
    extends _$OrderCopyWithImpl<$Res, _$OrderImpl>
    implements _$$OrderImplCopyWith<$Res> {
  __$$OrderImplCopyWithImpl(
      _$OrderImpl _value, $Res Function(_$OrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? total = freezed,
    Object? products = freezed,
  }) {
    return _then(_$OrderImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      products: freezed == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<OrderProductModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderImpl implements _Order {
  const _$OrderImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "products") final List<OrderProductModel>? products})
      : _products = products;

  factory _$OrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "total")
  final double? total;
  final List<OrderProductModel>? _products;
  @override
  @JsonKey(name: "products")
  List<OrderProductModel>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Order(id: $id, total: $total, products: $products)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._products, _products));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, total, const DeepCollectionEquality().hash(_products));

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderImplCopyWith<_$OrderImpl> get copyWith =>
      __$$OrderImplCopyWithImpl<_$OrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderImplToJson(
      this,
    );
  }
}

abstract class _Order implements Order {
  const factory _Order(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "total") final double? total,
          @JsonKey(name: "products") final List<OrderProductModel>? products}) =
      _$OrderImpl;

  factory _Order.fromJson(Map<String, dynamic> json) = _$OrderImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "products")
  List<OrderProductModel>? get products;

  /// Create a copy of Order
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderImplCopyWith<_$OrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderProductModel _$OrderProductModelFromJson(Map<String, dynamic> json) {
  return _OrderProductModel.fromJson(json);
}

/// @nodoc
mixin _$OrderProductModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "product_id")
  int? get productId => throw _privateConstructorUsedError;
  @JsonKey(name: "quantity")
  int? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  double? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "notes")
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this OrderProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderProductModelCopyWith<OrderProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderProductModelCopyWith<$Res> {
  factory $OrderProductModelCopyWith(
          OrderProductModel value, $Res Function(OrderProductModel) then) =
      _$OrderProductModelCopyWithImpl<$Res, OrderProductModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: "product_id") int? productId,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "notes") String? notes});
}

/// @nodoc
class _$OrderProductModelCopyWithImpl<$Res, $Val extends OrderProductModel>
    implements $OrderProductModelCopyWith<$Res> {
  _$OrderProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? productId = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? total = freezed,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderProductModelImplCopyWith<$Res>
    implements $OrderProductModelCopyWith<$Res> {
  factory _$$OrderProductModelImplCopyWith(_$OrderProductModelImpl value,
          $Res Function(_$OrderProductModelImpl) then) =
      __$$OrderProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: 'name') String? name,
      @JsonKey(name: "product_id") int? productId,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "notes") String? notes});
}

/// @nodoc
class __$$OrderProductModelImplCopyWithImpl<$Res>
    extends _$OrderProductModelCopyWithImpl<$Res, _$OrderProductModelImpl>
    implements _$$OrderProductModelImplCopyWith<$Res> {
  __$$OrderProductModelImplCopyWithImpl(_$OrderProductModelImpl _value,
      $Res Function(_$OrderProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? productId = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? total = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$OrderProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderProductModelImpl implements _OrderProductModel {
  const _$OrderProductModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: 'name') this.name,
      @JsonKey(name: "product_id") this.productId,
      @JsonKey(name: "quantity") this.quantity,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "notes") this.notes});

  factory _$OrderProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderProductModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: 'name')
  final String? name;
  @override
  @JsonKey(name: "product_id")
  final int? productId;
  @override
  @JsonKey(name: "quantity")
  final int? quantity;
  @override
  @JsonKey(name: "price")
  final double? price;
  @override
  @JsonKey(name: "total")
  final double? total;
  @override
  @JsonKey(name: "notes")
  final String? notes;

  @override
  String toString() {
    return 'OrderProductModel(id: $id, name: $name, productId: $productId, quantity: $quantity, price: $price, total: $total, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, productId, quantity, price, total, notes);

  /// Create a copy of OrderProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderProductModelImplCopyWith<_$OrderProductModelImpl> get copyWith =>
      __$$OrderProductModelImplCopyWithImpl<_$OrderProductModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderProductModelImplToJson(
      this,
    );
  }
}

abstract class _OrderProductModel implements OrderProductModel {
  const factory _OrderProductModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: 'name') final String? name,
      @JsonKey(name: "product_id") final int? productId,
      @JsonKey(name: "quantity") final int? quantity,
      @JsonKey(name: "price") final double? price,
      @JsonKey(name: "total") final double? total,
      @JsonKey(name: "notes") final String? notes}) = _$OrderProductModelImpl;

  factory _OrderProductModel.fromJson(Map<String, dynamic> json) =
      _$OrderProductModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: 'name')
  String? get name;
  @override
  @JsonKey(name: "product_id")
  int? get productId;
  @override
  @JsonKey(name: "quantity")
  int? get quantity;
  @override
  @JsonKey(name: "price")
  double? get price;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "notes")
  String? get notes;

  /// Create a copy of OrderProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderProductModelImplCopyWith<_$OrderProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
