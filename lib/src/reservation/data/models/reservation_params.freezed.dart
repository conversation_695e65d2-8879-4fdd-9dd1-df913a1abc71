// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reservation_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReserveRoomParams _$ReserveRoomParamsFromJson(Map<String, dynamic> json) {
  return _ReserveRoomParams.fromJson(json);
}

/// @nodoc
mixin _$ReserveRoomParams {
  @JsonKey(name: "room_id")
  int? get roomId => throw _privateConstructorUsedError;
  @JsonKey(name: "room_id")
  set roomId(int? value) => throw _privateConstructorUsedError;
  @JsonKey(name: "reservation_type")
  String? get reservationType => throw _privateConstructorUsedError;
  @JsonKey(name: "reservation_type")
  set reservationType(String? value) => throw _privateConstructorUsedError;
  @JsonKey(name: "additions")
  List<int>? get additions => throw _privateConstructorUsedError;
  @JsonKey(name: "additions")
  set additions(List<int>? value) => throw _privateConstructorUsedError;

  /// Serializes this ReserveRoomParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReserveRoomParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReserveRoomParamsCopyWith<ReserveRoomParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReserveRoomParamsCopyWith<$Res> {
  factory $ReserveRoomParamsCopyWith(
          ReserveRoomParams value, $Res Function(ReserveRoomParams) then) =
      _$ReserveRoomParamsCopyWithImpl<$Res, ReserveRoomParams>;
  @useResult
  $Res call(
      {@JsonKey(name: "room_id") int? roomId,
      @JsonKey(name: "reservation_type") String? reservationType,
      @JsonKey(name: "additions") List<int>? additions});
}

/// @nodoc
class _$ReserveRoomParamsCopyWithImpl<$Res, $Val extends ReserveRoomParams>
    implements $ReserveRoomParamsCopyWith<$Res> {
  _$ReserveRoomParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReserveRoomParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? roomId = freezed,
    Object? reservationType = freezed,
    Object? additions = freezed,
  }) {
    return _then(_value.copyWith(
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as int?,
      reservationType: freezed == reservationType
          ? _value.reservationType
          : reservationType // ignore: cast_nullable_to_non_nullable
              as String?,
      additions: freezed == additions
          ? _value.additions
          : additions // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReserveRoomParamsImplCopyWith<$Res>
    implements $ReserveRoomParamsCopyWith<$Res> {
  factory _$$ReserveRoomParamsImplCopyWith(_$ReserveRoomParamsImpl value,
          $Res Function(_$ReserveRoomParamsImpl) then) =
      __$$ReserveRoomParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "room_id") int? roomId,
      @JsonKey(name: "reservation_type") String? reservationType,
      @JsonKey(name: "additions") List<int>? additions});
}

/// @nodoc
class __$$ReserveRoomParamsImplCopyWithImpl<$Res>
    extends _$ReserveRoomParamsCopyWithImpl<$Res, _$ReserveRoomParamsImpl>
    implements _$$ReserveRoomParamsImplCopyWith<$Res> {
  __$$ReserveRoomParamsImplCopyWithImpl(_$ReserveRoomParamsImpl _value,
      $Res Function(_$ReserveRoomParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReserveRoomParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? roomId = freezed,
    Object? reservationType = freezed,
    Object? additions = freezed,
  }) {
    return _then(_$ReserveRoomParamsImpl(
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as int?,
      reservationType: freezed == reservationType
          ? _value.reservationType
          : reservationType // ignore: cast_nullable_to_non_nullable
              as String?,
      additions: freezed == additions
          ? _value.additions
          : additions // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReserveRoomParamsImpl implements _ReserveRoomParams {
  _$ReserveRoomParamsImpl(
      {@JsonKey(name: "room_id") this.roomId,
      @JsonKey(name: "reservation_type") this.reservationType,
      @JsonKey(name: "additions") this.additions});

  factory _$ReserveRoomParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReserveRoomParamsImplFromJson(json);

  @override
  @JsonKey(name: "room_id")
  int? roomId;
  @override
  @JsonKey(name: "reservation_type")
  String? reservationType;
  @override
  @JsonKey(name: "additions")
  List<int>? additions;

  @override
  String toString() {
    return 'ReserveRoomParams(roomId: $roomId, reservationType: $reservationType, additions: $additions)';
  }

  /// Create a copy of ReserveRoomParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReserveRoomParamsImplCopyWith<_$ReserveRoomParamsImpl> get copyWith =>
      __$$ReserveRoomParamsImplCopyWithImpl<_$ReserveRoomParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReserveRoomParamsImplToJson(
      this,
    );
  }
}

abstract class _ReserveRoomParams implements ReserveRoomParams {
  factory _ReserveRoomParams(
          {@JsonKey(name: "room_id") int? roomId,
          @JsonKey(name: "reservation_type") String? reservationType,
          @JsonKey(name: "additions") List<int>? additions}) =
      _$ReserveRoomParamsImpl;

  factory _ReserveRoomParams.fromJson(Map<String, dynamic> json) =
      _$ReserveRoomParamsImpl.fromJson;

  @override
  @JsonKey(name: "room_id")
  int? get roomId;
  @JsonKey(name: "room_id")
  set roomId(int? value);
  @override
  @JsonKey(name: "reservation_type")
  String? get reservationType;
  @JsonKey(name: "reservation_type")
  set reservationType(String? value);
  @override
  @JsonKey(name: "additions")
  List<int>? get additions;
  @JsonKey(name: "additions")
  set additions(List<int>? value);

  /// Create a copy of ReserveRoomParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReserveRoomParamsImplCopyWith<_$ReserveRoomParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MakeOrderParams _$MakeOrderParamsFromJson(Map<String, dynamic> json) {
  return _MakeOrderParams.fromJson(json);
}

/// @nodoc
mixin _$MakeOrderParams {
  @JsonKey(name: "reservation_id")
  int? get reservationId => throw _privateConstructorUsedError;
  @JsonKey(name: "reservation_id")
  set reservationId(int? value) => throw _privateConstructorUsedError;
  @JsonKey(name: "product_id")
  int? get productId => throw _privateConstructorUsedError;
  @JsonKey(name: "product_id")
  set productId(int? value) => throw _privateConstructorUsedError;
  @JsonKey(name: "quantity")
  int? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: "quantity")
  set quantity(int? value) => throw _privateConstructorUsedError;
  @JsonKey(name: "notes")
  String? get notes => throw _privateConstructorUsedError;
  @JsonKey(name: "notes")
  set notes(String? value) => throw _privateConstructorUsedError;

  /// Serializes this MakeOrderParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MakeOrderParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MakeOrderParamsCopyWith<MakeOrderParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MakeOrderParamsCopyWith<$Res> {
  factory $MakeOrderParamsCopyWith(
          MakeOrderParams value, $Res Function(MakeOrderParams) then) =
      _$MakeOrderParamsCopyWithImpl<$Res, MakeOrderParams>;
  @useResult
  $Res call(
      {@JsonKey(name: "reservation_id") int? reservationId,
      @JsonKey(name: "product_id") int? productId,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "notes") String? notes});
}

/// @nodoc
class _$MakeOrderParamsCopyWithImpl<$Res, $Val extends MakeOrderParams>
    implements $MakeOrderParamsCopyWith<$Res> {
  _$MakeOrderParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MakeOrderParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reservationId = freezed,
    Object? productId = freezed,
    Object? quantity = freezed,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      reservationId: freezed == reservationId
          ? _value.reservationId
          : reservationId // ignore: cast_nullable_to_non_nullable
              as int?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MakeOrderParamsImplCopyWith<$Res>
    implements $MakeOrderParamsCopyWith<$Res> {
  factory _$$MakeOrderParamsImplCopyWith(_$MakeOrderParamsImpl value,
          $Res Function(_$MakeOrderParamsImpl) then) =
      __$$MakeOrderParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "reservation_id") int? reservationId,
      @JsonKey(name: "product_id") int? productId,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "notes") String? notes});
}

/// @nodoc
class __$$MakeOrderParamsImplCopyWithImpl<$Res>
    extends _$MakeOrderParamsCopyWithImpl<$Res, _$MakeOrderParamsImpl>
    implements _$$MakeOrderParamsImplCopyWith<$Res> {
  __$$MakeOrderParamsImplCopyWithImpl(
      _$MakeOrderParamsImpl _value, $Res Function(_$MakeOrderParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of MakeOrderParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reservationId = freezed,
    Object? productId = freezed,
    Object? quantity = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$MakeOrderParamsImpl(
      reservationId: freezed == reservationId
          ? _value.reservationId
          : reservationId // ignore: cast_nullable_to_non_nullable
              as int?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MakeOrderParamsImpl implements _MakeOrderParams {
  _$MakeOrderParamsImpl(
      {@JsonKey(name: "reservation_id") this.reservationId,
      @JsonKey(name: "product_id") this.productId,
      @JsonKey(name: "quantity") this.quantity,
      @JsonKey(name: "notes") this.notes});

  factory _$MakeOrderParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$MakeOrderParamsImplFromJson(json);

  @override
  @JsonKey(name: "reservation_id")
  int? reservationId;
  @override
  @JsonKey(name: "product_id")
  int? productId;
  @override
  @JsonKey(name: "quantity")
  int? quantity;
  @override
  @JsonKey(name: "notes")
  String? notes;

  @override
  String toString() {
    return 'MakeOrderParams(reservationId: $reservationId, productId: $productId, quantity: $quantity, notes: $notes)';
  }

  /// Create a copy of MakeOrderParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MakeOrderParamsImplCopyWith<_$MakeOrderParamsImpl> get copyWith =>
      __$$MakeOrderParamsImplCopyWithImpl<_$MakeOrderParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MakeOrderParamsImplToJson(
      this,
    );
  }
}

abstract class _MakeOrderParams implements MakeOrderParams {
  factory _MakeOrderParams(
      {@JsonKey(name: "reservation_id") int? reservationId,
      @JsonKey(name: "product_id") int? productId,
      @JsonKey(name: "quantity") int? quantity,
      @JsonKey(name: "notes") String? notes}) = _$MakeOrderParamsImpl;

  factory _MakeOrderParams.fromJson(Map<String, dynamic> json) =
      _$MakeOrderParamsImpl.fromJson;

  @override
  @JsonKey(name: "reservation_id")
  int? get reservationId;
  @JsonKey(name: "reservation_id")
  set reservationId(int? value);
  @override
  @JsonKey(name: "product_id")
  int? get productId;
  @JsonKey(name: "product_id")
  set productId(int? value);
  @override
  @JsonKey(name: "quantity")
  int? get quantity;
  @JsonKey(name: "quantity")
  set quantity(int? value);
  @override
  @JsonKey(name: "notes")
  String? get notes;
  @JsonKey(name: "notes")
  set notes(String? value);

  /// Create a copy of MakeOrderParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MakeOrderParamsImplCopyWith<_$MakeOrderParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
