// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reservation_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReservationModel _$ReservationModelFromJson(Map<String, dynamic> json) {
  return _ReservationModel.fromJson(json);
}

/// @nodoc
mixin _$ReservationModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "room_id")
  int? get roomId => throw _privateConstructorUsedError;
  @JsonKey(name: "reservation_type")
  String? get reservationType => throw _privateConstructorUsedError;
  @JsonKey(name: "start_at")
  DateTime? get startAt => throw _privateConstructorUsedError;
  @JsonKey(name: "end_at")
  DateTime? get endAt => throw _privateConstructorUsedError;
  @JsonKey(name: "price_per_hour")
  double? get pricePerHour => throw _privateConstructorUsedError;
  @JsonKey(name: "total")
  double? get total => throw _privateConstructorUsedError;
  @JsonKey(name: "room")
  RoomModel? get room => throw _privateConstructorUsedError;
  @JsonKey(name: "additions_price")
  double? get additionsPrice => throw _privateConstructorUsedError;
  @JsonKey(name: "additions")
  List<AdditionModel>? get additions => throw _privateConstructorUsedError;

  /// Serializes this ReservationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReservationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReservationModelCopyWith<ReservationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReservationModelCopyWith<$Res> {
  factory $ReservationModelCopyWith(
          ReservationModel value, $Res Function(ReservationModel) then) =
      _$ReservationModelCopyWithImpl<$Res, ReservationModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "room_id") int? roomId,
      @JsonKey(name: "reservation_type") String? reservationType,
      @JsonKey(name: "start_at") DateTime? startAt,
      @JsonKey(name: "end_at") DateTime? endAt,
      @JsonKey(name: "price_per_hour") double? pricePerHour,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "room") RoomModel? room,
      @JsonKey(name: "additions_price") double? additionsPrice,
      @JsonKey(name: "additions") List<AdditionModel>? additions});

  $RoomModelCopyWith<$Res>? get room;
}

/// @nodoc
class _$ReservationModelCopyWithImpl<$Res, $Val extends ReservationModel>
    implements $ReservationModelCopyWith<$Res> {
  _$ReservationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReservationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? roomId = freezed,
    Object? reservationType = freezed,
    Object? startAt = freezed,
    Object? endAt = freezed,
    Object? pricePerHour = freezed,
    Object? total = freezed,
    Object? room = freezed,
    Object? additionsPrice = freezed,
    Object? additions = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as int?,
      reservationType: freezed == reservationType
          ? _value.reservationType
          : reservationType // ignore: cast_nullable_to_non_nullable
              as String?,
      startAt: freezed == startAt
          ? _value.startAt
          : startAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endAt: freezed == endAt
          ? _value.endAt
          : endAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      pricePerHour: freezed == pricePerHour
          ? _value.pricePerHour
          : pricePerHour // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      room: freezed == room
          ? _value.room
          : room // ignore: cast_nullable_to_non_nullable
              as RoomModel?,
      additionsPrice: freezed == additionsPrice
          ? _value.additionsPrice
          : additionsPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      additions: freezed == additions
          ? _value.additions
          : additions // ignore: cast_nullable_to_non_nullable
              as List<AdditionModel>?,
    ) as $Val);
  }

  /// Create a copy of ReservationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RoomModelCopyWith<$Res>? get room {
    if (_value.room == null) {
      return null;
    }

    return $RoomModelCopyWith<$Res>(_value.room!, (value) {
      return _then(_value.copyWith(room: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReservationModelImplCopyWith<$Res>
    implements $ReservationModelCopyWith<$Res> {
  factory _$$ReservationModelImplCopyWith(_$ReservationModelImpl value,
          $Res Function(_$ReservationModelImpl) then) =
      __$$ReservationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "room_id") int? roomId,
      @JsonKey(name: "reservation_type") String? reservationType,
      @JsonKey(name: "start_at") DateTime? startAt,
      @JsonKey(name: "end_at") DateTime? endAt,
      @JsonKey(name: "price_per_hour") double? pricePerHour,
      @JsonKey(name: "total") double? total,
      @JsonKey(name: "room") RoomModel? room,
      @JsonKey(name: "additions_price") double? additionsPrice,
      @JsonKey(name: "additions") List<AdditionModel>? additions});

  @override
  $RoomModelCopyWith<$Res>? get room;
}

/// @nodoc
class __$$ReservationModelImplCopyWithImpl<$Res>
    extends _$ReservationModelCopyWithImpl<$Res, _$ReservationModelImpl>
    implements _$$ReservationModelImplCopyWith<$Res> {
  __$$ReservationModelImplCopyWithImpl(_$ReservationModelImpl _value,
      $Res Function(_$ReservationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? roomId = freezed,
    Object? reservationType = freezed,
    Object? startAt = freezed,
    Object? endAt = freezed,
    Object? pricePerHour = freezed,
    Object? total = freezed,
    Object? room = freezed,
    Object? additionsPrice = freezed,
    Object? additions = freezed,
  }) {
    return _then(_$ReservationModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as int?,
      reservationType: freezed == reservationType
          ? _value.reservationType
          : reservationType // ignore: cast_nullable_to_non_nullable
              as String?,
      startAt: freezed == startAt
          ? _value.startAt
          : startAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endAt: freezed == endAt
          ? _value.endAt
          : endAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      pricePerHour: freezed == pricePerHour
          ? _value.pricePerHour
          : pricePerHour // ignore: cast_nullable_to_non_nullable
              as double?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as double?,
      room: freezed == room
          ? _value.room
          : room // ignore: cast_nullable_to_non_nullable
              as RoomModel?,
      additionsPrice: freezed == additionsPrice
          ? _value.additionsPrice
          : additionsPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      additions: freezed == additions
          ? _value._additions
          : additions // ignore: cast_nullable_to_non_nullable
              as List<AdditionModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReservationModelImpl implements _ReservationModel {
  const _$ReservationModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "room_id") this.roomId,
      @JsonKey(name: "reservation_type") this.reservationType,
      @JsonKey(name: "start_at") this.startAt,
      @JsonKey(name: "end_at") this.endAt,
      @JsonKey(name: "price_per_hour") this.pricePerHour,
      @JsonKey(name: "total") this.total,
      @JsonKey(name: "room") this.room,
      @JsonKey(name: "additions_price") this.additionsPrice,
      @JsonKey(name: "additions") final List<AdditionModel>? additions})
      : _additions = additions;

  factory _$ReservationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReservationModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "room_id")
  final int? roomId;
  @override
  @JsonKey(name: "reservation_type")
  final String? reservationType;
  @override
  @JsonKey(name: "start_at")
  final DateTime? startAt;
  @override
  @JsonKey(name: "end_at")
  final DateTime? endAt;
  @override
  @JsonKey(name: "price_per_hour")
  final double? pricePerHour;
  @override
  @JsonKey(name: "total")
  final double? total;
  @override
  @JsonKey(name: "room")
  final RoomModel? room;
  @override
  @JsonKey(name: "additions_price")
  final double? additionsPrice;
  final List<AdditionModel>? _additions;
  @override
  @JsonKey(name: "additions")
  List<AdditionModel>? get additions {
    final value = _additions;
    if (value == null) return null;
    if (_additions is EqualUnmodifiableListView) return _additions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ReservationModel(id: $id, roomId: $roomId, reservationType: $reservationType, startAt: $startAt, endAt: $endAt, pricePerHour: $pricePerHour, total: $total, room: $room, additionsPrice: $additionsPrice, additions: $additions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReservationModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.roomId, roomId) || other.roomId == roomId) &&
            (identical(other.reservationType, reservationType) ||
                other.reservationType == reservationType) &&
            (identical(other.startAt, startAt) || other.startAt == startAt) &&
            (identical(other.endAt, endAt) || other.endAt == endAt) &&
            (identical(other.pricePerHour, pricePerHour) ||
                other.pricePerHour == pricePerHour) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.room, room) || other.room == room) &&
            (identical(other.additionsPrice, additionsPrice) ||
                other.additionsPrice == additionsPrice) &&
            const DeepCollectionEquality()
                .equals(other._additions, _additions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      roomId,
      reservationType,
      startAt,
      endAt,
      pricePerHour,
      total,
      room,
      additionsPrice,
      const DeepCollectionEquality().hash(_additions));

  /// Create a copy of ReservationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReservationModelImplCopyWith<_$ReservationModelImpl> get copyWith =>
      __$$ReservationModelImplCopyWithImpl<_$ReservationModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReservationModelImplToJson(
      this,
    );
  }
}

abstract class _ReservationModel implements ReservationModel {
  const factory _ReservationModel(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "room_id") final int? roomId,
          @JsonKey(name: "reservation_type") final String? reservationType,
          @JsonKey(name: "start_at") final DateTime? startAt,
          @JsonKey(name: "end_at") final DateTime? endAt,
          @JsonKey(name: "price_per_hour") final double? pricePerHour,
          @JsonKey(name: "total") final double? total,
          @JsonKey(name: "room") final RoomModel? room,
          @JsonKey(name: "additions_price") final double? additionsPrice,
          @JsonKey(name: "additions") final List<AdditionModel>? additions}) =
      _$ReservationModelImpl;

  factory _ReservationModel.fromJson(Map<String, dynamic> json) =
      _$ReservationModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "room_id")
  int? get roomId;
  @override
  @JsonKey(name: "reservation_type")
  String? get reservationType;
  @override
  @JsonKey(name: "start_at")
  DateTime? get startAt;
  @override
  @JsonKey(name: "end_at")
  DateTime? get endAt;
  @override
  @JsonKey(name: "price_per_hour")
  double? get pricePerHour;
  @override
  @JsonKey(name: "total")
  double? get total;
  @override
  @JsonKey(name: "room")
  RoomModel? get room;
  @override
  @JsonKey(name: "additions_price")
  double? get additionsPrice;
  @override
  @JsonKey(name: "additions")
  List<AdditionModel>? get additions;

  /// Create a copy of ReservationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReservationModelImplCopyWith<_$ReservationModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
