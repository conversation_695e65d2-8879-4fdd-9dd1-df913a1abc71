// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reservation_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReserveRoomParamsImpl _$$ReserveRoomParamsImplFromJson(
        Map<String, dynamic> json) =>
    _$ReserveRoomParamsImpl(
      roomId: (json['room_id'] as num?)?.toInt(),
      reservationType: json['reservation_type'] as String?,
      additions: (json['additions'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$$ReserveRoomParamsImplToJson(
        _$ReserveRoomParamsImpl instance) =>
    <String, dynamic>{
      'room_id': instance.roomId,
      'reservation_type': instance.reservationType,
      'additions': instance.additions,
    };

_$MakeOrderParamsImpl _$$MakeOrderParamsImplFromJson(
        Map<String, dynamic> json) =>
    _$MakeOrderParamsImpl(
      reservationId: (json['reservation_id'] as num?)?.toInt(),
      productId: (json['product_id'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$MakeOrderParamsImplToJson(
        _$MakeOrderParamsImpl instance) =>
    <String, dynamic>{
      'reservation_id': instance.reservationId,
      'product_id': instance.productId,
      'quantity': instance.quantity,
      'notes': instance.notes,
    };
