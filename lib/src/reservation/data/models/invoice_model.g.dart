// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InvoiceModelImpl _$$InvoiceModelImplFromJson(Map<String, dynamic> json) =>
    _$InvoiceModelImpl(
      id: (json['id'] as num?)?.toInt(),
      singleTime: (json['single_time'] as num?)?.toDouble(),
      singlePrice: (json['single_price'] as num?)?.toDouble(),
      multiTime: (json['multi_time'] as num?)?.toDouble(),
      multiPrice: (json['multi_price'] as num?)?.toDouble(),
      emptyTime: (json['empty_time'] as num?)?.toDouble(),
      emptyPrice: (json['empty_price'] as num?)?.toDouble(),
      additionsPrice: (json['additions_price'] as num?)?.toDouble(),
      order: json['order'] == null
          ? null
          : Order.fromJson(json['order'] as Map<String, dynamic>),
      total: (json['total'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$InvoiceModelImplToJson(_$InvoiceModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'single_time': instance.singleTime,
      'single_price': instance.singlePrice,
      'multi_time': instance.multiTime,
      'multi_price': instance.multiPrice,
      'empty_time': instance.emptyTime,
      'empty_price': instance.emptyPrice,
      'additions_price': instance.additionsPrice,
      'order': instance.order,
      'total': instance.total,
    };

_$OrderImpl _$$OrderImplFromJson(Map<String, dynamic> json) => _$OrderImpl(
      id: (json['id'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toDouble(),
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => OrderProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$OrderImplToJson(_$OrderImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'total': instance.total,
      'products': instance.products,
    };

_$OrderProductModelImpl _$$OrderProductModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OrderProductModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      productId: (json['product_id'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
      total: (json['total'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$OrderProductModelImplToJson(
        _$OrderProductModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'product_id': instance.productId,
      'quantity': instance.quantity,
      'price': instance.price,
      'total': instance.total,
      'notes': instance.notes,
    };
