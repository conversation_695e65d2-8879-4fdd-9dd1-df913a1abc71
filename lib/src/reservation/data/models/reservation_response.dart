import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/reservation/data/models/invoice_model.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';

part 'reservation_response.g.dart';
part 'reservation_response.freezed.dart';

@JsonSerializable()
class ReservationResponse extends BaseResponse {
  final Data? data;

  ReservationResponse(
      {this.data, required super.errors, required super.message});
  factory ReservationResponse.fromJson(Map<String, dynamic> json) =>
      _$ReservationResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$ReservationResponseToJson(this);
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "reservation") ReservationModel? reservation,
    @JsonKey(name: "invoice") InvoiceModel? invoice,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$Data<PERSON>(json);
}
