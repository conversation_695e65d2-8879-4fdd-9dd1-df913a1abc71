// To parse this JSON data, do
//
//     final reservationModel = reservationModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';

part 'reservation_model.freezed.dart';
part 'reservation_model.g.dart';

ReservationModel reservationModelFromJson(String str) =>
    ReservationModel.fromJson(json.decode(str));

String reservationModelToJson(ReservationModel data) =>
    json.encode(data.toJson());

@freezed
class ReservationModel with _$ReservationModel {
  const factory ReservationModel({
    @Json<PERSON>ey(name: "id") int? id,
    @JsonKey(name: "room_id") int? roomId,
    @JsonKey(name: "reservation_type") String? reservationType,
    @JsonKey(name: "start_at") DateTime? startAt,
    @Json<PERSON>ey(name: "end_at") DateTime? endAt,
    @JsonKey(name: "price_per_hour") double? pricePerHour,
    @JsonKey(name: "total") double? total,
    @Json<PERSON>ey(name: "room") RoomModel? room,
    @JsonKey(name: "additions_price") double? additionsPrice,
    @JsonKey(name: "additions") List<AdditionModel>? additions,
  }) = _ReservationModel;

  factory ReservationModel.fromJson(Map<String, dynamic> json) =>
      _$ReservationModelFromJson(json);
}
