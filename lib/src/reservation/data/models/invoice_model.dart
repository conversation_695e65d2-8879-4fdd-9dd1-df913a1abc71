// To parse this JSON data, do
//
//     final invoiceModel = invoiceModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'invoice_model.freezed.dart';
part 'invoice_model.g.dart';

InvoiceModel invoiceModelFromJson(String str) =>
    InvoiceModel.fromJson(json.decode(str));

String invoiceModelToJson(InvoiceModel data) => json.encode(data.toJson());

@freezed
class InvoiceModel with _$InvoiceModel {
  const factory InvoiceModel({
    @Json<PERSON>ey(name: "id") int? id,
    @Json<PERSON>ey(name: "single_time") double? singleTime,
    @Json<PERSON>ey(name: "single_price") double? singlePrice,
    @Json<PERSON>ey(name: "multi_time") double? multiTime,
    @JsonKey(name: "multi_price") double? multiPrice,
    @Json<PERSON><PERSON>(name: "empty_time") double? emptyTime,
    @Json<PERSON>ey(name: "empty_price") double? emptyPrice,
    @J<PERSON><PERSON><PERSON>(name: "additions_price") double? additionsPrice,
    @JsonKey(name: "order") Order? order,
    @JsonKey(name: "total") double? total,
  }) = _InvoiceModel;

  factory InvoiceModel.fromJson(Map<String, dynamic> json) =>
      _$InvoiceModelFromJson(json);
}

@freezed
class Order with _$Order {
  const factory Order({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "total") double? total,
    @JsonKey(name: "products") List<OrderProductModel>? products,
  }) = _Order;

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
}

@freezed
class OrderProductModel with _$OrderProductModel {
  const factory OrderProductModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: "product_id") int? productId,
    @JsonKey(name: "quantity") int? quantity,
    @JsonKey(name: "price") double? price,
    @JsonKey(name: "total") double? total,
    @JsonKey(name: "notes") String? notes,
  }) = _OrderProductModel;

  factory OrderProductModel.fromJson(Map<String, dynamic> json) =>
      _$OrderProductModelFromJson(json);
}
