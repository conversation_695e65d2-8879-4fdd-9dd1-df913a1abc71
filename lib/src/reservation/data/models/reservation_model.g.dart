// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reservation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReservationModelImpl _$$ReservationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ReservationModelImpl(
      id: (json['id'] as num?)?.toInt(),
      roomId: (json['room_id'] as num?)?.toInt(),
      reservationType: json['reservation_type'] as String?,
      startAt: json['start_at'] == null
          ? null
          : DateTime.parse(json['start_at'] as String),
      endAt: json['end_at'] == null
          ? null
          : DateTime.parse(json['end_at'] as String),
      pricePerHour: (json['price_per_hour'] as num?)?.toDouble(),
      total: (json['total'] as num?)?.toDouble(),
      room: json['room'] == null
          ? null
          : RoomModel.fromJson(json['room'] as Map<String, dynamic>),
      additionsPrice: (json['additions_price'] as num?)?.toDouble(),
      additions: (json['additions'] as List<dynamic>?)
          ?.map((e) => AdditionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ReservationModelImplToJson(
        _$ReservationModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'room_id': instance.roomId,
      'reservation_type': instance.reservationType,
      'start_at': instance.startAt?.toIso8601String(),
      'end_at': instance.endAt?.toIso8601String(),
      'price_per_hour': instance.pricePerHour,
      'total': instance.total,
      'room': instance.room,
      'additions_price': instance.additionsPrice,
      'additions': instance.additions,
    };
