import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/reservation/presentation/widgets/room_details/room_details_additions.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';

class TransformModal extends StatefulWidget {
  const TransformModal({
    super.key,
    this.reservation,
    this.room,
  });
  final ReservationModel? reservation;
  final RoomModel? room;

  @override
  State<TransformModal> createState() => _TransformModalState();
}

class _TransformModalState extends State<TransformModal> {
  final reservationCubit = injector<ReservationCubit>();
  bool? isTransformToEmpty;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ReservationCubit, ReservationState>(
      buildWhen: (previous, current) => current.maybeWhen(
        getReservationSuccess: () => true,
        orElse: () => false,
      ),
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16),
          child: Column(
            children: [
              Text(
                context.l10n.theTransform,
                style: TextStyles.title16.copyWith(fontWeight: FontWeight.w700),
              ),
              const SizedBox(height: 12),
              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  context.l10n.transformTo,
                  style: TextStyles.body14,
                ),
              ),
              const SizedBox(height: 4),
              if (widget.reservation?.reservationType == 'single')
                Row(
                  children: [
                    Expanded(
                      child: CustomElevatedButton(
                        isLight: false,
                        backgroundColor: Color(0xff5B4B6F),
                        borderColor: isTransformToEmpty == false
                            ? AppColors.primary
                            : Colors.transparent,
                        onPressed: () {
                          setState(() {
                            isTransformToEmpty = false;
                            injector<ReservationCubit>().reservationType =
                                'multi';
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Row(
                            children: [
                              Assets.icons.transIcon.svg(
                                colorFilter: ColorFilter.mode(
                                  Colors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.transformToMulti,
                                style: TextStyles.body14,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: CustomElevatedButton(
                        isLight: false,
                        backgroundColor: Color(0xff5B4B6F),
                        borderColor: isTransformToEmpty == true
                            ? AppColors.primary
                            : Colors.transparent,
                        onPressed: () {
                          setState(() {
                            isTransformToEmpty = true;
                            injector<ReservationCubit>().reservationType =
                                'empty';
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Row(
                            children: [
                              Assets.icons.transIcon.svg(
                                colorFilter: ColorFilter.mode(
                                  Colors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.transformToRoom,
                                style: TextStyles.body14,
                              )
                            ],
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              if (widget.reservation?.reservationType == 'multi')
                Row(
                  children: [
                    Expanded(
                      child: CustomElevatedButton(
                        isLight: false,
                        backgroundColor: Color(0xff5B4B6F),
                        borderColor: isTransformToEmpty == false
                            ? AppColors.primary
                            : Colors.transparent,
                        onPressed: () {
                          setState(() {
                            isTransformToEmpty = false;
                            injector<ReservationCubit>().reservationType =
                                'single';
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Row(
                            children: [
                              Assets.icons.transIcon.svg(
                                colorFilter: ColorFilter.mode(
                                  Colors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.transformToSingle,
                                style: TextStyles.body14,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: CustomElevatedButton(
                        isLight: false,
                        backgroundColor: Color(0xff5B4B6F),
                        borderColor: isTransformToEmpty == true
                            ? AppColors.primary
                            : Colors.transparent,
                        onPressed: () {
                          setState(() {
                            isTransformToEmpty = true;
                            injector<ReservationCubit>().reservationType =
                                'empty';
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Row(
                            children: [
                              Assets.icons.transIcon.svg(
                                colorFilter: ColorFilter.mode(
                                  Colors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.transformToRoom,
                                style: TextStyles.body14,
                              )
                            ],
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              if (widget.reservation?.reservationType == 'empty')
                Row(
                  children: [
                    Expanded(
                      child: CustomElevatedButton(
                        isLight: false,
                        backgroundColor: Color(0xff5B4B6F),
                        borderColor: isTransformToEmpty == false
                            ? AppColors.primary
                            : Colors.transparent,
                        onPressed: () {
                          setState(() {
                            isTransformToEmpty = false;
                            injector<ReservationCubit>().reservationType =
                                'single';
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Row(
                            children: [
                              Assets.icons.transIcon.svg(
                                colorFilter: ColorFilter.mode(
                                  Colors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.transformToSingle,
                                style: TextStyles.body14,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: CustomElevatedButton(
                        isLight: false,
                        backgroundColor: Color(0xff5B4B6F),
                        borderColor: isTransformToEmpty == true
                            ? AppColors.primary
                            : Colors.transparent,
                        onPressed: () {
                          setState(() {
                            isTransformToEmpty = true;
                            injector<ReservationCubit>().reservationType =
                                'multi';
                          });
                        },
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Row(
                            children: [
                              Assets.icons.transIcon.svg(
                                colorFilter: ColorFilter.mode(
                                  Colors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                context.l10n.transformToMulti,
                                style: TextStyles.body14,
                              )
                            ],
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              const SizedBox(height: 16),
              RoomDetailsAdditions(
                reservation: widget.reservation,
                room: widget.room,
              ),
              const SizedBox(height: 16),
              CustomElevatedButton(
                child: Text('تحويل'),
                onPressed: () => reservationCubit
                    .toggleReservation(widget.reservation?.id ?? 0),
              )
            ],
          ),
        );
      },
    );
  }
}
