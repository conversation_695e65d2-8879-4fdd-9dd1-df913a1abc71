import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/src/reservation/data/models/invoice_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';

class InvoiceSection extends StatelessWidget {
  const InvoiceSection({
    super.key,
    required this.invoice,
  });
  final InvoiceModel? invoice;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ReservationCubit, ReservationState>(
      buildWhen: (previous, current) => current.maybeWhen(
        getReservationSuccess: () => true,
        stopReservationSuccess: () => true,
        toggleReservationSuccess: () => true,
        orElse: () => false,
      ),
      builder: (context, state) {
        if (invoice?.total != 0) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.l10n.invoice,
                style: TextStyles.body16.copyWith(fontWeight: FontWeight.w700),
              ),
              const SizedBox(height: 8),
              Column(
                children: [
                  if (invoice?.singlePrice != 0)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${invoice?.singleTime?.toStringAsFixed(2)} ${context.l10n.singleHour}',
                          style: TextStyles.body14,
                        ),
                        Text(
                          '${invoice?.singlePrice?.toStringAsFixed(2)} ${context.l10n.pound}',
                          style: TextStyles.body14,
                        ),
                      ],
                    ),
                  const SizedBox(height: 5),
                  if (invoice?.multiPrice != 0)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${invoice?.multiTime?.toStringAsFixed(2)} ${context.l10n.multiHour}',
                          style: TextStyles.body14,
                        ),
                        Text(
                          '${invoice?.multiPrice?.toStringAsFixed(2)} ${context.l10n.pound}',
                          style: TextStyles.body14,
                        ),
                      ],
                    ),
                  const SizedBox(height: 5),
                  if (invoice?.emptyPrice != 0)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${invoice?.emptyTime?.toStringAsFixed(2)} ${context.l10n.roomHour}',
                          style: TextStyles.body14,
                        ),
                        Text(
                          '${invoice?.emptyPrice?.toStringAsFixed(2)} ${context.l10n.pound}',
                          style: TextStyles.body14,
                        ),
                      ],
                    ),
                  const SizedBox(height: 5),
                  if (invoice?.additionsPrice != 0)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.l10n.additions,
                          style: TextStyles.body14,
                        ),
                        Text(
                          '${invoice?.additionsPrice?.toStringAsFixed(2)} ${context.l10n.pound}',
                          style: TextStyles.body14,
                        ),
                      ],
                    ),
                  const SizedBox(height: 5),
                  if (invoice?.order != null)
                    ...List.generate(invoice?.order?.products?.length ?? 0,
                        (index) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${invoice?.order?.products?[index].name ?? ''} ${invoice?.order?.products?[index].quantity ?? ''}',
                            style: TextStyles.body14,
                          ),
                          Text(
                            '${invoice?.order?.products?[index].total?.toStringAsFixed(2)} ${context.l10n.pound}',
                            style: TextStyles.body14,
                          ),
                        ],
                      );
                    })
                ],
              ),
              Divider(color: Color(0xFF484848), thickness: 1),
              const SizedBox(height: 5),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.l10n.total,
                    style:
                        TextStyles.body14.copyWith(fontWeight: FontWeight.w700),
                  ),
                  Text(
                    '${invoice?.total?.toStringAsFixed(2)} ${context.l10n.pound}',
                    style:
                        TextStyles.body14.copyWith(fontWeight: FontWeight.w700),
                  ),
                ],
              ),
            ],
          );
        } else {
          return SizedBox();
        }
      },
    );
  }
}
