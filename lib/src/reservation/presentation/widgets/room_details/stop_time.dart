import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';

class StopTime extends StatefulWidget {
  const StopTime({
    super.key,
    required this.isRunning,
    required this.reservation,
  });
  final bool isRunning;
  final ReservationModel? reservation;
  @override
  State<StopTime> createState() => _StopTimeState();
}

class _StopTimeState extends State<StopTime> {
  final reservationCubit = injector<ReservationCubit>();
  @override
  Widget build(BuildContext context) {
    return widget.isRunning
        ? CustomElevatedButton(
            onPressed: () =>
                reservationCubit.stopReservation(widget.reservation?.id ?? 0),
            child: Text(context.l10n.stopTime),
          )
        : CustomElevatedButton(
            onPressed: () {
              UiHelper.showCustomDialog(
                barrierDismissible: false,
                context: context,
                dialog: AlertDialog(
                  backgroundColor: AppColors.darkPurple,
                  icon: Assets.icons.doorIcon.svg(),
                  title: Text(
                    context.l10n.areYouSureYouWantCloseRoom,
                    style: TextStyles.title16
                        .copyWith(fontWeight: FontWeight.w700),
                  ),
                  titlePadding: EdgeInsets.only(top: 10),
                  actionsPadding:
                      EdgeInsets.only(top: 40, bottom: 24, right: 30, left: 30),
                  iconPadding: EdgeInsets.only(top: 30, bottom: 10),
                  actions: [
                    CustomElevatedButton(
                      width: 250,
                      isLight: false,
                      child: Text(context.l10n.yes),
                      onPressed: () => reservationCubit
                          .endReservation(widget.reservation?.id ?? 0),
                    ),
                    const SizedBox(height: 16),
                    CustomElevatedButton(
                      width: 250,
                      isLight: false,
                      backgroundColor: AppColors.darkPurple,
                      borderColor: AppColors.primary,
                      child: Text(context.l10n.no),
                      onPressed: () => context.router.maybePop(),
                    )
                  ],
                ),
              );
            },
            child: Text(context.l10n.closeRoom),
          );
  }
}
