import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/reservation/presentation/widgets/room_details/transform_modal.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';

class RoomActions extends StatefulWidget {
  const RoomActions({
    super.key,
    this.reservation,
    required this.room,
  });
  final ReservationModel? reservation;
  final RoomModel room;

  @override
  State<RoomActions> createState() => _RoomActionsState();
}

class _RoomActionsState extends State<RoomActions> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CustomElevatedButton(
            onPressed: () => showModalBottomSheet(
              context: context,
              backgroundColor: AppColors.darkPurple,
              isScrollControlled: true,
              builder: (context) => BlocProvider.value(
                value: injector<ReservationCubit>(),
                child: IntrinsicHeight(
                  child: TransformModal(
                    reservation: widget.reservation,
                    room: widget.room,
                  ),
                ),
              ),
            ),
            borderColor: AppColors.primary,
            isLight: false,
            backgroundColor: Colors.black,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.transIcon.svg(),
                  const SizedBox(width: 8),
                  Text(
                    context.l10n.transform,
                    style: TextStyles.body16.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w700,
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: CustomElevatedButton(
            onPressed: () => context.router.push(CafeCategoriesRoute()),
            borderColor: AppColors.primary,
            isLight: false,
            backgroundColor: Colors.black,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.coffeIcon.svg(),
                  const SizedBox(width: 8),
                  Text(
                    context.l10n.menu,
                    style: TextStyles.body16.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w700,
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
