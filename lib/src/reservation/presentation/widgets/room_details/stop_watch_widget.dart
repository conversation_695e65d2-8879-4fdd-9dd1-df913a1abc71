import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/gen/fonts.gen.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';

class StopWatchWidget extends StatelessWidget {
  const StopWatchWidget({
    super.key,
    required this.time,
    required this.reservation,
  });

  final String time;
  final ReservationModel reservation;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ReservationCubit, ReservationState>(
      buildWhen: (previous, current) => current.maybeWhen(
        getReservationSuccess: () => true,
        toggleReservationSuccess: () => true,
        orElse: () => false,
      ),
      builder: (context, state) {
        return Center(
          child: Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.none,
            children: [
              Positioned(
                top: -25,
                child: Container(
                  height: 100,
                  width: .62.sw,
                  alignment: Alignment.topCenter,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(26),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.watch_later_outlined,
                        size: 16.sp,
                      ),
                      const SizedBox(width: 3),
                      Text(reservation.reservationType ?? '')
                    ],
                  ),
                ),
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 30),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(26),
                  border: Border.all(
                    width: 1,
                    color: AppColors.primary,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary,
                      blurRadius: 24.0,
                      spreadRadius: 1,
                    )
                  ],
                ),
                child: Center(
                  child: Text(
                    time,
                    style: TextStyle(
                      fontSize: 120.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.cyan,
                      fontFamily: FontFamily.dSDigital,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
