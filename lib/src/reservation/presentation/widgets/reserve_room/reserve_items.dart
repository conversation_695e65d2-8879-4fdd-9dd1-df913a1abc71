import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';

class ReserveItems extends StatelessWidget {
  const ReserveItems({
    super.key,
    this.room,
  });
  final RoomModel? room;

  @override
  Widget build(BuildContext context) {
    final reserveToggleCubit = context.watch<ToggleCubit<ReservationType>>();
    final reservationCubit = injector<ReservationCubit>();
    List<ReservationType> reservationTypes = [
      ReservationType(
        title: context.l10n.single,
        price: room?.singlePrice ?? 0,
        handle: 'single',
      ),
      ReservationType(
        title: context.l10n.multi,
        price: room?.multiPrice ?? 0,
        handle: 'multi',
      ),
      ReservationType(
        title: context.l10n.empty,
        price: room?.price ?? 0,
        handle: 'empty',
      ),
    ];
    return Column(
      children: [
        Wrap(
          direction: Axis.horizontal,
          spacing: 16,
          runSpacing: 10,
          children: List.generate(
            reservationTypes.length,
            (index) {
              return GestureDetector(
                onTap: () {
                  reserveToggleCubit.toggle(reservationTypes[index]);
                  reservationCubit.reservationType =
                      reservationTypes[index].handle;
                },
                child: Container(
                  width: 100,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.primary),
                    color: reserveToggleCubit.value?.price ==
                            reservationTypes[index].price
                        ? AppColors.primary
                        : AppColors.card,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${reservationTypes[index].price}',
                        style: TextStyles.headline24.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        reservationTypes[index].title,
                        style: TextStyles.body14.copyWith(
                          color: reserveToggleCubit.value?.price ==
                                  reservationTypes[index].price
                              ? Colors.white
                              : AppColors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 32),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: CustomElevatedButton(
            isDisabled: reserveToggleCubit.value == null,
            child: Text(context.l10n.startNow),
            onPressed: () {
              reserveToggleCubit.value == null
                  ? null
                  : reservationCubit.reserveRoom(room?.id ?? 0);
            },
          ),
        ),
      ],
    );
  }
}

class ReservationType {
  final String title;
  final double price;
  final String handle;

  ReservationType(
      {required this.title, required this.price, required this.handle});
}
