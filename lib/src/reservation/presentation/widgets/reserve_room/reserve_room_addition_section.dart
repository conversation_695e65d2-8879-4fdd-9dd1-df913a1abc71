import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';

class ReserveRoomAdditionSection extends StatefulWidget {
  const ReserveRoomAdditionSection({super.key, required this.room});

  final RoomModel? room;

  @override
  State<ReserveRoomAdditionSection> createState() =>
      _ReserveRoomAdditionSectionState();
}

class _ReserveRoomAdditionSectionState
    extends State<ReserveRoomAdditionSection> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(context.l10n.additions, style: TextStyles.body14),
        ...List.generate(widget.room?.additions?.length ?? 0, (index) {
          return Padding(
            padding: const EdgeInsets.only(top: 6.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 250,
                  child: AdditionItem(
                    index: index,
                    addition: widget.room?.additions?[index],
                  ),
                ),
                Text(
                  "${widget.room?.additions?[index].price} جم",
                  style: TextStyles.body14,
                ),
              ],
            ),
          );
        }),
        const SizedBox(height: 24),
      ],
    );
  }
}

class AdditionItem extends StatefulWidget {
  const AdditionItem({super.key, required this.index, required this.addition});
  final int index;
  final AdditionModel? addition;

  @override
  State<AdditionItem> createState() => _AdditionItemState();
}

class _AdditionItemState extends State<AdditionItem> {
  final reservationCubit = injector<ReservationCubit>();
  bool isSelected = false;
  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      visualDensity: VisualDensity(horizontal: -4, vertical: -1),
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
      fillColor: WidgetStatePropertyAll(AppColors.card),
      checkColor: AppColors.primary,
      activeColor: AppColors.primary,
      side: BorderSide(
        color: AppColors.primary,
        width: 1,
      ),
      checkboxShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
        side: BorderSide(
          color: AppColors.primary,
          width: 1,
        ),
      ),
      value: isSelected,
      onChanged: (value) {
        setState(() {
          isSelected = value ?? false;
        });
        if (isSelected) {
          reservationCubit.additions.add(widget.addition?.id ?? 0);
        } else {
          reservationCubit.additions.remove(widget.addition?.id ?? 0);
        }
      },
      title: Row(
        children: [
          Assets.icons.wifiIcon.svg(),
          const SizedBox(width: 10),
          Text(
            widget.addition?.name ?? '',
            style: TextStyles.body14,
          ),
        ],
      ),
    );
  }
}
