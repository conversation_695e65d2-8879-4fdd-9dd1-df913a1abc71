import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/src/reservation/data/models/reservation_params.dart';
import 'package:r2/src/reservation/data/repository/reservation_repo.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_operations.dart';

part 'reservation_state.dart';
part 'reservation_cubit.freezed.dart';

class ReservationCubit extends Cubit<ReservationState>
    with ReservationOperations {
  final ReservationRepo _reservationRepo;

  ReservationCubit(this._reservationRepo) : super(ReservationState.initial());
  Future<void> reserveRoom(int roomId) async {
    emit(ReservationState.loading());
    final params = ReserveRoomParams(
      roomId: roomId,
      reservationType: reservationType,
      additions: additions,
    );
    final response = await _reservationRepo.reserveRoom(params);
    response.when(
      success: (result) {
        reservationModel = result.data?.reservation;
        emit(ReservationState.reserveRoomSuccess());
      },
      failure: (message) => emit(
        ReservationState.reserveRoomFailure(message),
      ),
    );
  }

  Future<void> getReservation(int roomId) async {
    emit(ReservationState.loading());
    final response = await _reservationRepo.getReservation(roomId);
    response.when(
      success: (result) {
        reservationModel = result.data?.reservation;
        invoiceModel = result.data?.invoice;
        emit(ReservationState.getReservationSuccess());
      },
      failure: (message) => emit(
        ReservationState.getReservationFailure(message),
      ),
    );
  }

  Future<void> stopReservation(int reservationId) async {
    emit(ReservationState.loading());

    final response = await _reservationRepo.stopReservation(reservationId);
    response.when(
      success: (result) {
        reservationModel = result.data?.reservation;
        invoiceModel = result.data?.invoice;

        emit(ReservationState.stopReservationSuccess());
      },
      failure: (message) => emit(
        ReservationState.stopReservationFailure(message),
      ),
    );
  }

  Future<void> endReservation(int reservationId) async {
    emit(ReservationState.loading());

    final response = await _reservationRepo.endReservation(reservationId);
    response.when(
      success: (result) {
        reservationModel = result.data?.reservation;
        invoiceModel = result.data?.invoice;
        emit(ReservationState.endReservationSuccess());
      },
      failure: (message) => emit(
        ReservationState.endReservationFailure(message),
      ),
    );
  }

  Future<void> toggleReservation(int reservationId) async {
    emit(ReservationState.loading());
    final params = ReserveRoomParams(
      reservationType: reservationType,
      additions: additions,
    );
    final response =
        await _reservationRepo.toggleReservation(params, reservationId);
    response.when(
      success: (result) {
        reservationModel = result.data?.reservation;
        invoiceModel = result.data?.invoice;
        emit(ReservationState.toggleReservationSuccess());
      },
      failure: (message) => emit(
        ReservationState.toggleReservationFailure(message),
      ),
    );
  }

  Future<void> makeOrder(int reservationId) async {
    emit(ReservationState.loading());
    final params = MakeOrderParams(
      quantity: quantity,
      notes: notes,
      productId: productId,
      reservationId: reservationId,
    );
    final response = await _reservationRepo.makeOrder(params);
    response.when(
      success: (result) {
        emit(ReservationState.makeOrderSuccess());
      },
      failure: (message) => emit(
        ReservationState.makeOrderFailure(message),
      ),
    );
  }
}
