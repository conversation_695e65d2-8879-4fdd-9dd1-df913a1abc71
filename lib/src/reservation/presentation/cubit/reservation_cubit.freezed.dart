// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reservation_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ReservationState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReservationStateCopyWith<$Res> {
  factory $ReservationStateCopyWith(
          ReservationState value, $Res Function(ReservationState) then) =
      _$ReservationStateCopyWithImpl<$Res, ReservationState>;
}

/// @nodoc
class _$ReservationStateCopyWithImpl<$Res, $Val extends ReservationState>
    implements $ReservationStateCopyWith<$Res> {
  _$ReservationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'ReservationState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ReservationState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'ReservationState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements ReservationState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$ReserveRoomSuccessImplCopyWith<$Res> {
  factory _$$ReserveRoomSuccessImplCopyWith(_$ReserveRoomSuccessImpl value,
          $Res Function(_$ReserveRoomSuccessImpl) then) =
      __$$ReserveRoomSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReserveRoomSuccessImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$ReserveRoomSuccessImpl>
    implements _$$ReserveRoomSuccessImplCopyWith<$Res> {
  __$$ReserveRoomSuccessImplCopyWithImpl(_$ReserveRoomSuccessImpl _value,
      $Res Function(_$ReserveRoomSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ReserveRoomSuccessImpl implements _ReserveRoomSuccess {
  const _$ReserveRoomSuccessImpl();

  @override
  String toString() {
    return 'ReservationState.reserveRoomSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ReserveRoomSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return reserveRoomSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return reserveRoomSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (reserveRoomSuccess != null) {
      return reserveRoomSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return reserveRoomSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return reserveRoomSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (reserveRoomSuccess != null) {
      return reserveRoomSuccess(this);
    }
    return orElse();
  }
}

abstract class _ReserveRoomSuccess implements ReservationState {
  const factory _ReserveRoomSuccess() = _$ReserveRoomSuccessImpl;
}

/// @nodoc
abstract class _$$ReserveRoomFailureImplCopyWith<$Res> {
  factory _$$ReserveRoomFailureImplCopyWith(_$ReserveRoomFailureImpl value,
          $Res Function(_$ReserveRoomFailureImpl) then) =
      __$$ReserveRoomFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ReserveRoomFailureImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$ReserveRoomFailureImpl>
    implements _$$ReserveRoomFailureImplCopyWith<$Res> {
  __$$ReserveRoomFailureImplCopyWithImpl(_$ReserveRoomFailureImpl _value,
      $Res Function(_$ReserveRoomFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ReserveRoomFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ReserveRoomFailureImpl implements _ReserveRoomFailure {
  const _$ReserveRoomFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ReservationState.reserveRoomFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReserveRoomFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReserveRoomFailureImplCopyWith<_$ReserveRoomFailureImpl> get copyWith =>
      __$$ReserveRoomFailureImplCopyWithImpl<_$ReserveRoomFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return reserveRoomFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return reserveRoomFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (reserveRoomFailure != null) {
      return reserveRoomFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return reserveRoomFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return reserveRoomFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (reserveRoomFailure != null) {
      return reserveRoomFailure(this);
    }
    return orElse();
  }
}

abstract class _ReserveRoomFailure implements ReservationState {
  const factory _ReserveRoomFailure(final String message) =
      _$ReserveRoomFailureImpl;

  String get message;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReserveRoomFailureImplCopyWith<_$ReserveRoomFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetReservationSuccessImplCopyWith<$Res> {
  factory _$$GetReservationSuccessImplCopyWith(
          _$GetReservationSuccessImpl value,
          $Res Function(_$GetReservationSuccessImpl) then) =
      __$$GetReservationSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetReservationSuccessImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$GetReservationSuccessImpl>
    implements _$$GetReservationSuccessImplCopyWith<$Res> {
  __$$GetReservationSuccessImplCopyWithImpl(_$GetReservationSuccessImpl _value,
      $Res Function(_$GetReservationSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetReservationSuccessImpl implements _GetReservationSuccess {
  const _$GetReservationSuccessImpl();

  @override
  String toString() {
    return 'ReservationState.getReservationSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetReservationSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return getReservationSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return getReservationSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (getReservationSuccess != null) {
      return getReservationSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return getReservationSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return getReservationSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (getReservationSuccess != null) {
      return getReservationSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetReservationSuccess implements ReservationState {
  const factory _GetReservationSuccess() = _$GetReservationSuccessImpl;
}

/// @nodoc
abstract class _$$GetReservationFailureImplCopyWith<$Res> {
  factory _$$GetReservationFailureImplCopyWith(
          _$GetReservationFailureImpl value,
          $Res Function(_$GetReservationFailureImpl) then) =
      __$$GetReservationFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$GetReservationFailureImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$GetReservationFailureImpl>
    implements _$$GetReservationFailureImplCopyWith<$Res> {
  __$$GetReservationFailureImplCopyWithImpl(_$GetReservationFailureImpl _value,
      $Res Function(_$GetReservationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$GetReservationFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetReservationFailureImpl implements _GetReservationFailure {
  const _$GetReservationFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ReservationState.getReservationFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetReservationFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetReservationFailureImplCopyWith<_$GetReservationFailureImpl>
      get copyWith => __$$GetReservationFailureImplCopyWithImpl<
          _$GetReservationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return getReservationFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return getReservationFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (getReservationFailure != null) {
      return getReservationFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return getReservationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return getReservationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (getReservationFailure != null) {
      return getReservationFailure(this);
    }
    return orElse();
  }
}

abstract class _GetReservationFailure implements ReservationState {
  const factory _GetReservationFailure(final String message) =
      _$GetReservationFailureImpl;

  String get message;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetReservationFailureImplCopyWith<_$GetReservationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StopReservationSuccessImplCopyWith<$Res> {
  factory _$$StopReservationSuccessImplCopyWith(
          _$StopReservationSuccessImpl value,
          $Res Function(_$StopReservationSuccessImpl) then) =
      __$$StopReservationSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopReservationSuccessImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$StopReservationSuccessImpl>
    implements _$$StopReservationSuccessImplCopyWith<$Res> {
  __$$StopReservationSuccessImplCopyWithImpl(
      _$StopReservationSuccessImpl _value,
      $Res Function(_$StopReservationSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StopReservationSuccessImpl implements _StopReservationSuccess {
  const _$StopReservationSuccessImpl();

  @override
  String toString() {
    return 'ReservationState.stopReservationSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StopReservationSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return stopReservationSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return stopReservationSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (stopReservationSuccess != null) {
      return stopReservationSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return stopReservationSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return stopReservationSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (stopReservationSuccess != null) {
      return stopReservationSuccess(this);
    }
    return orElse();
  }
}

abstract class _StopReservationSuccess implements ReservationState {
  const factory _StopReservationSuccess() = _$StopReservationSuccessImpl;
}

/// @nodoc
abstract class _$$StopReservationFailureImplCopyWith<$Res> {
  factory _$$StopReservationFailureImplCopyWith(
          _$StopReservationFailureImpl value,
          $Res Function(_$StopReservationFailureImpl) then) =
      __$$StopReservationFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$StopReservationFailureImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$StopReservationFailureImpl>
    implements _$$StopReservationFailureImplCopyWith<$Res> {
  __$$StopReservationFailureImplCopyWithImpl(
      _$StopReservationFailureImpl _value,
      $Res Function(_$StopReservationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$StopReservationFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$StopReservationFailureImpl implements _StopReservationFailure {
  const _$StopReservationFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ReservationState.stopReservationFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StopReservationFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StopReservationFailureImplCopyWith<_$StopReservationFailureImpl>
      get copyWith => __$$StopReservationFailureImplCopyWithImpl<
          _$StopReservationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return stopReservationFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return stopReservationFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (stopReservationFailure != null) {
      return stopReservationFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return stopReservationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return stopReservationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (stopReservationFailure != null) {
      return stopReservationFailure(this);
    }
    return orElse();
  }
}

abstract class _StopReservationFailure implements ReservationState {
  const factory _StopReservationFailure(final String message) =
      _$StopReservationFailureImpl;

  String get message;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StopReservationFailureImplCopyWith<_$StopReservationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EndReservationSuccessImplCopyWith<$Res> {
  factory _$$EndReservationSuccessImplCopyWith(
          _$EndReservationSuccessImpl value,
          $Res Function(_$EndReservationSuccessImpl) then) =
      __$$EndReservationSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EndReservationSuccessImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$EndReservationSuccessImpl>
    implements _$$EndReservationSuccessImplCopyWith<$Res> {
  __$$EndReservationSuccessImplCopyWithImpl(_$EndReservationSuccessImpl _value,
      $Res Function(_$EndReservationSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EndReservationSuccessImpl implements _EndReservationSuccess {
  const _$EndReservationSuccessImpl();

  @override
  String toString() {
    return 'ReservationState.endReservationSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EndReservationSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return endReservationSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return endReservationSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (endReservationSuccess != null) {
      return endReservationSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return endReservationSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return endReservationSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (endReservationSuccess != null) {
      return endReservationSuccess(this);
    }
    return orElse();
  }
}

abstract class _EndReservationSuccess implements ReservationState {
  const factory _EndReservationSuccess() = _$EndReservationSuccessImpl;
}

/// @nodoc
abstract class _$$EndReservationFailureImplCopyWith<$Res> {
  factory _$$EndReservationFailureImplCopyWith(
          _$EndReservationFailureImpl value,
          $Res Function(_$EndReservationFailureImpl) then) =
      __$$EndReservationFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$EndReservationFailureImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$EndReservationFailureImpl>
    implements _$$EndReservationFailureImplCopyWith<$Res> {
  __$$EndReservationFailureImplCopyWithImpl(_$EndReservationFailureImpl _value,
      $Res Function(_$EndReservationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$EndReservationFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EndReservationFailureImpl implements _EndReservationFailure {
  const _$EndReservationFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ReservationState.endReservationFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EndReservationFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EndReservationFailureImplCopyWith<_$EndReservationFailureImpl>
      get copyWith => __$$EndReservationFailureImplCopyWithImpl<
          _$EndReservationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return endReservationFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return endReservationFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (endReservationFailure != null) {
      return endReservationFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return endReservationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return endReservationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (endReservationFailure != null) {
      return endReservationFailure(this);
    }
    return orElse();
  }
}

abstract class _EndReservationFailure implements ReservationState {
  const factory _EndReservationFailure(final String message) =
      _$EndReservationFailureImpl;

  String get message;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EndReservationFailureImplCopyWith<_$EndReservationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ToggleReservationSuccessImplCopyWith<$Res> {
  factory _$$ToggleReservationSuccessImplCopyWith(
          _$ToggleReservationSuccessImpl value,
          $Res Function(_$ToggleReservationSuccessImpl) then) =
      __$$ToggleReservationSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ToggleReservationSuccessImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$ToggleReservationSuccessImpl>
    implements _$$ToggleReservationSuccessImplCopyWith<$Res> {
  __$$ToggleReservationSuccessImplCopyWithImpl(
      _$ToggleReservationSuccessImpl _value,
      $Res Function(_$ToggleReservationSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ToggleReservationSuccessImpl implements _ToggleReservationSuccess {
  const _$ToggleReservationSuccessImpl();

  @override
  String toString() {
    return 'ReservationState.toggleReservationSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToggleReservationSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return toggleReservationSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return toggleReservationSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (toggleReservationSuccess != null) {
      return toggleReservationSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return toggleReservationSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return toggleReservationSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (toggleReservationSuccess != null) {
      return toggleReservationSuccess(this);
    }
    return orElse();
  }
}

abstract class _ToggleReservationSuccess implements ReservationState {
  const factory _ToggleReservationSuccess() = _$ToggleReservationSuccessImpl;
}

/// @nodoc
abstract class _$$ToggleReservationFailureImplCopyWith<$Res> {
  factory _$$ToggleReservationFailureImplCopyWith(
          _$ToggleReservationFailureImpl value,
          $Res Function(_$ToggleReservationFailureImpl) then) =
      __$$ToggleReservationFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ToggleReservationFailureImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$ToggleReservationFailureImpl>
    implements _$$ToggleReservationFailureImplCopyWith<$Res> {
  __$$ToggleReservationFailureImplCopyWithImpl(
      _$ToggleReservationFailureImpl _value,
      $Res Function(_$ToggleReservationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ToggleReservationFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ToggleReservationFailureImpl implements _ToggleReservationFailure {
  const _$ToggleReservationFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ReservationState.toggleReservationFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToggleReservationFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ToggleReservationFailureImplCopyWith<_$ToggleReservationFailureImpl>
      get copyWith => __$$ToggleReservationFailureImplCopyWithImpl<
          _$ToggleReservationFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return toggleReservationFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return toggleReservationFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (toggleReservationFailure != null) {
      return toggleReservationFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return toggleReservationFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return toggleReservationFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (toggleReservationFailure != null) {
      return toggleReservationFailure(this);
    }
    return orElse();
  }
}

abstract class _ToggleReservationFailure implements ReservationState {
  const factory _ToggleReservationFailure(final String message) =
      _$ToggleReservationFailureImpl;

  String get message;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ToggleReservationFailureImplCopyWith<_$ToggleReservationFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MakeOrderSuccessImplCopyWith<$Res> {
  factory _$$MakeOrderSuccessImplCopyWith(_$MakeOrderSuccessImpl value,
          $Res Function(_$MakeOrderSuccessImpl) then) =
      __$$MakeOrderSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MakeOrderSuccessImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$MakeOrderSuccessImpl>
    implements _$$MakeOrderSuccessImplCopyWith<$Res> {
  __$$MakeOrderSuccessImplCopyWithImpl(_$MakeOrderSuccessImpl _value,
      $Res Function(_$MakeOrderSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MakeOrderSuccessImpl implements _MakeOrderSuccess {
  const _$MakeOrderSuccessImpl();

  @override
  String toString() {
    return 'ReservationState.makeOrderSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MakeOrderSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return makeOrderSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return makeOrderSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (makeOrderSuccess != null) {
      return makeOrderSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return makeOrderSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return makeOrderSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (makeOrderSuccess != null) {
      return makeOrderSuccess(this);
    }
    return orElse();
  }
}

abstract class _MakeOrderSuccess implements ReservationState {
  const factory _MakeOrderSuccess() = _$MakeOrderSuccessImpl;
}

/// @nodoc
abstract class _$$MakeOrderFailureImplCopyWith<$Res> {
  factory _$$MakeOrderFailureImplCopyWith(_$MakeOrderFailureImpl value,
          $Res Function(_$MakeOrderFailureImpl) then) =
      __$$MakeOrderFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$MakeOrderFailureImplCopyWithImpl<$Res>
    extends _$ReservationStateCopyWithImpl<$Res, _$MakeOrderFailureImpl>
    implements _$$MakeOrderFailureImplCopyWith<$Res> {
  __$$MakeOrderFailureImplCopyWithImpl(_$MakeOrderFailureImpl _value,
      $Res Function(_$MakeOrderFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$MakeOrderFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MakeOrderFailureImpl implements _MakeOrderFailure {
  const _$MakeOrderFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ReservationState.makeOrderFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MakeOrderFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MakeOrderFailureImplCopyWith<_$MakeOrderFailureImpl> get copyWith =>
      __$$MakeOrderFailureImplCopyWithImpl<_$MakeOrderFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() reserveRoomSuccess,
    required TResult Function(String message) reserveRoomFailure,
    required TResult Function() getReservationSuccess,
    required TResult Function(String message) getReservationFailure,
    required TResult Function() stopReservationSuccess,
    required TResult Function(String message) stopReservationFailure,
    required TResult Function() endReservationSuccess,
    required TResult Function(String message) endReservationFailure,
    required TResult Function() toggleReservationSuccess,
    required TResult Function(String message) toggleReservationFailure,
    required TResult Function() makeOrderSuccess,
    required TResult Function(String message) makeOrderFailure,
  }) {
    return makeOrderFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? reserveRoomSuccess,
    TResult? Function(String message)? reserveRoomFailure,
    TResult? Function()? getReservationSuccess,
    TResult? Function(String message)? getReservationFailure,
    TResult? Function()? stopReservationSuccess,
    TResult? Function(String message)? stopReservationFailure,
    TResult? Function()? endReservationSuccess,
    TResult? Function(String message)? endReservationFailure,
    TResult? Function()? toggleReservationSuccess,
    TResult? Function(String message)? toggleReservationFailure,
    TResult? Function()? makeOrderSuccess,
    TResult? Function(String message)? makeOrderFailure,
  }) {
    return makeOrderFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? reserveRoomSuccess,
    TResult Function(String message)? reserveRoomFailure,
    TResult Function()? getReservationSuccess,
    TResult Function(String message)? getReservationFailure,
    TResult Function()? stopReservationSuccess,
    TResult Function(String message)? stopReservationFailure,
    TResult Function()? endReservationSuccess,
    TResult Function(String message)? endReservationFailure,
    TResult Function()? toggleReservationSuccess,
    TResult Function(String message)? toggleReservationFailure,
    TResult Function()? makeOrderSuccess,
    TResult Function(String message)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (makeOrderFailure != null) {
      return makeOrderFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_ReserveRoomSuccess value) reserveRoomSuccess,
    required TResult Function(_ReserveRoomFailure value) reserveRoomFailure,
    required TResult Function(_GetReservationSuccess value)
        getReservationSuccess,
    required TResult Function(_GetReservationFailure value)
        getReservationFailure,
    required TResult Function(_StopReservationSuccess value)
        stopReservationSuccess,
    required TResult Function(_StopReservationFailure value)
        stopReservationFailure,
    required TResult Function(_EndReservationSuccess value)
        endReservationSuccess,
    required TResult Function(_EndReservationFailure value)
        endReservationFailure,
    required TResult Function(_ToggleReservationSuccess value)
        toggleReservationSuccess,
    required TResult Function(_ToggleReservationFailure value)
        toggleReservationFailure,
    required TResult Function(_MakeOrderSuccess value) makeOrderSuccess,
    required TResult Function(_MakeOrderFailure value) makeOrderFailure,
  }) {
    return makeOrderFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult? Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult? Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult? Function(_GetReservationFailure value)? getReservationFailure,
    TResult? Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult? Function(_StopReservationFailure value)? stopReservationFailure,
    TResult? Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult? Function(_EndReservationFailure value)? endReservationFailure,
    TResult? Function(_ToggleReservationSuccess value)?
        toggleReservationSuccess,
    TResult? Function(_ToggleReservationFailure value)?
        toggleReservationFailure,
    TResult? Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult? Function(_MakeOrderFailure value)? makeOrderFailure,
  }) {
    return makeOrderFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_ReserveRoomSuccess value)? reserveRoomSuccess,
    TResult Function(_ReserveRoomFailure value)? reserveRoomFailure,
    TResult Function(_GetReservationSuccess value)? getReservationSuccess,
    TResult Function(_GetReservationFailure value)? getReservationFailure,
    TResult Function(_StopReservationSuccess value)? stopReservationSuccess,
    TResult Function(_StopReservationFailure value)? stopReservationFailure,
    TResult Function(_EndReservationSuccess value)? endReservationSuccess,
    TResult Function(_EndReservationFailure value)? endReservationFailure,
    TResult Function(_ToggleReservationSuccess value)? toggleReservationSuccess,
    TResult Function(_ToggleReservationFailure value)? toggleReservationFailure,
    TResult Function(_MakeOrderSuccess value)? makeOrderSuccess,
    TResult Function(_MakeOrderFailure value)? makeOrderFailure,
    required TResult orElse(),
  }) {
    if (makeOrderFailure != null) {
      return makeOrderFailure(this);
    }
    return orElse();
  }
}

abstract class _MakeOrderFailure implements ReservationState {
  const factory _MakeOrderFailure(final String message) =
      _$MakeOrderFailureImpl;

  String get message;

  /// Create a copy of ReservationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MakeOrderFailureImplCopyWith<_$MakeOrderFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
