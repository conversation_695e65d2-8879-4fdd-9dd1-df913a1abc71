import 'package:r2/src/reservation/data/models/invoice_model.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';

mixin ReservationOperations {
  //ReserveRoom
  String? reservationType;
  List<int> additions = [];
  ReservationModel? reservationModel;
  InvoiceModel? invoiceModel;
  void clearReservationParams() {
    reservationType = null;
    additions = [];
  }

  // makeOrder
  int? productId;
  int? quantity;
  String? notes;
  void clearMakeOrderParams() {
    productId = null;
    quantity = null;
    notes = null;
  }
}
