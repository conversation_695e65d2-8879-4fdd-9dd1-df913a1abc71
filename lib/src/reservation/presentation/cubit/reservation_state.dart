part of 'reservation_cubit.dart';

@freezed
class ReservationState with _$ReservationState {
  const factory ReservationState.initial() = _Initial;
  const factory ReservationState.loading() = _Loading;
  const factory ReservationState.reserveRoomSuccess() = _ReserveRoomSuccess;
  const factory ReservationState.reserveRoomFailure(String message) =
      _ReserveRoomFailure;

  const factory ReservationState.getReservationSuccess() =
      _GetReservationSuccess;
  const factory ReservationState.getReservationFailure(String message) =
      _GetReservationFailure;
  const factory ReservationState.stopReservationSuccess() =
      _StopReservationSuccess;
  const factory ReservationState.stopReservationFailure(String message) =
      _StopReservationFailure;
  const factory ReservationState.endReservationSuccess() =
      _EndReservationSuccess;
  const factory ReservationState.endReservationFailure(String message) =
      _EndReservationFailure;
  const factory ReservationState.toggleReservationSuccess() =
      _ToggleReservationSuccess;
  const factory ReservationState.toggleReservationFailure(String message) =
      _ToggleReservationFailure;
  const factory ReservationState.makeOrderSuccess() = _MakeOrderSuccess;
  const factory ReservationState.makeOrderFailure(String message) =
      _MakeOrderFailure;
}
