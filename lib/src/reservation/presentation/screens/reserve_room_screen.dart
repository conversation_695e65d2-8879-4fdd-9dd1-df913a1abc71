import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/reservation/presentation/widgets/reserve_room/reserve_items.dart';
import 'package:r2/src/reservation/presentation/widgets/reserve_room/reserve_room_addition_section.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';

@RoutePage()
class ReserveRoomScreen extends StatefulWidget implements AutoRouteWrapper {
  const ReserveRoomScreen({super.key, this.room});
  final RoomModel? room;

  @override
  State<ReserveRoomScreen> createState() => _ReserveRoomScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<ReservationCubit>(),
        child: this,
      );
}

class _ReserveRoomScreenState extends State<ReserveRoomScreen> {
  late final ToggleCubit<ReservationType> reserveToggleCubit;
  final reservationCubit = injector<ReservationCubit>();
  final roomsCubit = injector<RoomsCubit>();
  @override
  void initState() {
    reserveToggleCubit = ToggleCubit<ReservationType>();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReservationCubit, ReservationState>(
      listener: (context, state) {
        state.whenOrNull(
          reserveRoomSuccess: () async {
            reservationCubit.clearReservationParams();
            await roomsCubit.getRooms();
            UiHelper.onSuccess();
            if (!context.mounted) return;
            context.router
                .replace(RoomDetailsRoute(room: widget.room ?? RoomModel()));
          },
          reserveRoomFailure: (message) {
            UiHelper.onFailure(context, message);
          },
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar:
            defaultAppBar(context: context, title: context.l10n.reserveRoom),
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 24.0),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 24),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          height: 45,
                          width: 250,
                          padding:
                              EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                          decoration: BoxDecoration(
                            color: AppColors.darkPurple,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(22),
                              bottomLeft: Radius.circular(22),
                            ),
                          ),
                          child: Text(
                            widget.room?.name ?? '',
                            style: TextStyles.body16,
                          ),
                        ),
                        Text(
                          '${widget.room?.capacity.toString()} افراد',
                          style: TextStyles.body16,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 14),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Assets.icons.psIcon.svg(),
                            const SizedBox(width: 10),
                            Text(
                              widget.room?.serviceCategory == null
                                  ? widget.room?.service?.name ?? ''
                                  : widget.room?.serviceCategory?.name ?? '',
                              style: TextStyles.body14,
                            ),
                          ],
                        ),
                        Row(
                          children: List.generate(
                            widget.room?.additions?.length ?? 0,
                            (index) => Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 4.0),
                              child: Assets.icons.wifiIcon.svg(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: ReserveRoomAdditionSection(
                      room: widget.room,
                    ),
                  ),
                  Spacer(),
                  BlocProvider(
                    create: (context) => reserveToggleCubit,
                    child: ReserveItems(
                      room: widget.room,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
