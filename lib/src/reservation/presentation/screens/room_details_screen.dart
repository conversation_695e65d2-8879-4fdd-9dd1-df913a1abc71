import 'dart:async';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/bottom_nav_bar/cubit/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:r2/src/reservation/data/models/invoice_model.dart';
import 'package:r2/src/reservation/data/models/reservation_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/reservation/presentation/widgets/room_details/invoice_section.dart';
import 'package:r2/src/reservation/presentation/widgets/room_details/room_actions.dart';
import 'package:r2/src/reservation/presentation/widgets/room_details/stop_time.dart';
import 'package:r2/src/reservation/presentation/widgets/room_details/stop_watch_widget.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';

@RoutePage()
class RoomDetailsScreen extends StatefulWidget implements AutoRouteWrapper {
  const RoomDetailsScreen({super.key, required this.room});
  final RoomModel room;

  @override
  State<RoomDetailsScreen> createState() => _RoomDetailsScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: injector<ReservationCubit>()..getReservation(room.id ?? 0),
        child: this,
      );
}

class _RoomDetailsScreenState extends State<RoomDetailsScreen> {
  ReservationModel? reservation;
  InvoiceModel? invoice;
  final reservationCubit = injector<ReservationCubit>();
  Timer? _timer;
  int _elapsedSeconds = 0;
  bool isRunning = false;
  late DateTime _startTime;

  void _startTimer() {
    _startTime = reservation?.startAt ?? DateTime.now();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds = DateTime.now().difference(_startTime).inSeconds;
      });
    });
  }

  void getElapsedTime() {
    setState(() {
      _startTime = reservation?.startAt ?? DateTime.now();
      _elapsedSeconds =
          reservation?.endAt?.difference(_startTime).inSeconds ?? 0;
    });
  }

  void _startStopwatch() {
    setState(() {
      isRunning = true;
      _elapsedSeconds = 0;
    });
    _startTimer();
  }

  void _stopStopwatch() {
    setState(() {
      isRunning = false;
    });
    _timer?.cancel();
  }

  String _formatTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReservationCubit, ReservationState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () => UiHelper.onLoading(context),
          getReservationSuccess: () async {
            UiHelper.onSuccess();
            reservation = reservationCubit.reservationModel;
            invoice = reservationCubit.invoiceModel;
            if (reservation?.room?.roomStatus?.handle == 'reserved') {
              _startStopwatch();
            } else {
              getElapsedTime();
            }
          },
          getReservationFailure: (message) =>
              UiHelper.onFailure(context, message),
          stopReservationSuccess: () {
            UiHelper.onSuccess();
            _stopStopwatch();
            reservation = reservationCubit.reservationModel;
            invoice = reservationCubit.invoiceModel;
            UiHelper.onSuccess();
          },
          stopReservationFailure: (message) =>
              UiHelper.onFailure(context, message),
          endReservationSuccess: () async {
            UiHelper.onSuccess();
            await injector<RoomsCubit>().getRooms();
            injector<BottomNavBarCubit>().changeIndex(0);
            if (!context.mounted) return;
            context.router.replaceAll([const BottomNavBarRoute()]);
          },
          toggleReservationSuccess: () async {
            UiHelper.onSuccess();
            reservation = reservationCubit.reservationModel;
            invoice = reservationCubit.invoiceModel;
            reservationCubit.clearReservationParams();
            context.router.maybePop();
            await injector<ReservationCubit>()
                .getReservation(reservation?.room?.id ?? 0);
          },
          toggleReservationFailure: (message) =>
              UiHelper.onFailure(context, message),
          makeOrderSuccess: () {
            UiHelper.onSuccess();
            reservationCubit.getReservation(reservation?.room?.id ?? 0);
          },
          makeOrderFailure: (message) => UiHelper.onFailure(context, message),
        );
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar:
            defaultAppBar(context: context, title: context.l10n.roomDetails),
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 250,
                      padding:
                          EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                      decoration: BoxDecoration(
                        color: AppColors.darkPurple,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(22),
                          bottomLeft: Radius.circular(22),
                        ),
                      ),
                      child: Text(
                        reservation?.room?.name ?? '',
                        style: TextStyles.body16,
                      ),
                    ),
                    const SizedBox(height: 60),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          StopWatchWidget(
                            reservation: reservation ?? ReservationModel(),
                            time: _formatTime(_elapsedSeconds),
                          ),
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Assets.icons.psIcon.svg(),
                                  const SizedBox(width: 10),
                                  Text(
                                    reservation?.room?.serviceCategory == null
                                        ? reservation?.room?.service?.name ?? ''
                                        : reservation
                                                ?.room?.serviceCategory?.name ??
                                            '',
                                    style: TextStyles.body14,
                                  ),
                                ],
                              ),
                              Row(
                                children: List.generate(
                                  reservation?.additions?.length ?? 0,
                                  (index) => Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 4.0),
                                    child: Assets.icons.wifiIcon.svg(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 30),
                          if (reservation?.room?.roomStatus?.handle ==
                              'reserved') ...[
                            RoomActions(
                              reservation: reservation ?? ReservationModel(),
                              room: reservation?.room ?? RoomModel(),
                            ),
                            const SizedBox(height: 24),
                          ],
                          InvoiceSection(
                            invoice: invoice ?? InvoiceModel(),
                          ),
                          const SizedBox(height: 50),
                          StopTime(
                            isRunning: isRunning,
                            reservation: reservation,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
