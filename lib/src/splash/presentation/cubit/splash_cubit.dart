import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';
import 'package:r2/src/splash/data/repository/splash_reposetory.dart';

part 'splash_state.dart';
part 'splash_cubit.freezed.dart';

class SplashCubit extends Cubit<SplashState> {
  final SplashReposetory splashReposetory;
  SplashModel? splashModel;
  SplashCubit(this.splashReposetory) : super(SplashState.initial());

  Future<void> getSplash() async {
    emit(SplashState.loading());
    final response = await splashReposetory.getSplash();
    response.when(
        success: (result) {
          emit(SplashState.splashSuccess());
          splashModel = result.data;
        },
        failure: (message) => emit(SplashState.splashFailure(message)));
  }
}
