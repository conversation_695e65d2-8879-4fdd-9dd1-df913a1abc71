import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';

@RoutePage()
class SplashScreen extends StatelessWidget implements AutoRouteWrapper {
  const SplashScreen({super.key});

  @override
  @override
  Widget build(BuildContext context) {
    return BlocListener<SplashCubit, SplashState>(
      listener: (context, state) {
        state.whenOrNull(
          splashSuccess: () {
            injector<AuthCubit>().isUserLoggedIn()
                ? context.router.replace(const BranchesRoute())
                : context.router.replace(const LoginRoute());
          },
          splashFailure: (message) => UiHelper.onFailure(context, message),
        );
      },
      child: Scaffold(
        body: SizedBox(
          width: double.infinity,
          child: Assets.images.splash.image(fit: BoxFit.fill),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
      value: injector<SplashCubit>()..getSplash(),
      child: this,
    );
  }
}
