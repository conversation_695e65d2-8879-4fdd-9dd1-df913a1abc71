// To parse this JSON data, do
//
//     final splashModel = splashModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'splash_model.freezed.dart';
part 'splash_model.g.dart';

SplashModel splashModelFromJson(String str) =>
    SplashModel.fromJson(json.decode(str));

String splashModelToJson(SplashModel data) => json.encode(data.toJson());

@freezed
class SplashModel with _$SplashModel {
  const factory SplashModel({
    @JsonKey(name: "countries") List<CountryModel>? countries,
    @JsonKey(name: "services") List<ServiceModel>? services,
    @JsonKey(name: "additions") List<AdditionModel>? additions,
    @JsonKey(name: "payment") List<PaymentModel>? payment,
  }) = _SplashModel;

  factory SplashModel.fromJson(Map<String, dynamic> json) =>
      _$SplashModelFromJson(json);
}

@freezed
class AdditionModel with _$AdditionModel {
  const factory AdditionModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "handle") String? handle,
    @JsonKey(name: "price") double? price,
  }) = _AdditionModel;

  factory AdditionModel.fromJson(Map<String, dynamic> json) =>
      _$AdditionModelFromJson(json);
}

@freezed
class CountryModel with _$CountryModel {
  const factory CountryModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "handle") String? handle,
    @JsonKey(name: "cities") List<CityModel>? cities,
  }) = _CountryModel;

  factory CountryModel.fromJson(Map<String, dynamic> json) =>
      _$CountryModelFromJson(json);
}

@freezed
class CityModel with _$CityModel {
  const factory CityModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "country_id") int? countryId,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "handle") String? handle,
  }) = _CityModel;

  factory CityModel.fromJson(Map<String, dynamic> json) =>
      _$CityModelFromJson(json);
}

@freezed
class PaymentModel with _$PaymentModel {
  const factory PaymentModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "has_validation") bool? hasValidation,
    @JsonKey(name: "handle") String? handle,
  }) = _PaymentModel;

  factory PaymentModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentModelFromJson(json);
}

@freezed
class ServiceModel with _$ServiceModel {
  const factory ServiceModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "handle") String? handle,
    @JsonKey(name: "categories") List<CategoryModel>? categories,
  }) = _ServiceModel;

  factory ServiceModel.fromJson(Map<String, dynamic> json) =>
      _$ServiceModelFromJson(json);
}

@freezed
class CategoryModel with _$CategoryModel {
  const factory CategoryModel({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "service_id") int? serviceId,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "handle") String? handle,
  }) = _CategoryModel;

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);
}
