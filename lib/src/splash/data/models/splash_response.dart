import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';

part 'splash_response.g.dart';
@JsonSerializable()
class  SplashRespone  extends BaseResponse{
  final SplashModel? data;

  SplashRespone({
    this.data,
    super.message,
    super.errors});

  factory SplashRespone.fromJson(Map<String, dynamic> json) => _$SplashResponeFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$SplashResponeToJson(this);  

}
