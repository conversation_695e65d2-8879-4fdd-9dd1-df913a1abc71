import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/splash/data/api_service/splash_api_service.dart';
import 'package:r2/src/splash/data/models/splash_response.dart';

class SplashReposetory {
  final SplashApiService splashApiService;

  SplashReposetory(this.splashApiService);
  Future<Result<SplashRespone>> getSplash() async =>
      await errorHandlerAsync(() => splashApiService.getSplash());
}
