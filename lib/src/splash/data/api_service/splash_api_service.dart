import 'package:dio/dio.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/splash/data/models/splash_response.dart';
import 'package:retrofit/retrofit.dart';


part 'splash_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class SplashApiService {
  factory SplashApiService(
    Dio dio, {
    String baseUrl,
  }) = _SplashApiService;

  @GET(EndPoints.splash)
  Future<SplashRespone> getSplash();
}
