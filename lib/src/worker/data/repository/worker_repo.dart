import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/worker/data/api_service/worker_api_service.dart';
import 'package:r2/src/worker/data/models/worker_params.dart';
import 'package:r2/src/worker/data/models/worker_response.dart';

class WorkerRepo {
  final WorkerApiService _workerApiService;

  WorkerRepo(this._workerApiService);

  Future<Result<WWorkersResponse>> addWorker(WorkerParams params) async =>
      await errorHandlerAsync(
          () async => await _workerApiService.addWorker(params));
  Future<Result<GetWWorkersResponse>> getWorkers() async =>
      await errorHandlerAsync(() async => await _workerApiService.getWorkers());
  Future<Result<WWorkersResponse>> updateWorker(
          int id, WorkerParams params) async =>
      await errorHandlerAsync(
          () async => await _workerApiService.updateWorker(id, params));
  Future<Result<WWorkersResponse>> deleteWorker(int id) async =>
      await errorHandlerAsync(
          () async => await _workerApiService.deleteWorker(id));
}
