import 'package:dio/dio.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/worker/data/models/worker_params.dart';
import 'package:r2/src/worker/data/models/worker_response.dart';
import 'package:retrofit/retrofit.dart';
part 'worker_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class WorkerApiService {
  factory WorkerApiService(
    Dio dio, {
    String baseUrl,
  }) = _WorkerApiService;
  @GET(EndPoints.workers)
  Future<GetWWorkersResponse> getWorkers();
  @POST(EndPoints.workers)
  Future<WWorkersResponse> addWorker(@Body() WorkerParams params);
  @POST('${EndPoints.workers}/{id}/update')
  Future<WWorkersResponse> updateWorker(
      @Path('id') int id, @Body() WorkerParams params);
  @POST('${EndPoints.workers}/{id}/delete')
  Future<WWorkersResponse> deleteWorker(@Path('id') int id);
}
