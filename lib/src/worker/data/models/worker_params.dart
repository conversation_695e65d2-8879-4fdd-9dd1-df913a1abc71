// To parse this JSON data, do
//
//     final workerParams = workerParamsFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'worker_params.freezed.dart';
part 'worker_params.g.dart';

WorkerParams workerParamsFromJson(String str) => WorkerParams.fromJson(json.decode(str));

String workerParamsToJson(WorkerParams data) => json.encode(data.toJson());

@freezed
class WorkerParams with _$WorkerParams {
    const factory WorkerParams({
        @JsonKey(name: "hall_id")
        required int hallId,
        @<PERSON><PERSON><PERSON><PERSON>(name: "name")
        required String name,
        @J<PERSON><PERSON><PERSON>(name: "dialing_code")
        required String dialingCode,
        @Json<PERSON>ey(name: "phone")
        required String phone,
    }) = _WorkerParams;

    factory WorkerParams.fromJson(Map<String, dynamic> json) => _$WorkerParamsFromJson(json);
}
