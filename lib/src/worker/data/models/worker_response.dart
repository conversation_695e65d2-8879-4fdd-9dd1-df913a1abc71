import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
part 'worker_response.g.dart';
@JsonSerializable()
class GetWWorkersResponse extends BaseResponse {
  final List<UserModel>? data;
  GetWWorkersResponse(
      {this.data, required super.errors, required super.message});
 factory GetWWorkersResponse.fromJson(Map<String, dynamic> json) =>
      _$GetWWorkersResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetWWorkersResponseToJson(this);         
}
@JsonSerializable()
class WWorkersResponse extends BaseResponse {
  final UserModel? data;
  WWorkersResponse(
      {this.data, required super.errors, required super.message});
 factory WWorkersResponse.fromJson(Map<String, dynamic> json) =>
      _$WWorkersResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$WWorkersResponseToJson(this);         
}
