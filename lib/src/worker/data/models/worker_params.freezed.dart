// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'worker_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkerParams _$WorkerParamsFromJson(Map<String, dynamic> json) {
  return _WorkerParams.fromJson(json);
}

/// @nodoc
mixin _$WorkerParams {
  @JsonKey(name: "hall_id")
  int get hallId => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: "dialing_code")
  String get dialingCode => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String get phone => throw _privateConstructorUsedError;

  /// Serializes this WorkerParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkerParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkerParamsCopyWith<WorkerParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkerParamsCopyWith<$Res> {
  factory $WorkerParamsCopyWith(
          WorkerParams value, $Res Function(WorkerParams) then) =
      _$WorkerParamsCopyWithImpl<$Res, WorkerParams>;
  @useResult
  $Res call(
      {@JsonKey(name: "hall_id") int hallId,
      @JsonKey(name: "name") String name,
      @JsonKey(name: "dialing_code") String dialingCode,
      @JsonKey(name: "phone") String phone});
}

/// @nodoc
class _$WorkerParamsCopyWithImpl<$Res, $Val extends WorkerParams>
    implements $WorkerParamsCopyWith<$Res> {
  _$WorkerParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkerParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hallId = null,
    Object? name = null,
    Object? dialingCode = null,
    Object? phone = null,
  }) {
    return _then(_value.copyWith(
      hallId: null == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      dialingCode: null == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkerParamsImplCopyWith<$Res>
    implements $WorkerParamsCopyWith<$Res> {
  factory _$$WorkerParamsImplCopyWith(
          _$WorkerParamsImpl value, $Res Function(_$WorkerParamsImpl) then) =
      __$$WorkerParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "hall_id") int hallId,
      @JsonKey(name: "name") String name,
      @JsonKey(name: "dialing_code") String dialingCode,
      @JsonKey(name: "phone") String phone});
}

/// @nodoc
class __$$WorkerParamsImplCopyWithImpl<$Res>
    extends _$WorkerParamsCopyWithImpl<$Res, _$WorkerParamsImpl>
    implements _$$WorkerParamsImplCopyWith<$Res> {
  __$$WorkerParamsImplCopyWithImpl(
      _$WorkerParamsImpl _value, $Res Function(_$WorkerParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hallId = null,
    Object? name = null,
    Object? dialingCode = null,
    Object? phone = null,
  }) {
    return _then(_$WorkerParamsImpl(
      hallId: null == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      dialingCode: null == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkerParamsImpl implements _WorkerParams {
  const _$WorkerParamsImpl(
      {@JsonKey(name: "hall_id") required this.hallId,
      @JsonKey(name: "name") required this.name,
      @JsonKey(name: "dialing_code") required this.dialingCode,
      @JsonKey(name: "phone") required this.phone});

  factory _$WorkerParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkerParamsImplFromJson(json);

  @override
  @JsonKey(name: "hall_id")
  final int hallId;
  @override
  @JsonKey(name: "name")
  final String name;
  @override
  @JsonKey(name: "dialing_code")
  final String dialingCode;
  @override
  @JsonKey(name: "phone")
  final String phone;

  @override
  String toString() {
    return 'WorkerParams(hallId: $hallId, name: $name, dialingCode: $dialingCode, phone: $phone)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkerParamsImpl &&
            (identical(other.hallId, hallId) || other.hallId == hallId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.dialingCode, dialingCode) ||
                other.dialingCode == dialingCode) &&
            (identical(other.phone, phone) || other.phone == phone));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, hallId, name, dialingCode, phone);

  /// Create a copy of WorkerParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkerParamsImplCopyWith<_$WorkerParamsImpl> get copyWith =>
      __$$WorkerParamsImplCopyWithImpl<_$WorkerParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkerParamsImplToJson(
      this,
    );
  }
}

abstract class _WorkerParams implements WorkerParams {
  const factory _WorkerParams(
          {@JsonKey(name: "hall_id") required final int hallId,
          @JsonKey(name: "name") required final String name,
          @JsonKey(name: "dialing_code") required final String dialingCode,
          @JsonKey(name: "phone") required final String phone}) =
      _$WorkerParamsImpl;

  factory _WorkerParams.fromJson(Map<String, dynamic> json) =
      _$WorkerParamsImpl.fromJson;

  @override
  @JsonKey(name: "hall_id")
  int get hallId;
  @override
  @JsonKey(name: "name")
  String get name;
  @override
  @JsonKey(name: "dialing_code")
  String get dialingCode;
  @override
  @JsonKey(name: "phone")
  String get phone;

  /// Create a copy of WorkerParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkerParamsImplCopyWith<_$WorkerParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
