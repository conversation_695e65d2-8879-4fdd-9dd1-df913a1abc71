// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'worker_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetWWorkersResponse _$GetWWorkersResponseFromJson(Map<String, dynamic> json) =>
    GetWWorkersResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      errors: json['errors'],
      message: json['message'] as String?,
    );

Map<String, dynamic> _$GetWWorkersResponseToJson(
        GetWWorkersResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };

WWorkersResponse _$WWorkersResponseFromJson(Map<String, dynamic> json) =>
    WWorkersResponse(
      data: json['data'] == null
          ? null
          : UserModel.fromJson(json['data'] as Map<String, dynamic>),
      errors: json['errors'],
      message: json['message'] as String?,
    );

Map<String, dynamic> _$WWorkersResponseToJson(WWorkersResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };
