import 'package:auto_route/auto_route.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';

class AddWorker extends StatelessWidget {
  const AddWorker({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.addWorker,
          style: TextStyles.body14,
        ),
        const SizedBox(height: 8),
        DottedBorder(
          color: AppColors.darkPurple,
          strokeWidth: 2,
          dashPattern: [8, 8],
          child: GestureDetector(
            onTap: () => context.router.push( AddWorkerRoute()),
            child: Sized<PERSON><PERSON>(
              height: 100,
              width: double.infinity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: AppColors.primary, width: 3),
                    ),
                    child: Icon(Icons.add, size: 24),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    context.l10n.addWorker,
                    style: TextStyles.body14.copyWith(color: AppColors.grey),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
