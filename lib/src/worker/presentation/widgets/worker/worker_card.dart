import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';

class WorkerCard extends StatelessWidget {
  final UserModel? worker;
  const WorkerCard({super.key, this.worker});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: ExpansionTile(
        childrenPadding: EdgeInsets.symmetric(horizontal: 16),
        expandedCrossAxisAlignment: CrossAxisAlignment.start,
        expandedAlignment: Alignment.centerRight,
        minTileHeight: 80,
        collapsedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        collapsedBackgroundColor: AppColors.darkPurple,
        shape: Border.all(
          color: AppColors.darkPurple,
        ),
        backgroundColor: AppColors.darkPurple,
        leading: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: AppColors.card,
          ),
          child: Assets.icons.personIcon.svg(),
        ),
        title: Text(
          worker?.name ?? '-',
          style: TextStyles.title16,
        ),
        children: [
          Divider(
            height: 1,
            color: AppColors.card,
            thickness: 1,
            endIndent: 16,
            indent: 16,
          ),
          const SizedBox(height: 4),
          ListTile(
            contentPadding: EdgeInsets.zero,
            visualDensity: VisualDensity(horizontal: -4, vertical: -4),
            leading: Assets.icons.phoneIcon.svg(),
            title: Text(
              worker?.phone ?? '-',
              style: TextStyles.body14.copyWith(color: AppColors.grey),
            ),
            trailing: IntrinsicWidth(
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () =>
                          context.router.push(AddWorkerRoute(worker: worker)),
                      child: Assets.icons.editIcon.svg(height: 20)),
                  const SizedBox(width: 20),
                  GestureDetector(
                    onTap: () => UiHelper.showCustomDialog(
                      barrierDismissible: false,
                      context: context,
                      dialog: AlertDialog(
                        backgroundColor: AppColors.darkPurple,
                        icon: Assets.icons.trashIcon2.svg(),
                        title: Text(
                          context.l10n.areYouSureYouWantDelete,
                          style: TextStyles.title16
                              .copyWith(fontWeight: FontWeight.w700),
                        ),
                        titlePadding: EdgeInsets.only(top: 10),
                        actionsPadding: EdgeInsets.only(
                            top: 40, bottom: 24, right: 30, left: 30),
                        iconPadding: EdgeInsets.only(top: 30, bottom: 10),
                        actions: [
                          CustomElevatedButton(
                            width: 250,
                            isLight: false,
                            child: Text(context.l10n.yes),
                            onPressed: ()async{
                              context.router.maybePop();
                              await injector<WorkerCubit>().deleteWorker(worker?.id ?? 0);
                            } ,
                          ),
                          const SizedBox(height: 16),
                          CustomElevatedButton(
                            width: 250,
                            isLight: false,
                            backgroundColor: AppColors.darkPurple,
                            borderColor: AppColors.primary,
                            child: Text(context.l10n.no),
                            onPressed: () => context.router.maybePop(),
                          )
                        ],
                      ),
                    ),
                    child: Assets.icons.trashIcon.svg(height: 20),
                  ),
                ],
              ),
            ),
          ),
          // ListTile(
          //   contentPadding: EdgeInsets.zero,
          //   visualDensity: VisualDensity(horizontal: -4, vertical: -4),
          //   leading: Assets.icons.whatsappIcon.svg(),
          //   title: Text(
          //     '01015620800',
          //     style: TextStyles.body14.copyWith(color: AppColors.grey),
          //   ),
          // ),
          // ListTile(
          //   contentPadding: EdgeInsets.zero,
          //   visualDensity: VisualDensity(horizontal: -4, vertical: -4),
          //   leading: Assets.icons.locationIcon.svg(),
          // title: Text(
          //   worker?.address ?? '-',
          //   style: TextStyles.body14.copyWith(color: AppColors.grey),
          // ),

          // ),
        ],
      ),
    );
  }
}
