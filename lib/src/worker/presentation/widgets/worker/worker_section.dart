import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/add_widget.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';
import 'package:r2/src/worker/presentation/widgets/worker/worker_card.dart';

class WorkerSection extends StatelessWidget {
  const WorkerSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AddWidget(
          title: context.l10n.addWorker,
          onTap: () => context.router.push(
             AddWorkerRoute(),
          ),
        ),
        const SizedBox(height: 16),
        BlocBuilder<WorkerCubit, WorkerState>(
          builder: (context, state) {
            return ListView.separated(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) => WorkerCard(worker: injector<WorkerCubit>().workers![index],),
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemCount: injector<WorkerCubit>().workers?.length ?? 0,
            );
          },
        )
      ],
    );
  }
}
