import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/default_country_code_picker.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';

class AddWorkerForm extends StatefulWidget {
  final UserModel? worker;

  const AddWorkerForm({super.key, this.worker});

  @override
  State<AddWorkerForm> createState() => _AddWorkerFormState();
}

class _AddWorkerFormState extends State<AddWorkerForm> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  void onSubmit() async {
    if (!formKey.currentState!.validate()) return;
    formKey.currentState!.save();
    if (widget.worker?.id != null) {
      await injector<WorkerCubit>().updateWorker(widget.worker?.id ?? 0);
    } else {
      await injector<WorkerCubit>().addWorker();
    }
  }

  @override
  initState() {
    super.initState();
    if (widget.worker?.id != null) {
      initCtrls();
    }
  }

  void initCtrls() {
    injector<WorkerCubit>().nameController?.text = widget.worker?.name ?? '';
    injector<WorkerCubit>().phoneController?.text = widget.worker?.phone ?? '';
  }
  @override
  void dispose() {
    super.dispose();
    injector<WorkerCubit>().disposeContrllers();
  }
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 32.0, right: 16, left: 16),
      child: Form(
        key: formKey,
        child: Column(
          children: [
            LabeledField(
              title: context.l10n.name,
              field: CustomTextField(
                controller: injector<WorkerCubit>().nameController,
                hintText: context.l10n.name,
                validator: (value) {
                  if (value != null && value.isEmpty) {
                    return context.l10n.pleaseEnterYourName;
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 20),
            LabeledField(
              title: context.l10n.phoneNumber,
              field: Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: injector<WorkerCubit>().phoneController,
                      hintText: context.l10n.phoneNumber,
                      validator: (value) {
                        if (value != null && value.isEmpty) {
                          return context.l10n.pleaseEnterValidUserPhone;
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  DefaultCountryCodePicker(
                    onChanged: (country) {
                      injector<WorkerCubit>().daialingCode = country.dialCode;
                    },
                  )
                ],
              ),
            ),
            const SizedBox(height: 20),
            // LabeledField(
            //   title: context.l10n.whatsappNumber,
            //   field: Row(
            //     children: [
            //       Expanded(
            //         child: CustomTextField(
            //           hintText: context.l10n.whatsappNumber,
            //         ),
            //       ),
            //       const SizedBox(width: 16),
            //       DefaultCountryCodePicker()
            //     ],
            //   ),
            // ),
            // const SizedBox(height: 20),
            LabeledField(
              title: context.l10n.branch,
              field: CustomDropDownButton(
                onChanged: (value) {
                  injector<WorkerCubit>().hallId = value;
                },
                onValidate: (value) {
                  if (value == null) {
                    return context.l10n.pleaseSelectBranch;
                  }
                  return null;
                },
                hintText: context.l10n.branch,
                filled: true,
                items: injector<HallsCubit>()
                    .halls!
                    .map(
                      (e) => DropdownMenuItem(
                        value: e.id,
                        child: Text(e.name ?? ''),
                      ),
                    )
                    .toList(),
              ),
            ),
            const SizedBox(height: 20),
            // LabeledField(
            //   title: context.l10n.workerImage,
            //   field: GestureDetector(
            //     onTap: _showImagePickerOptions,
            //     child: DottedBorder(
            //       color: AppColors.darkPurple,
            //       strokeWidth: 2,
            //       dashPattern: [8, 8],
            //       borderType: BorderType.RRect,
            //       radius: const Radius.circular(8),
            //       child: SizedBox(
            //         height: 100,
            //         width: double.infinity,
            //         child: _image == null
            //             ? Center(child: Assets.icons.imagePickerIcon.svg())
            //             : Image.file(
            //                 _image!,
            //                 fit: BoxFit.fitHeight,
            //               ),
            //       ),
            //     ),
            //   ),
            // ),
            const SizedBox(height: 45),
            CustomElevatedButton(
              onPressed: onSubmit,
              child: Text(context.l10n.save),
            )
          ],
        ),
      ),
    );
  }
}
// File? _image;

  // Future<void> _pickImage(ImageSource source) async {
  //   final picker = ImagePicker();
  //   final pickedFile = await picker.pickImage(source: source);

  //   if (pickedFile != null) {
  //     setState(() {
  //       _image = File(pickedFile.path);
  //     });
  //   }
  // }

  // Future<void> _showImagePickerOptions() async {
  //   return showModalBottomSheet<void>(
  //     context: context,
  //     backgroundColor: AppColors.darkPurple,
  //     builder: (BuildContext context) {
  //       return Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: <Widget>[
  //           ListTile(
  //             leading: const Icon(
  //               Icons.photo_library,
  //               color: AppColors.primary,
  //             ),
  //             title: const Text('Choose from Gallery'),
  //             onTap: () {
  //               _pickImage(ImageSource.gallery);
  //               Navigator.pop(context);
  //             },
  //           ),
  //           ListTile(
  //             leading: const Icon(
  //               Icons.photo_camera,
  //               color: AppColors.primary,
  //             ),
  //             title: const Text('Take a Photo'),
  //             onTap: () {
  //               _pickImage(ImageSource.camera);
  //               Navigator.pop(context);
  //             },
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }