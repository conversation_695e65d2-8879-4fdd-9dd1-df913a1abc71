part of 'worker_cubit.dart';

@freezed
class WorkerState with _$WorkerState {
  const factory WorkerState.initial() = _Initial;
  const factory WorkerState.loading() = Loading;
  const factory WorkerState.getWorkersSuccess() = GetWorkersSuccess;
  const factory WorkerState.getWorkersFailure(String message) = GetWorkersFailure;
  const factory WorkerState.addWorkerSuccess() = AddWorkerSuccess;
  const factory WorkerState.addWorkerFailure(String message) = AddWorkerFailure;
  const factory WorkerState.updateWorkerSuccess() = UpdateWorkerSuccess;
  const factory WorkerState.updateWorkerFailure(String message) = UpdateWorkerFailure;
  const factory WorkerState.deleteWorkerSuccess() = DeleteWorkerSuccess;
  const factory WorkerState.deleteWorkerFailure(String message) = DeleteWorkerFailure;

}
