// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'worker_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WorkerState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkerStateCopyWith<$Res> {
  factory $WorkerStateCopyWith(
          WorkerState value, $Res Function(WorkerState) then) =
      _$WorkerStateCopyWithImpl<$Res, WorkerState>;
}

/// @nodoc
class _$WorkerStateCopyWithImpl<$Res, $Val extends WorkerState>
    implements $WorkerStateCopyWith<$Res> {
  _$WorkerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'WorkerState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements WorkerState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'WorkerState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements WorkerState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$GetWorkersSuccessImplCopyWith<$Res> {
  factory _$$GetWorkersSuccessImplCopyWith(_$GetWorkersSuccessImpl value,
          $Res Function(_$GetWorkersSuccessImpl) then) =
      __$$GetWorkersSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetWorkersSuccessImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$GetWorkersSuccessImpl>
    implements _$$GetWorkersSuccessImplCopyWith<$Res> {
  __$$GetWorkersSuccessImplCopyWithImpl(_$GetWorkersSuccessImpl _value,
      $Res Function(_$GetWorkersSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetWorkersSuccessImpl implements GetWorkersSuccess {
  const _$GetWorkersSuccessImpl();

  @override
  String toString() {
    return 'WorkerState.getWorkersSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetWorkersSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return getWorkersSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return getWorkersSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (getWorkersSuccess != null) {
      return getWorkersSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return getWorkersSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return getWorkersSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (getWorkersSuccess != null) {
      return getWorkersSuccess(this);
    }
    return orElse();
  }
}

abstract class GetWorkersSuccess implements WorkerState {
  const factory GetWorkersSuccess() = _$GetWorkersSuccessImpl;
}

/// @nodoc
abstract class _$$GetWorkersFailureImplCopyWith<$Res> {
  factory _$$GetWorkersFailureImplCopyWith(_$GetWorkersFailureImpl value,
          $Res Function(_$GetWorkersFailureImpl) then) =
      __$$GetWorkersFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$GetWorkersFailureImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$GetWorkersFailureImpl>
    implements _$$GetWorkersFailureImplCopyWith<$Res> {
  __$$GetWorkersFailureImplCopyWithImpl(_$GetWorkersFailureImpl _value,
      $Res Function(_$GetWorkersFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$GetWorkersFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetWorkersFailureImpl implements GetWorkersFailure {
  const _$GetWorkersFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'WorkerState.getWorkersFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetWorkersFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetWorkersFailureImplCopyWith<_$GetWorkersFailureImpl> get copyWith =>
      __$$GetWorkersFailureImplCopyWithImpl<_$GetWorkersFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return getWorkersFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return getWorkersFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (getWorkersFailure != null) {
      return getWorkersFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return getWorkersFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return getWorkersFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (getWorkersFailure != null) {
      return getWorkersFailure(this);
    }
    return orElse();
  }
}

abstract class GetWorkersFailure implements WorkerState {
  const factory GetWorkersFailure(final String message) =
      _$GetWorkersFailureImpl;

  String get message;

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetWorkersFailureImplCopyWith<_$GetWorkersFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddWorkerSuccessImplCopyWith<$Res> {
  factory _$$AddWorkerSuccessImplCopyWith(_$AddWorkerSuccessImpl value,
          $Res Function(_$AddWorkerSuccessImpl) then) =
      __$$AddWorkerSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddWorkerSuccessImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$AddWorkerSuccessImpl>
    implements _$$AddWorkerSuccessImplCopyWith<$Res> {
  __$$AddWorkerSuccessImplCopyWithImpl(_$AddWorkerSuccessImpl _value,
      $Res Function(_$AddWorkerSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AddWorkerSuccessImpl implements AddWorkerSuccess {
  const _$AddWorkerSuccessImpl();

  @override
  String toString() {
    return 'WorkerState.addWorkerSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AddWorkerSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return addWorkerSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return addWorkerSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (addWorkerSuccess != null) {
      return addWorkerSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return addWorkerSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return addWorkerSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (addWorkerSuccess != null) {
      return addWorkerSuccess(this);
    }
    return orElse();
  }
}

abstract class AddWorkerSuccess implements WorkerState {
  const factory AddWorkerSuccess() = _$AddWorkerSuccessImpl;
}

/// @nodoc
abstract class _$$AddWorkerFailureImplCopyWith<$Res> {
  factory _$$AddWorkerFailureImplCopyWith(_$AddWorkerFailureImpl value,
          $Res Function(_$AddWorkerFailureImpl) then) =
      __$$AddWorkerFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$AddWorkerFailureImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$AddWorkerFailureImpl>
    implements _$$AddWorkerFailureImplCopyWith<$Res> {
  __$$AddWorkerFailureImplCopyWithImpl(_$AddWorkerFailureImpl _value,
      $Res Function(_$AddWorkerFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$AddWorkerFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddWorkerFailureImpl implements AddWorkerFailure {
  const _$AddWorkerFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'WorkerState.addWorkerFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddWorkerFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddWorkerFailureImplCopyWith<_$AddWorkerFailureImpl> get copyWith =>
      __$$AddWorkerFailureImplCopyWithImpl<_$AddWorkerFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return addWorkerFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return addWorkerFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (addWorkerFailure != null) {
      return addWorkerFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return addWorkerFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return addWorkerFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (addWorkerFailure != null) {
      return addWorkerFailure(this);
    }
    return orElse();
  }
}

abstract class AddWorkerFailure implements WorkerState {
  const factory AddWorkerFailure(final String message) = _$AddWorkerFailureImpl;

  String get message;

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddWorkerFailureImplCopyWith<_$AddWorkerFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateWorkerSuccessImplCopyWith<$Res> {
  factory _$$UpdateWorkerSuccessImplCopyWith(_$UpdateWorkerSuccessImpl value,
          $Res Function(_$UpdateWorkerSuccessImpl) then) =
      __$$UpdateWorkerSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateWorkerSuccessImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$UpdateWorkerSuccessImpl>
    implements _$$UpdateWorkerSuccessImplCopyWith<$Res> {
  __$$UpdateWorkerSuccessImplCopyWithImpl(_$UpdateWorkerSuccessImpl _value,
      $Res Function(_$UpdateWorkerSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateWorkerSuccessImpl implements UpdateWorkerSuccess {
  const _$UpdateWorkerSuccessImpl();

  @override
  String toString() {
    return 'WorkerState.updateWorkerSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateWorkerSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return updateWorkerSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return updateWorkerSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (updateWorkerSuccess != null) {
      return updateWorkerSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return updateWorkerSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return updateWorkerSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (updateWorkerSuccess != null) {
      return updateWorkerSuccess(this);
    }
    return orElse();
  }
}

abstract class UpdateWorkerSuccess implements WorkerState {
  const factory UpdateWorkerSuccess() = _$UpdateWorkerSuccessImpl;
}

/// @nodoc
abstract class _$$UpdateWorkerFailureImplCopyWith<$Res> {
  factory _$$UpdateWorkerFailureImplCopyWith(_$UpdateWorkerFailureImpl value,
          $Res Function(_$UpdateWorkerFailureImpl) then) =
      __$$UpdateWorkerFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UpdateWorkerFailureImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$UpdateWorkerFailureImpl>
    implements _$$UpdateWorkerFailureImplCopyWith<$Res> {
  __$$UpdateWorkerFailureImplCopyWithImpl(_$UpdateWorkerFailureImpl _value,
      $Res Function(_$UpdateWorkerFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$UpdateWorkerFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateWorkerFailureImpl implements UpdateWorkerFailure {
  const _$UpdateWorkerFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'WorkerState.updateWorkerFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateWorkerFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateWorkerFailureImplCopyWith<_$UpdateWorkerFailureImpl> get copyWith =>
      __$$UpdateWorkerFailureImplCopyWithImpl<_$UpdateWorkerFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return updateWorkerFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return updateWorkerFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (updateWorkerFailure != null) {
      return updateWorkerFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return updateWorkerFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return updateWorkerFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (updateWorkerFailure != null) {
      return updateWorkerFailure(this);
    }
    return orElse();
  }
}

abstract class UpdateWorkerFailure implements WorkerState {
  const factory UpdateWorkerFailure(final String message) =
      _$UpdateWorkerFailureImpl;

  String get message;

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateWorkerFailureImplCopyWith<_$UpdateWorkerFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteWorkerSuccessImplCopyWith<$Res> {
  factory _$$DeleteWorkerSuccessImplCopyWith(_$DeleteWorkerSuccessImpl value,
          $Res Function(_$DeleteWorkerSuccessImpl) then) =
      __$$DeleteWorkerSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteWorkerSuccessImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$DeleteWorkerSuccessImpl>
    implements _$$DeleteWorkerSuccessImplCopyWith<$Res> {
  __$$DeleteWorkerSuccessImplCopyWithImpl(_$DeleteWorkerSuccessImpl _value,
      $Res Function(_$DeleteWorkerSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteWorkerSuccessImpl implements DeleteWorkerSuccess {
  const _$DeleteWorkerSuccessImpl();

  @override
  String toString() {
    return 'WorkerState.deleteWorkerSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteWorkerSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return deleteWorkerSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return deleteWorkerSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (deleteWorkerSuccess != null) {
      return deleteWorkerSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return deleteWorkerSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return deleteWorkerSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (deleteWorkerSuccess != null) {
      return deleteWorkerSuccess(this);
    }
    return orElse();
  }
}

abstract class DeleteWorkerSuccess implements WorkerState {
  const factory DeleteWorkerSuccess() = _$DeleteWorkerSuccessImpl;
}

/// @nodoc
abstract class _$$DeleteWorkerFailureImplCopyWith<$Res> {
  factory _$$DeleteWorkerFailureImplCopyWith(_$DeleteWorkerFailureImpl value,
          $Res Function(_$DeleteWorkerFailureImpl) then) =
      __$$DeleteWorkerFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DeleteWorkerFailureImplCopyWithImpl<$Res>
    extends _$WorkerStateCopyWithImpl<$Res, _$DeleteWorkerFailureImpl>
    implements _$$DeleteWorkerFailureImplCopyWith<$Res> {
  __$$DeleteWorkerFailureImplCopyWithImpl(_$DeleteWorkerFailureImpl _value,
      $Res Function(_$DeleteWorkerFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$DeleteWorkerFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteWorkerFailureImpl implements DeleteWorkerFailure {
  const _$DeleteWorkerFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'WorkerState.deleteWorkerFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteWorkerFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteWorkerFailureImplCopyWith<_$DeleteWorkerFailureImpl> get copyWith =>
      __$$DeleteWorkerFailureImplCopyWithImpl<_$DeleteWorkerFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getWorkersSuccess,
    required TResult Function(String message) getWorkersFailure,
    required TResult Function() addWorkerSuccess,
    required TResult Function(String message) addWorkerFailure,
    required TResult Function() updateWorkerSuccess,
    required TResult Function(String message) updateWorkerFailure,
    required TResult Function() deleteWorkerSuccess,
    required TResult Function(String message) deleteWorkerFailure,
  }) {
    return deleteWorkerFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getWorkersSuccess,
    TResult? Function(String message)? getWorkersFailure,
    TResult? Function()? addWorkerSuccess,
    TResult? Function(String message)? addWorkerFailure,
    TResult? Function()? updateWorkerSuccess,
    TResult? Function(String message)? updateWorkerFailure,
    TResult? Function()? deleteWorkerSuccess,
    TResult? Function(String message)? deleteWorkerFailure,
  }) {
    return deleteWorkerFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getWorkersSuccess,
    TResult Function(String message)? getWorkersFailure,
    TResult Function()? addWorkerSuccess,
    TResult Function(String message)? addWorkerFailure,
    TResult Function()? updateWorkerSuccess,
    TResult Function(String message)? updateWorkerFailure,
    TResult Function()? deleteWorkerSuccess,
    TResult Function(String message)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (deleteWorkerFailure != null) {
      return deleteWorkerFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetWorkersSuccess value) getWorkersSuccess,
    required TResult Function(GetWorkersFailure value) getWorkersFailure,
    required TResult Function(AddWorkerSuccess value) addWorkerSuccess,
    required TResult Function(AddWorkerFailure value) addWorkerFailure,
    required TResult Function(UpdateWorkerSuccess value) updateWorkerSuccess,
    required TResult Function(UpdateWorkerFailure value) updateWorkerFailure,
    required TResult Function(DeleteWorkerSuccess value) deleteWorkerSuccess,
    required TResult Function(DeleteWorkerFailure value) deleteWorkerFailure,
  }) {
    return deleteWorkerFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult? Function(GetWorkersFailure value)? getWorkersFailure,
    TResult? Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult? Function(AddWorkerFailure value)? addWorkerFailure,
    TResult? Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult? Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult? Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult? Function(DeleteWorkerFailure value)? deleteWorkerFailure,
  }) {
    return deleteWorkerFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetWorkersSuccess value)? getWorkersSuccess,
    TResult Function(GetWorkersFailure value)? getWorkersFailure,
    TResult Function(AddWorkerSuccess value)? addWorkerSuccess,
    TResult Function(AddWorkerFailure value)? addWorkerFailure,
    TResult Function(UpdateWorkerSuccess value)? updateWorkerSuccess,
    TResult Function(UpdateWorkerFailure value)? updateWorkerFailure,
    TResult Function(DeleteWorkerSuccess value)? deleteWorkerSuccess,
    TResult Function(DeleteWorkerFailure value)? deleteWorkerFailure,
    required TResult orElse(),
  }) {
    if (deleteWorkerFailure != null) {
      return deleteWorkerFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteWorkerFailure implements WorkerState {
  const factory DeleteWorkerFailure(final String message) =
      _$DeleteWorkerFailureImpl;

  String get message;

  /// Create a copy of WorkerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteWorkerFailureImplCopyWith<_$DeleteWorkerFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
