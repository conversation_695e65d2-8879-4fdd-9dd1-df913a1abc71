import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/src/worker/data/repository/worker_repo.dart';
import 'package:r2/src/worker/presentation/cubit/worker_operations.dart';

part 'worker_state.dart';
part 'worker_cubit.freezed.dart';

class WorkerCubit extends Cubit<WorkerState> with WorkersOperations {
  final WorkerRepo workerRepo;
  WorkerCubit(this.workerRepo) : super(WorkerState.initial());

  Future<void> getWorkers() async {
    emit(WorkerState.loading());
    final result = await workerRepo.getWorkers();
    result.when(
        success: (success) {
          emit(WorkerState.getWorkersSuccess());
          workers=success.data ?? [];

        },
        failure: (message) => emit(WorkerState.getWorkersFailure(message)));
  }

  Future<void> addWorker() async {
    emit(WorkerState.loading());
    final result = await workerRepo.addWorker(createParams());
    result.when(
        success: (success) {
          emit(WorkerState.addWorkerSuccess());
        },
        failure: (message) => emit(WorkerState.addWorkerFailure(message)));
  }
  Future<void> updateWorker(int id) async {
    emit(WorkerState.loading());
    final result = await workerRepo.updateWorker(id, createParams());
    result.when(
        success: (success) {
          emit(WorkerState.updateWorkerSuccess());
        },
        failure: (message) => emit(WorkerState.updateWorkerFailure(message)));
  }
  Future<void> deleteWorker(int id) async {
    emit(WorkerState.loading());
    final result = await workerRepo.deleteWorker(id);
    result.when(
        success: (success) {
          emit(WorkerState.deleteWorkerSuccess());
        },
        failure: (message) => emit(WorkerState.deleteWorkerFailure(message)));
  }
}
