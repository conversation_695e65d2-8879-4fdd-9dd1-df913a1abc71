import 'package:flutter/material.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
import 'package:r2/src/worker/data/models/worker_params.dart';

mixin WorkersOperations {
  List<UserModel>? workers;
  TextEditingController? nameController;
  TextEditingController? phoneController;
  String? daialingCode ;
  int? hallId;
  WorkerParams createParams() =>
      WorkerParams(
        name: nameController!.text,
       phone: phoneController!.text,
       dialingCode: daialingCode ??'+20',
       hallId: hallId ?? 0
       );
 void initContrllers(){
  nameController = TextEditingController();
  phoneController = TextEditingController();
 }    
 void disposeContrllers(){
  nameController?.dispose();
  phoneController?.dispose();
 }  
 void clearData(){
  nameController?.clear();
  phoneController?.clear();
  hallId=null;
  daialingCode ='+20';
 }
}
