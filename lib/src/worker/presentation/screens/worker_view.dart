import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';
import 'package:r2/src/worker/presentation/widgets/worker/worker_section.dart';

class WorkerView extends StatefulWidget {
  const WorkerView({super.key});

  @override
  State<WorkerView> createState() => _WorkerViewState();
}

class _WorkerViewState extends State<WorkerView> {
  @override
  initState() {
    super.initState();
    context.read<WorkerCubit>().getWorkers();
    
  }
  @override
  Widget build(BuildContext context) {
    return BlocListener<WorkerCubit, WorkerState>(
      listenWhen: (previous, current) => current.maybeWhen(
        orElse: () => false,
        getWorkersSuccess: () => true,
        getWorkersFailure: (message) => true,
        loading: () => true,
        deleteWorkerFailure: (message) => true,
        deleteWorkerSuccess: () => true,
      ),
      listener: (context, state) {
        state.whenOrNull(
          loading: () => UiHelper.onLoading(context),
          getWorkersSuccess: () => UiHelper.onSuccess(),
          getWorkersFailure: (message) => UiHelper.onFailure(context, message),
          deleteWorkerFailure: (message) => UiHelper.onFailure(context, message),
          deleteWorkerSuccess: () {
            UiHelper.onSuccess();
            context.read<WorkerCubit>().getWorkers();
            },
        );
      },
      child: Scaffold(
        body: DefaultScaffoldGradient(
          child: SingleChildScrollView(
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      trailing: Assets.icons.filterIcon.svg(),
                      leading: Assets.icons.workerIconActive.svg(),
                      title: Text(
                        context.l10n.workers,
                        style: TextStyles.title16.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    WorkerSection()
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
