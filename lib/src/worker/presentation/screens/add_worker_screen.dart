import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';
import 'package:r2/src/worker/presentation/widgets/add_worker/add_worker_form.dart';

@RoutePage()
class AddWorkerScreen extends StatelessWidget implements AutoRouteWrapper {
  final UserModel? worker;
  const AddWorkerScreen({super.key,this.worker});

  @override
  Widget build(BuildContext context) {
    return BlocListener<WorkerCubit, WorkerState>(
       listenWhen: (previous, current) => current.maybeWhen(
        orElse: () => false,
        addWorkerSuccess: () => true,
        addWorkerFailure: (message) => true,
        loading: () => true,
        updateWorkerFailure: (message) => true,
        updateWorkerSuccess: () => true,
      ),
      listener: (context, state) {
        state.whenOrNull(
          loading: () => UiHelper.onLoading(context),
          addWorkerSuccess: () {
            UiHelper.onSuccess();
            context.router.maybePop();
            injector<WorkerCubit>().getWorkers();
          },
          addWorkerFailure:(message)=> UiHelper.onFailure(context,message),
        updateWorkerFailure: (message) => UiHelper.onFailure(context, message),
          updateWorkerSuccess: () {
            UiHelper.onSuccess();
            context.router.maybePop();
            injector<WorkerCubit>().getWorkers();
          },
        );
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: defaultAppBar(context: context, title: context.l10n.addWorker),
        body: DefaultScaffoldGradient(
          child: SingleChildScrollView(
            child: SafeArea(
              child: AddWorkerForm(worker: worker,),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
      value: injector<WorkerCubit>()..initContrllers(),
      child: this,
    );
  }
}
