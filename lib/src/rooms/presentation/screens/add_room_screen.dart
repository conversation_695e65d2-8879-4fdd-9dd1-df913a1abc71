import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/src/bottom_nav_bar/cubit/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';
import 'package:r2/src/rooms/presentation/widgets/add_room/add_room_additions_section.dart';
import 'package:r2/src/rooms/presentation/widgets/add_room/room_games_section.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';

@RoutePage()
class AddRoomScreen extends StatefulWidget implements AutoRouteWrapper {
  const AddRoomScreen({super.key, required this.room});
  final RoomModel? room;

  @override
  State<AddRoomScreen> createState() => _AddRoomScreenState();

  @override
  Widget wrappedRoute(BuildContext context) =>
      BlocProvider.value(value: injector<RoomsCubit>(), child: this);
}

class _AddRoomScreenState extends State<AddRoomScreen> {
  late final ToggleCubit<int> gamesToggleCubit;
  final splashCubit = injector<SplashCubit>();
  final roomsCubit = injector<RoomsCubit>();
  @override
  void initState() {
    roomsCubit.initAddRoomControllers();
    if (widget.room != null) {
      gamesToggleCubit = ToggleCubit<int>(value: widget.room!.serviceId ?? 0);
      roomsCubit.roomNameController.text = widget.room!.name!;
      roomsCubit.roomCapacityController.text = widget.room!.capacity.toString();
      roomsCubit.singlePriceController.text =
          widget.room!.singlePrice.toString();
      roomsCubit.multiPriceController.text = widget.room!.multiPrice.toString();
      roomsCubit.priceController.text = widget.room!.price.toString();
      roomsCubit.notesController.text =
          widget.room!.notes != null ? widget.room!.notes! : '';
      roomsCubit.serviceCategoryId = widget.room!.serviceCategoryId;
      roomsCubit.serviceId = widget.room!.serviceId;
    } else {
      gamesToggleCubit = ToggleCubit<int>(
          value: splashCubit.splashModel?.services?.first.id ?? 0);
    }
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    roomsCubit.disposeAddRoomControllers();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RoomsCubit, RoomsState>(
      listener: (context, state) {
        state.whenOrNull(
          addRoomSuccess: () {
            roomsCubit.clear();
            injector<BottomNavBarCubit>().changeIndex(0);
            context.router.replace(const BottomNavBarRoute());
            UiHelper.onSuccess();
          },
          addRoomFailure: (message) => UiHelper.onFailure(context, message),
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: defaultAppBar(
          context: context,
          title: widget.room == null
              ? context.l10n.addRoom
              : context.l10n.editRoom,
        ),
        extendBodyBehindAppBar: true,
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(
                    top: 30.0, bottom: 40, right: 16, left: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LabeledField(
                      title: context.l10n.roomName,
                      field: CustomTextField(
                        hintText: context.l10n.roomName,
                        initialValue: roomsCubit.roomNameController.text,
                        onChanged: (value) {
                          roomsCubit.roomNameController.text = value!;
                        },
                      ),
                    ),
                    BlocProvider(
                      create: (context) => gamesToggleCubit,
                      child: RoomGamesSection(
                        room: widget.room,
                      ),
                    ),
                    const SizedBox(height: 10),
                    LabeledField(
                      title: context.l10n.pricePerHourForSingle,
                      field: CustomTextField(
                        hintText: context.l10n.pricePerHourForSingle,
                        initialValue: roomsCubit.singlePriceController.text,
                        onChanged: (value) {
                          roomsCubit.singlePriceController.text = value!;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    LabeledField(
                      title: context.l10n.pricePerHourForMalti,
                      field: CustomTextField(
                        hintText: context.l10n.pricePerHourForMalti,
                        initialValue: roomsCubit.multiPriceController.text,
                        onChanged: (value) {
                          roomsCubit.multiPriceController.text = value!;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    LabeledField(
                      title: context.l10n.priceRoomOnly,
                      field: CustomTextField(
                        hintText: context.l10n.priceRoomOnly,
                        initialValue: roomsCubit.priceController.text,
                        onChanged: (value) {
                          roomsCubit.priceController.text = value!;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    LabeledField(
                      title: context.l10n.maximumNumberOfPeopleInTheRoom,
                      field: CustomTextField(
                        hintText: context.l10n.maximumNumberOfPeopleInTheRoom,
                        initialValue: roomsCubit.roomCapacityController.text,
                        onChanged: (value) {
                          roomsCubit.roomCapacityController.text = value!;
                        },
                      ),
                    ),
                    AddRoomAdditionsSection(
                      room: widget.room,
                    ),
                    LabeledField(
                      title: context.l10n.notes,
                      field: CustomTextField(
                        maxLines: 4,
                        hintText: context.l10n.notes,
                        initialValue: roomsCubit.notesController.text,
                        onChanged: (value) {
                          roomsCubit.notesController.text = value!;
                        },
                      ),
                    ),
                    const SizedBox(height: 60),
                    Row(
                      children: [
                        Expanded(
                          child: CustomElevatedButton(
                            child: Text(context.l10n.save),
                            onPressed: () async {
                              if (widget.room != null) {
                                roomsCubit.formKey.currentState!.save();
                                injector<RoomsCubit>().roomId =
                                    widget.room?.id ?? 0;
                                await roomsCubit.updateRoom();
                              } else {
                                roomsCubit.formKey.currentState!.save();
                                await roomsCubit.addRoom();
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        CustomElevatedButton(
                          isLight: false,
                          width: .32.sw,
                          backgroundColor: Colors.transparent,
                          borderColor: AppColors.primary,
                          child: Text(
                            context.l10n.back,
                            style: TextStyle(
                              color: AppColors.primary,
                            ),
                          ),
                          onPressed: () => context.router.maybePop(),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
