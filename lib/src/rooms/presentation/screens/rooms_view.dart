import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/widgets/shared/add_widget.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';
import 'package:r2/src/rooms/presentation/widgets/rooms_view/branch_section.dart';
import 'package:r2/src/rooms/presentation/widgets/rooms_view/rooms_cards_section.dart';

class RoomsView extends StatefulWidget {
  const RoomsView({super.key});

  @override
  State<RoomsView> createState() => _RoomsViewState();
}

class _RoomsViewState extends State<RoomsView> {
  final roomsCubit = injector<RoomsCubit>();

  @override
  void initState() {
    super.initState();
    injector<RoomsCubit>().getRooms();
  }

  @override
  void dispose() {
    super.dispose();
    roomsCubit.clearRooms();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RoomsCubit, RoomsState>(
      listenWhen: (previous, current) => current.maybeWhen(
        getRoomsSuccess: () => true,
        getRoomsFailure: (_) => true,
        deleteRoomSuccess: () => true,
        deleteRoomFailure: (_) => true,
        loading: () => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
          deleteRoomSuccess: () async {
            UiHelper.onSuccess();
            context.router.maybePop();
            await roomsCubit.getRooms();
          },
          deleteRoomFailure: (message) => UiHelper.onFailure(context, message),
          getRoomsSuccess: () => UiHelper.onSuccess(),
          getRoomsFailure: (message) => UiHelper.onFailure(context, message),
          loading: () {
            if (context.router.current.name == BottomNavBarRoute.name) {
              UiHelper.onLoading(context);
            }
          },
        );
      },
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: defaultAppBar(
            context: context,
            title: injector<HallsCubit>().gethallData()?.name ?? '',
          ),
          body: DefaultScaffoldGradient(
            child: SafeArea(
              bottom: true,
              child: NestedScrollView(
                headerSliverBuilder: (context, innerBoxIsScrolled) => [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        children: [
                          const SizedBox(height: 20),
                          BranchSection(),
                          const SizedBox(height: 20),
                          AddWidget(
                            title: context.l10n.addRoom,
                            onTap: () =>
                                context.router.push(AddRoomRoute(room: null)),
                          ),
                          const SizedBox(height: 12),
                        ],
                      ),
                    ),
                  ),
                  SliverPersistentHeader(
                    pinned: true,
                    floating: false,
                    delegate: _SliverAppBarDelegate(
                      TabBar(
                        indicatorColor: AppColors.primary,
                        labelColor: AppColors.primary,
                        indicatorSize: TabBarIndicatorSize.tab,
                        tabs: [
                          Tab(
                            text: context.l10n.unBookedRooms,
                          ),
                          Tab(
                            text: context.l10n.bookedRooms,
                          ),
                        ],
                      ),
                    ),
                  )
                ],
                body: TabBarView(
                  children: [
                    BlocBuilder<RoomsCubit, RoomsState>(
                      buildWhen: (previous, current) => current.maybeWhen(
                        getRoomsSuccess: () => true,
                        orElse: () => false,
                      ),
                      builder: (context, state) {
                        return RoomsCardsSection(
                          isReserve: false,
                          rooms: roomsCubit.availableRooms,
                        );
                      },
                    ),
                    BlocBuilder<RoomsCubit, RoomsState>(
                      buildWhen: (previous, current) => current.maybeWhen(
                        getRoomsSuccess: () => true,
                        orElse: () => false,
                      ),
                      builder: (context, state) {
                        return RoomsCardsSection(
                          isReserve: true,
                          rooms: roomsCubit.reservedRooms,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _SliverAppBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Color(0xff241f3d),
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
