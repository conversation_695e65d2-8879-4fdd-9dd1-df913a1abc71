import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';
import 'package:toggle_switch/toggle_switch.dart';

class RoomGamesSection extends StatefulWidget {
  const RoomGamesSection({super.key, this.room});
  final RoomModel? room;

  @override
  State<RoomGamesSection> createState() => _RoomGamesSectionState();
}

class _RoomGamesSectionState extends State<RoomGamesSection> {
  final splashCubit = injector<SplashCubit>();
  final roomsCubit = injector<RoomsCubit>();
  @override
  initState() {
    super.initState();
    if (widget.room == null) {
      roomsCubit.serviceId = splashCubit.splashModel?.services?[0].id ?? 0;
      if (splashCubit.splashModel?.services?[0].categories?.isNotEmpty ??
          false) {
        roomsCubit.serviceCategoryId =
            splashCubit.splashModel?.services?[0].categories?[0].id ?? 0;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final gamesToggleCubit = context.watch<ToggleCubit<int>>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          context.l10n.gamesInRoom,
          style: TextStyles.body14,
        ),
        const SizedBox(height: 16),
        ...List.generate(
          splashCubit.splashModel?.services?.length ?? 0,
          (index) {
            return Column(
              children: [
                RadioListTile<int>(
                  secondary: Text(
                      splashCubit.splashModel?.services?[index].name ?? '',
                      style: TextStyles.body14),
                  contentPadding: EdgeInsets.zero,
                  value: splashCubit.splashModel?.services?[index].id ?? 0,
                  groupValue: gamesToggleCubit.value,
                  onChanged: (value) {
                    gamesToggleCubit.toggle(value!);
                    roomsCubit.serviceId =
                        splashCubit.splashModel?.services?[index].id ?? 0;
                    if (splashCubit.splashModel?.services?[index].categories
                            ?.isEmpty ??
                        false) {
                      roomsCubit.serviceCategoryId = null;
                    }
                  },
                  title: Row(
                    children: [
                      Assets.icons.psIcon.svg(),
                      const SizedBox(width: 16),
                      Text(
                          splashCubit.splashModel?.services?[index].name ?? ''),
                    ],
                  ),
                  activeColor: AppColors.primary,
                  fillColor: WidgetStateProperty.all(AppColors.primary),
                  hoverColor: AppColors.primary,
                ),
                if (gamesToggleCubit.value ==
                        splashCubit.splashModel?.services?[index].id &&
                    (splashCubit.splashModel?.services?[index].categories
                            ?.isNotEmpty ??
                        false)) ...[
                  ToggleSwitch(
                    initialLabelIndex: widget.room != null
                        ? (splashCubit.splashModel?.services?[index].categories
                                ?.indexWhere((element) =>
                                    element.id ==
                                    widget.room?.serviceCategoryId) ??
                            0)
                        : 0,
                    minHeight: 40,
                    minWidth: 150,
                    radiusStyle: true,
                    curve: Curves.easeIn,
                    totalSwitches: splashCubit
                            .splashModel?.services?[index].categories?.length ??
                        0,
                    borderWidth: 1,
                    cornerRadius: 20,
                    borderColor: [AppColors.primary],
                    dividerColor: AppColors.primary,
                    animate: true,
                    labels: splashCubit.splashModel?.services?[index].categories
                            ?.map((e) => e.name ?? '')
                            .toList() ??
                        [],
                    activeBgColor: [AppColors.primary],
                    onToggle: (value) {
                      roomsCubit.serviceCategoryId = splashCubit.splashModel
                              ?.services?[index].categories?[value!].id ??
                          0;
                    },
                    fontSize: 14,
                  ),
                  SizedBox(height: 5),
                  Divider(
                    thickness: 1,
                    color: AppColors.card,
                  ),
                ],
              ],
            );
          },
        ),
      ],
    );
  }
}
