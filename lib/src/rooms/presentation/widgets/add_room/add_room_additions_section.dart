import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';

class AddRoomAdditionsSection extends StatefulWidget {
  const AddRoomAdditionsSection({super.key, this.room});
  final RoomModel? room;

  @override
  State<AddRoomAdditionsSection> createState() =>
      _AddRoomAdditionsSectionState();
}

class _AddRoomAdditionsSectionState extends State<AddRoomAdditionsSection> {
  final splashCubit = injector<SplashCubit>();
  final roomsCubit = injector<RoomsCubit>();

  @override
  Widget build(BuildContext context) {
    return Form(
      key: roomsCubit.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Text(context.l10n.additions, style: TextStyles.body14),
          ...List.generate(splashCubit.splashModel?.additions?.length ?? 0,
              (index) {
            return Padding(
              padding: const EdgeInsets.only(top: 6.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: AdditionItem(
                      addition: splashCubit.splashModel?.additions?[index],
                      room: widget.room,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}

class AdditionItem extends StatefulWidget {
  const AdditionItem({super.key, this.addition, this.room});
  final AdditionModel? addition;
  final RoomModel? room;

  @override
  State<AdditionItem> createState() => _AdditionItemState();
}

class _AdditionItemState extends State<AdditionItem> {
  final splashCubit = injector<SplashCubit>();
  final roomsCubit = injector<RoomsCubit>();
  bool isSelected = false;
  AdditionModel? addition;

  @override
  void initState() {
    if (widget.room != null) {
      addition = widget.room?.additions?.firstWhere(
        (element) => element.id == widget.addition?.id,
        orElse: () => AdditionModel(),
      );
      if (addition != null && addition!.id != null) {
        isSelected = true;
        roomsCubit.additions.add(addition!);
      }
    }
    super.initState();
  }

  @override
  void dispose() {
    roomsCubit.additions.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 230,
          child: CheckboxListTile(
            visualDensity: VisualDensity(horizontal: -4, vertical: -1),
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
            fillColor: WidgetStatePropertyAll(AppColors.card),
            checkColor: AppColors.primary,
            activeColor: AppColors.primary,
            side: BorderSide(
              color: AppColors.primary,
              width: 1,
            ),
            checkboxShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
              side: BorderSide(
                color: AppColors.primary,
                width: 1,
              ),
            ),
            value: isSelected,
            onChanged: (value) {
              setState(() {
                isSelected = value ?? false;
                if (isSelected) {
                  roomsCubit.additions.add(
                    AdditionModel(
                      id: widget.addition?.id ?? 0,
                    ),
                  );
                }
                if (!isSelected) {
                  roomsCubit.additions.removeWhere(
                      (element) => element.id == widget.addition?.id);
                }
              });
            },
            title: Row(
              children: [
                Assets.icons.wifiIcon.svg(),
                const SizedBox(width: 10),
                Text(
                  widget.addition?.name ?? '',
                  style: TextStyles.body14,
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: CustomTextField(
            hintText: context.l10n.pricePerHour,
            initialValue: addition?.price?.toString() ?? '0',
            keyboardType: TextInputType.number,
            onSaved: (value) {
              if (roomsCubit.additions.isNotEmpty && value != null) {
                final addedAdditionIndex = roomsCubit.additions.indexWhere(
                  (element) => element.id == widget.addition?.id,
                );
                if (addedAdditionIndex != -1) {
                  AdditionModel addedAddition =
                      roomsCubit.additions[addedAdditionIndex];
                  addedAddition =
                      addedAddition.copyWith(price: double.tryParse(value));
                  roomsCubit.additions[addedAdditionIndex] = addedAddition;
                }
              }
            },
          ),
        ),
      ],
    );
  }
}
