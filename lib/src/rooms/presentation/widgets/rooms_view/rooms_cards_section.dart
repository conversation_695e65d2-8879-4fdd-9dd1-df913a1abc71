import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/widgets/rooms_view/room_card.dart';

class RoomsCardsSection extends StatelessWidget {
  const RoomsCardsSection({
    super.key,
    required this.isReserve,
    required this.rooms,
  });
  final bool isReserve;
  final List<RoomModel> rooms;

  @override
  Widget build(BuildContext context) {
    return rooms.isEmpty
        ? Center(
            child: Text(
              isReserve
                  ? context.l10n.noRoomsReservedNow
                  : context.l10n.noRooms,
              style: TextStyles.headline24.copyWith(
                  color: AppColors.primary, fontWeight: FontWeight.w700),
            ),
          )
        : ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            itemBuilder: (context, index) => RoomCard(
              room: rooms[index],
              isReserve: isReserve,
            ),
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemCount: rooms.length,
          );
  }
}
