import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';

class BranchSection extends StatelessWidget {
  const BranchSection({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        UiHelper.showCustomDialog(
          context: context,
          dialog: AlertDialog(
            contentPadding:
                EdgeInsets.only(top: 30, right: 24, left: 32, bottom: 24),
            backgroundColor: AppColors.darkPurple,
            icon: Column(
              children: [
                Align(
                  alignment: AlignmentDirectional.centerEnd,
                  child: GestureDetector(
                    onTap: () => context.router.maybePop(),
                    child: Assets.icons.minSizeIcon.svg(),
                  ),
                ),
                const SizedBox(height: 3),
                Container(
                  padding: EdgeInsets.all(30),
                  decoration: BoxDecoration(
                    color: AppColors.card,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: injector<HallsCubit>().gethallData()?.image != null
                      ? SizedBox(
                          height: 100,
                          child: Image.network(
                              (injector<HallsCubit>().gethallData()!.image!)),
                        )
                      : Assets.icons.psConttrollerIcon.svg(),
                ),
                const SizedBox(height: 5),
                Text(
                  injector<HallsCubit>().gethallData()?.name ?? '',
                  style: TextStyles.title16,
                ),
              ],
            ),
            content: IntrinsicHeight(
              child: Column(
                children: [
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    leading: Assets.icons.phoneIcon.svg(),
                    title: Text(
                      injector<HallsCubit>().gethallData()?.phone ?? '',
                      style: TextStyles.body14,
                    ),
                  ),
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    leading: Assets.icons.whatsappIcon.svg(),
                    title: Text(
                      injector<HallsCubit>().gethallData()?.whatsapp ?? '',
                      style: TextStyles.body14,
                    ),
                  ),
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    visualDensity: VisualDensity(horizontal: -4, vertical: -4),
                    leading: Assets.icons.locationIcon.svg(),
                    title: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        injector<HallsCubit>().gethallData()?.address ?? '',
                        style: TextStyles.body14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () => context.router.push(
                      AddBranchRoute(
                        hall: injector<HallsCubit>().gethallData(),
                      ),
                    ),
                    child: Assets.icons.editIcon.svg(
                      height: 30,
                    ),
                  ),
                  const SizedBox(width: 50),
                  GestureDetector(
                    onTap: () => UiHelper.showCustomDialog(
                      barrierDismissible: false,
                      context: context,
                      dialog: AlertDialog(
                        backgroundColor: AppColors.darkPurple,
                        icon: Assets.icons.trashIcon2.svg(),
                        title: Text(
                          context.l10n.confirmDelete,
                          style: TextStyles.title16
                              .copyWith(fontWeight: FontWeight.w700),
                        ),
                        titlePadding: EdgeInsets.only(top: 10),
                        actionsPadding: EdgeInsets.only(
                            top: 40, bottom: 24, right: 30, left: 30),
                        iconPadding: EdgeInsets.only(top: 30, bottom: 10),
                        actions: [
                          CustomElevatedButton(
                            width: 250,
                            isLight: false,
                            child: Text(context.l10n.yes),
                            onPressed: () => injector<HallsCubit>().deleteHall(
                                injector<HallsCubit>().gethallData()!.id
                                    as int),
                          ),
                          const SizedBox(height: 16),
                          CustomElevatedButton(
                            width: 250,
                            isLight: false,
                            backgroundColor: AppColors.darkPurple,
                            borderColor: AppColors.primary,
                            child: Text(context.l10n.no),
                            onPressed: () => context.router.maybePop(),
                          ),
                        ],
                      ),
                    ),
                    child: GestureDetector(
                      onTap: () async {
                        context.router.replaceAll([BranchesRoute()]);
                        await injector<HallsCubit>().deleteHall(
                            injector<HallsCubit>().gethallData()!.id as int);
                      },
                      child: Assets.icons.trashIcon.svg(height: 30),
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(top: 4, bottom: 4, right: 4, left: 16),
        height: 65,
        decoration: BoxDecoration(
          color: AppColors.darkPurple,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: AppColors.card,
                  ),
                  child: injector<HallsCubit>().gethallData()?.image != null
                      ? Image.network(
                          (injector<HallsCubit>().gethallData()!.image!))
                      : Assets.icons.psConttrollerIcon.svg(),
                ),
                const SizedBox(width: 10),
                Text(
                  injector<HallsCubit>().gethallData()?.name ?? '',
                  style: TextStyles.title16,
                ),
              ],
            ),
            Assets.icons.maxSizeIcon.svg(),
          ],
        ),
      ),
    );
  }
}
