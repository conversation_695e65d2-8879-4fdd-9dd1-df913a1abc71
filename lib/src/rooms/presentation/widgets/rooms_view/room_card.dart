import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';

class RoomCard extends StatelessWidget {
  const RoomCard({
    required this.isReserve,
    super.key,
    required this.room,
  });
  final bool isReserve;
  final RoomModel room;

  @override
  Widget build(BuildContext context) {
    final roomsCubit = injector<RoomsCubit>();
    return GestureDetector(
      onTap: () {
        room.roomStatus?.handle == 'available'
            ? context.router.push(ReserveRoomRoute(room: room))
            : context.router.push(RoomDetailsRoute(room: room));
      },
      child: Container(
        padding: EdgeInsets.only(
          top: 16,
          bottom: isReserve ? 0 : 8,
        ),
        decoration: BoxDecoration(
          color: AppColors.darkPurple,
          borderRadius: BorderRadius.circular(16),
          border: isReserve ? Border.all(color: AppColors.primary) : null,
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 45,
                    width: 250,
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.card,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        bottomLeft: Radius.circular(12),
                      ),
                    ),
                    child: Text(
                      room.name ?? '',
                      style: TextStyles.body16,
                    ),
                  ),
                  if (!isReserve)
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () =>
                              context.router.push(AddRoomRoute(room: room)),
                          child: Assets.icons.editIcon.svg(),
                        ),
                        const SizedBox(width: 20),
                        GestureDetector(
                          onTap: () => UiHelper.showCustomDialog(
                            barrierDismissible: false,
                            context: context,
                            dialog: AlertDialog(
                              backgroundColor: AppColors.darkPurple,
                              icon: Assets.icons.trashIcon2.svg(),
                              title: Text(
                                context.l10n.areYouSureYouWantDelete,
                                style: TextStyles.title16
                                    .copyWith(fontWeight: FontWeight.w700),
                              ),
                              titlePadding: EdgeInsets.only(top: 10),
                              actionsPadding: EdgeInsets.only(
                                  top: 40, bottom: 24, right: 30, left: 30),
                              iconPadding: EdgeInsets.only(top: 30, bottom: 10),
                              actions: [
                                CustomElevatedButton(
                                  width: 250,
                                  isLight: false,
                                  child: Text(context.l10n.yes),
                                  onPressed: () async {
                                    await roomsCubit.deleteRoom(room.id!);
                                  },
                                ),
                                const SizedBox(height: 16),
                                CustomElevatedButton(
                                  width: 250,
                                  isLight: false,
                                  backgroundColor: AppColors.darkPurple,
                                  borderColor: AppColors.primary,
                                  child: Text(context.l10n.no),
                                  onPressed: () => context.router.maybePop(),
                                )
                              ],
                            ),
                          ),
                          child: Assets.icons.trashIcon.svg(height: 20),
                        ),
                      ],
                    )
                ],
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Assets.icons.psIcon.svg(),
                      const SizedBox(width: 10),
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                            room.serviceCategory == null
                                ? room.service?.name ?? ''
                                : room.serviceCategory?.name ?? '',
                            style: TextStyles.body14),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        '${room.capacity} افراد',
                        style: TextStyles.body14,
                      ),
                    ],
                  ),
                  Row(
                    children: List.generate(
                      room.additions?.length ?? 0,
                      (index) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: Assets.icons.wifiIcon.svg(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            if (isReserve) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: Center(
                  child: Text(
                    context.l10n.bookingMadeThroughTheHall,
                    style: TextStyles.body12.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              )
            ],
          ],
        ),
      ),
    );
  }
}
