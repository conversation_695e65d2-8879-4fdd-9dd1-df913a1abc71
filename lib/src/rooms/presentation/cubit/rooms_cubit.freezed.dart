// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rooms_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RoomsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomsStateCopyWith<$Res> {
  factory $RoomsStateCopyWith(
          RoomsState value, $Res Function(RoomsState) then) =
      _$RoomsStateCopyWithImpl<$Res, RoomsState>;
}

/// @nodoc
class _$RoomsStateCopyWithImpl<$Res, $Val extends RoomsState>
    implements $RoomsStateCopyWith<$Res> {
  _$RoomsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl with DiagnosticableTreeMixin implements _Initial {
  const _$InitialImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.initial()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'RoomsState.initial'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements RoomsState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl with DiagnosticableTreeMixin implements Loading {
  const _$LoadingImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.loading()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'RoomsState.loading'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements RoomsState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$GetRoomsSuccessImplCopyWith<$Res> {
  factory _$$GetRoomsSuccessImplCopyWith(_$GetRoomsSuccessImpl value,
          $Res Function(_$GetRoomsSuccessImpl) then) =
      __$$GetRoomsSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetRoomsSuccessImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$GetRoomsSuccessImpl>
    implements _$$GetRoomsSuccessImplCopyWith<$Res> {
  __$$GetRoomsSuccessImplCopyWithImpl(
      _$GetRoomsSuccessImpl _value, $Res Function(_$GetRoomsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetRoomsSuccessImpl
    with DiagnosticableTreeMixin
    implements GetRoomsSuccess {
  const _$GetRoomsSuccessImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.getRoomsSuccess()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'RoomsState.getRoomsSuccess'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetRoomsSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return getRoomsSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return getRoomsSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (getRoomsSuccess != null) {
      return getRoomsSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return getRoomsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return getRoomsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (getRoomsSuccess != null) {
      return getRoomsSuccess(this);
    }
    return orElse();
  }
}

abstract class GetRoomsSuccess implements RoomsState {
  const factory GetRoomsSuccess() = _$GetRoomsSuccessImpl;
}

/// @nodoc
abstract class _$$GetRoomsFailureImplCopyWith<$Res> {
  factory _$$GetRoomsFailureImplCopyWith(_$GetRoomsFailureImpl value,
          $Res Function(_$GetRoomsFailureImpl) then) =
      __$$GetRoomsFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$GetRoomsFailureImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$GetRoomsFailureImpl>
    implements _$$GetRoomsFailureImplCopyWith<$Res> {
  __$$GetRoomsFailureImplCopyWithImpl(
      _$GetRoomsFailureImpl _value, $Res Function(_$GetRoomsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$GetRoomsFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetRoomsFailureImpl
    with DiagnosticableTreeMixin
    implements GetRoomsFailure {
  const _$GetRoomsFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.getRoomsFailure(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RoomsState.getRoomsFailure'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetRoomsFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetRoomsFailureImplCopyWith<_$GetRoomsFailureImpl> get copyWith =>
      __$$GetRoomsFailureImplCopyWithImpl<_$GetRoomsFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return getRoomsFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return getRoomsFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (getRoomsFailure != null) {
      return getRoomsFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return getRoomsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return getRoomsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (getRoomsFailure != null) {
      return getRoomsFailure(this);
    }
    return orElse();
  }
}

abstract class GetRoomsFailure implements RoomsState {
  const factory GetRoomsFailure(final String message) = _$GetRoomsFailureImpl;

  String get message;

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetRoomsFailureImplCopyWith<_$GetRoomsFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddRoomSuccessImplCopyWith<$Res> {
  factory _$$AddRoomSuccessImplCopyWith(_$AddRoomSuccessImpl value,
          $Res Function(_$AddRoomSuccessImpl) then) =
      __$$AddRoomSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddRoomSuccessImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$AddRoomSuccessImpl>
    implements _$$AddRoomSuccessImplCopyWith<$Res> {
  __$$AddRoomSuccessImplCopyWithImpl(
      _$AddRoomSuccessImpl _value, $Res Function(_$AddRoomSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AddRoomSuccessImpl
    with DiagnosticableTreeMixin
    implements AddRoomSuccess {
  const _$AddRoomSuccessImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.addRoomSuccess()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'RoomsState.addRoomSuccess'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AddRoomSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return addRoomSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return addRoomSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (addRoomSuccess != null) {
      return addRoomSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return addRoomSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return addRoomSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (addRoomSuccess != null) {
      return addRoomSuccess(this);
    }
    return orElse();
  }
}

abstract class AddRoomSuccess implements RoomsState {
  const factory AddRoomSuccess() = _$AddRoomSuccessImpl;
}

/// @nodoc
abstract class _$$AddRoomFailureImplCopyWith<$Res> {
  factory _$$AddRoomFailureImplCopyWith(_$AddRoomFailureImpl value,
          $Res Function(_$AddRoomFailureImpl) then) =
      __$$AddRoomFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$AddRoomFailureImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$AddRoomFailureImpl>
    implements _$$AddRoomFailureImplCopyWith<$Res> {
  __$$AddRoomFailureImplCopyWithImpl(
      _$AddRoomFailureImpl _value, $Res Function(_$AddRoomFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$AddRoomFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddRoomFailureImpl
    with DiagnosticableTreeMixin
    implements AddRoomFailure {
  const _$AddRoomFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.addRoomFailure(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RoomsState.addRoomFailure'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddRoomFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddRoomFailureImplCopyWith<_$AddRoomFailureImpl> get copyWith =>
      __$$AddRoomFailureImplCopyWithImpl<_$AddRoomFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return addRoomFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return addRoomFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (addRoomFailure != null) {
      return addRoomFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return addRoomFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return addRoomFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (addRoomFailure != null) {
      return addRoomFailure(this);
    }
    return orElse();
  }
}

abstract class AddRoomFailure implements RoomsState {
  const factory AddRoomFailure(final String message) = _$AddRoomFailureImpl;

  String get message;

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddRoomFailureImplCopyWith<_$AddRoomFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteRoomSuccessImplCopyWith<$Res> {
  factory _$$DeleteRoomSuccessImplCopyWith(_$DeleteRoomSuccessImpl value,
          $Res Function(_$DeleteRoomSuccessImpl) then) =
      __$$DeleteRoomSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteRoomSuccessImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$DeleteRoomSuccessImpl>
    implements _$$DeleteRoomSuccessImplCopyWith<$Res> {
  __$$DeleteRoomSuccessImplCopyWithImpl(_$DeleteRoomSuccessImpl _value,
      $Res Function(_$DeleteRoomSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteRoomSuccessImpl
    with DiagnosticableTreeMixin
    implements DeleteRoomSuccess {
  const _$DeleteRoomSuccessImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.deleteRoomSuccess()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'RoomsState.deleteRoomSuccess'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteRoomSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return deleteRoomSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return deleteRoomSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (deleteRoomSuccess != null) {
      return deleteRoomSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return deleteRoomSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return deleteRoomSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (deleteRoomSuccess != null) {
      return deleteRoomSuccess(this);
    }
    return orElse();
  }
}

abstract class DeleteRoomSuccess implements RoomsState {
  const factory DeleteRoomSuccess() = _$DeleteRoomSuccessImpl;
}

/// @nodoc
abstract class _$$DeleteRoomFailureImplCopyWith<$Res> {
  factory _$$DeleteRoomFailureImplCopyWith(_$DeleteRoomFailureImpl value,
          $Res Function(_$DeleteRoomFailureImpl) then) =
      __$$DeleteRoomFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DeleteRoomFailureImplCopyWithImpl<$Res>
    extends _$RoomsStateCopyWithImpl<$Res, _$DeleteRoomFailureImpl>
    implements _$$DeleteRoomFailureImplCopyWith<$Res> {
  __$$DeleteRoomFailureImplCopyWithImpl(_$DeleteRoomFailureImpl _value,
      $Res Function(_$DeleteRoomFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$DeleteRoomFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteRoomFailureImpl
    with DiagnosticableTreeMixin
    implements DeleteRoomFailure {
  const _$DeleteRoomFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'RoomsState.deleteRoomFailure(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'RoomsState.deleteRoomFailure'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteRoomFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteRoomFailureImplCopyWith<_$DeleteRoomFailureImpl> get copyWith =>
      __$$DeleteRoomFailureImplCopyWithImpl<_$DeleteRoomFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() getRoomsSuccess,
    required TResult Function(String message) getRoomsFailure,
    required TResult Function() addRoomSuccess,
    required TResult Function(String message) addRoomFailure,
    required TResult Function() deleteRoomSuccess,
    required TResult Function(String message) deleteRoomFailure,
  }) {
    return deleteRoomFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? getRoomsSuccess,
    TResult? Function(String message)? getRoomsFailure,
    TResult? Function()? addRoomSuccess,
    TResult? Function(String message)? addRoomFailure,
    TResult? Function()? deleteRoomSuccess,
    TResult? Function(String message)? deleteRoomFailure,
  }) {
    return deleteRoomFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? getRoomsSuccess,
    TResult Function(String message)? getRoomsFailure,
    TResult Function()? addRoomSuccess,
    TResult Function(String message)? addRoomFailure,
    TResult Function()? deleteRoomSuccess,
    TResult Function(String message)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (deleteRoomFailure != null) {
      return deleteRoomFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(GetRoomsSuccess value) getRoomsSuccess,
    required TResult Function(GetRoomsFailure value) getRoomsFailure,
    required TResult Function(AddRoomSuccess value) addRoomSuccess,
    required TResult Function(AddRoomFailure value) addRoomFailure,
    required TResult Function(DeleteRoomSuccess value) deleteRoomSuccess,
    required TResult Function(DeleteRoomFailure value) deleteRoomFailure,
  }) {
    return deleteRoomFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult? Function(GetRoomsFailure value)? getRoomsFailure,
    TResult? Function(AddRoomSuccess value)? addRoomSuccess,
    TResult? Function(AddRoomFailure value)? addRoomFailure,
    TResult? Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult? Function(DeleteRoomFailure value)? deleteRoomFailure,
  }) {
    return deleteRoomFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(GetRoomsSuccess value)? getRoomsSuccess,
    TResult Function(GetRoomsFailure value)? getRoomsFailure,
    TResult Function(AddRoomSuccess value)? addRoomSuccess,
    TResult Function(AddRoomFailure value)? addRoomFailure,
    TResult Function(DeleteRoomSuccess value)? deleteRoomSuccess,
    TResult Function(DeleteRoomFailure value)? deleteRoomFailure,
    required TResult orElse(),
  }) {
    if (deleteRoomFailure != null) {
      return deleteRoomFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteRoomFailure implements RoomsState {
  const factory DeleteRoomFailure(final String message) =
      _$DeleteRoomFailureImpl;

  String get message;

  /// Create a copy of RoomsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteRoomFailureImplCopyWith<_$DeleteRoomFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
