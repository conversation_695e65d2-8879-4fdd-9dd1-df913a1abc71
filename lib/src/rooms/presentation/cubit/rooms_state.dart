part of 'rooms_cubit.dart';

@freezed
class RoomsState with _$RoomsState {
  const factory RoomsState.initial() = _Initial;
  const factory RoomsState.loading() = Loading;
  const factory RoomsState.getRoomsSuccess() = GetRoomsSuccess;
  const factory RoomsState.getRoomsFailure(String message) = GetRoomsFailure;
  const factory RoomsState.addRoomSuccess() = AddRoomSuccess;
  const factory RoomsState.addRoomFailure(String message) = AddRoomFailure;
  const factory RoomsState.deleteRoomSuccess() = DeleteRoomSuccess;
  const factory RoomsState.deleteRoomFailure(String message) =
      DeleteRoomFailure;
}
