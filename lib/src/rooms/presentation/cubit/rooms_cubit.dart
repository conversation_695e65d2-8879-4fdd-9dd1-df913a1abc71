import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/rooms/data/models/rooms_params.dart';
import 'package:r2/src/rooms/data/repository/rooms_repo.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_operations.dart';

part 'rooms_state.dart';
part 'rooms_cubit.freezed.dart';

class RoomsCubit extends Cubit<RoomsState> with RoomsOperations {
  final RoomsRepo _roomsRepo;
  RoomsCubit(this._roomsRepo) : super(RoomsState.initial());

  Future<void> getRooms() async {
    emit(RoomsState.loading());
    final response = await _roomsRepo.getRooms(GetRoomsParams(
      hallId: injector<HallsCubit>().gethallData()?.id ?? 0,
    ));
    response.when(
      success: (result) {
        availableRooms = result.data?.available ?? [];
        reservedRooms = result.data?.reserved ?? [];
        emit(RoomsState.getRoomsSuccess());
      },
      failure: (message) => emit(
        RoomsState.getRoomsFailure(message),
      ),
    );
  }

  Future<void> addRoom() async {
    // emit(RoomsState.loading());
    final params = AddRoomParams(
      hallId: injector<HallsCubit>().gethallData()?.id ?? 0,
      additions: additions,
      capacity: int.tryParse(roomCapacityController.text),
      multiPrice: double.tryParse(multiPriceController.text),
      name: roomNameController.text,
      notes: notesController.text,
      price: double.tryParse(priceController.text),
      serviceCategoryId: serviceCategoryId,
      serviceId: serviceId,
      singlePrice: double.tryParse(singlePriceController.text),
    );
    final response = await _roomsRepo.addRoom(params);
    response.when(
      success: (result) {
        emit(RoomsState.addRoomSuccess());
      },
      failure: (message) => emit(
        RoomsState.addRoomFailure(message),
      ),
    );
  }

  Future<void> updateRoom() async {
    emit(RoomsState.loading());
    final params = AddRoomParams(
      roomId: roomId,
      hallId: injector<HallsCubit>().gethallData()?.id ?? 0,
      additions: additions,
      capacity: int.tryParse(roomCapacityController.text),
      multiPrice: double.tryParse(multiPriceController.text),
      name: roomNameController.text,
      notes: notesController.text,
      price: double.tryParse(priceController.text),
      serviceCategoryId: serviceCategoryId,
      serviceId: serviceId,
      singlePrice: double.tryParse(singlePriceController.text),
    );
    final response = await _roomsRepo.updateRoom(params);
    response.when(
      success: (result) {
        emit(RoomsState.addRoomSuccess());
      },
      failure: (message) => emit(
        RoomsState.addRoomFailure(message),
      ),
    );
  }

  Future<void> deleteRoom(int id) async {
    emit(RoomsState.loading());
    final response = await _roomsRepo.deleteRoom(id);
    response.when(
      success: (result) {
        emit(RoomsState.deleteRoomSuccess());
      },
      failure: (message) => emit(
        RoomsState.deleteRoomFailure(message),
      ),
    );
  }
}
