import 'package:flutter/material.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';

mixin RoomsOperations {
  final formKey = GlobalKey<FormState>();

  //GetRooms
  List<RoomModel> availableRooms = [];
  List<RoomModel> reservedRooms = [];

  void clearRooms() {
    availableRooms = [];
    reservedRooms = [];
  }

  //AddRoom
  late TextEditingController roomNameController;
  late TextEditingController roomCapacityController;
  late TextEditingController singlePriceController;
  late TextEditingController multiPriceController;
  late TextEditingController notesController;
  late TextEditingController priceController;
  List<AdditionModel> additions = [];
  int? serviceId;
  int? serviceCategoryId;
  int? roomId;

  void initAddRoomControllers() {
    roomNameController = TextEditingController();
    roomCapacityController = TextEditingController();
    singlePriceController = TextEditingController();
    multiPriceController = TextEditingController();
    notesController = TextEditingController();
    priceController = TextEditingController();
  }

  void disposeAddRoomControllers() {
    roomNameController.dispose();
    roomCapacityController.dispose();
    singlePriceController.dispose();
    multiPriceController.dispose();
    notesController.dispose();
    priceController.dispose();
  }

  void clear() {
    roomNameController.clear();
    roomCapacityController.clear();
    singlePriceController.clear();
    multiPriceController.clear();
    notesController.clear();
    priceController.clear();
    additions.clear();
    serviceId = null;
    serviceCategoryId = null;
    roomId = null;
  }
}
