import 'package:dio/dio.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/rooms/data/models/get_rooms_response.dart';
import 'package:r2/src/rooms/data/models/rooms_params.dart';
import 'package:retrofit/retrofit.dart';

part 'rooms_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class RoomsApiService {
  factory RoomsApiService(
    Dio dio, {
    String baseUrl,
  }) = _RoomsApiService;

  @GET(EndPoints.rooms)
  Future<GetRoomsResponse> getRooms(@Queries() GetRoomsParams params);

  @POST(EndPoints.rooms)
  Future<BaseResponse> addRoom(@Body() AddRoomParams params);

  @POST('${EndPoints.rooms}/{id}/delete')
  Future<BaseResponse> deleteRoom(@Path('id') int id);

  @POST('${EndPoints.rooms}/{id}/update')
  Future<BaseResponse> updateRoom(
      @Body() AddRoomParams params, @Path('id') int id);
}
