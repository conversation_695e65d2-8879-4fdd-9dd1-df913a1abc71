import 'package:r2/core/api/base_response.dart';
import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/rooms/data/api_service/rooms_api_service.dart';
import 'package:r2/src/rooms/data/models/get_rooms_response.dart';
import 'package:r2/src/rooms/data/models/rooms_params.dart';

class RoomsRepo {
  final RoomsApiService _roomsApiService;

  RoomsRepo(this._roomsApiService);
  Future<Result<GetRoomsResponse>> getRooms(GetRoomsParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _roomsApiService.getRooms(params);
        return result;
      });

  Future<Result<BaseResponse>> addRoom(AddRoomParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _roomsApiService.addRoom(params);
        return result;
      });
  Future<Result<BaseResponse>> updateRoom(AddRoomParams params) async =>
      await errorHandlerAsync(() async {
        final result =
            await _roomsApiService.updateRoom(params, params.roomId!);
        return result;
      });

  Future<Result<BaseResponse>> deleteRoom(int id) async =>
      await errorHandlerAsync(() async {
        final result = await _roomsApiService.deleteRoom(id);
        return result;
      });
}
