// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rooms_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddRoomParams _$AddRoomParamsFromJson(Map<String, dynamic> json) =>
    AddRoomParams(
      roomId: (json['room_id'] as num?)?.toInt(),
      hallId: (json['hall_id'] as num?)?.toInt(),
      serviceId: (json['service_id'] as num?)?.toInt(),
      serviceCategoryId: (json['service_category_id'] as num?)?.toInt(),
      additions: (json['additions'] as List<dynamic>?)
          ?.map((e) => AdditionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      name: json['name'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      singlePrice: (json['single_price'] as num?)?.toDouble(),
      multiPrice: (json['multi_price'] as num?)?.toDouble(),
      capacity: (json['capacity'] as num?)?.toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$AddRoomParamsToJson(AddRoomParams instance) =>
    <String, dynamic>{
      'hall_id': instance.hallId,
      'room_id': instance.roomId,
      'service_id': instance.serviceId,
      'service_category_id': instance.serviceCategoryId,
      'additions': instance.additions,
      'name': instance.name,
      'price': instance.price,
      'single_price': instance.singlePrice,
      'multi_price': instance.multiPrice,
      'capacity': instance.capacity,
      'notes': instance.notes,
    };

Addition _$AdditionFromJson(Map<String, dynamic> json) => Addition(
      id: (json['id'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$AdditionToJson(Addition instance) => <String, dynamic>{
      'id': instance.id,
      'price': instance.price,
    };

_$GetRoomsParamsImpl _$$GetRoomsParamsImplFromJson(Map<String, dynamic> json) =>
    _$GetRoomsParamsImpl(
      hallId: (json['hall_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GetRoomsParamsImplToJson(
        _$GetRoomsParamsImpl instance) =>
    <String, dynamic>{
      'hall_id': instance.hallId,
    };
