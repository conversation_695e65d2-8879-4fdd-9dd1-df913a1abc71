// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rooms_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RoomModel _$RoomModelFromJson(Map<String, dynamic> json) {
  return _RoomsModel.fromJson(json);
}

/// @nodoc
mixin _$RoomModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "hall_id")
  int? get hallId => throw _privateConstructorUsedError;
  @JsonKey(name: "service_id")
  int? get serviceId => throw _privateConstructorUsedError;
  @JsonKey(name: "service_category_id")
  int? get serviceCategoryId => throw _privateConstructorUsedError;
  @JsonKey(name: "room_status_id")
  int? get roomStatusId => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  double? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "single_price")
  double? get singlePrice => throw _privateConstructorUsedError;
  @JsonKey(name: "multi_price")
  double? get multiPrice => throw _privateConstructorUsedError;
  @JsonKey(name: "capacity")
  int? get capacity => throw _privateConstructorUsedError;
  @JsonKey(name: "notes")
  String? get notes => throw _privateConstructorUsedError;
  @JsonKey(name: "service")
  RoomStatus? get service => throw _privateConstructorUsedError;
  @JsonKey(name: "service_category")
  RoomStatus? get serviceCategory => throw _privateConstructorUsedError;
  @JsonKey(name: "room_status")
  RoomStatus? get roomStatus => throw _privateConstructorUsedError;
  @JsonKey(name: "additions")
  List<AdditionModel>? get additions => throw _privateConstructorUsedError;

  /// Serializes this RoomModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RoomModelCopyWith<RoomModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomModelCopyWith<$Res> {
  factory $RoomModelCopyWith(RoomModel value, $Res Function(RoomModel) then) =
      _$RoomModelCopyWithImpl<$Res, RoomModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "hall_id") int? hallId,
      @JsonKey(name: "service_id") int? serviceId,
      @JsonKey(name: "service_category_id") int? serviceCategoryId,
      @JsonKey(name: "room_status_id") int? roomStatusId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "single_price") double? singlePrice,
      @JsonKey(name: "multi_price") double? multiPrice,
      @JsonKey(name: "capacity") int? capacity,
      @JsonKey(name: "notes") String? notes,
      @JsonKey(name: "service") RoomStatus? service,
      @JsonKey(name: "service_category") RoomStatus? serviceCategory,
      @JsonKey(name: "room_status") RoomStatus? roomStatus,
      @JsonKey(name: "additions") List<AdditionModel>? additions});

  $RoomStatusCopyWith<$Res>? get service;
  $RoomStatusCopyWith<$Res>? get serviceCategory;
  $RoomStatusCopyWith<$Res>? get roomStatus;
}

/// @nodoc
class _$RoomModelCopyWithImpl<$Res, $Val extends RoomModel>
    implements $RoomModelCopyWith<$Res> {
  _$RoomModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? hallId = freezed,
    Object? serviceId = freezed,
    Object? serviceCategoryId = freezed,
    Object? roomStatusId = freezed,
    Object? name = freezed,
    Object? price = freezed,
    Object? singlePrice = freezed,
    Object? multiPrice = freezed,
    Object? capacity = freezed,
    Object? notes = freezed,
    Object? service = freezed,
    Object? serviceCategory = freezed,
    Object? roomStatus = freezed,
    Object? additions = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      hallId: freezed == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceId: freezed == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceCategoryId: freezed == serviceCategoryId
          ? _value.serviceCategoryId
          : serviceCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      roomStatusId: freezed == roomStatusId
          ? _value.roomStatusId
          : roomStatusId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      singlePrice: freezed == singlePrice
          ? _value.singlePrice
          : singlePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      multiPrice: freezed == multiPrice
          ? _value.multiPrice
          : multiPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      capacity: freezed == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as int?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      service: freezed == service
          ? _value.service
          : service // ignore: cast_nullable_to_non_nullable
              as RoomStatus?,
      serviceCategory: freezed == serviceCategory
          ? _value.serviceCategory
          : serviceCategory // ignore: cast_nullable_to_non_nullable
              as RoomStatus?,
      roomStatus: freezed == roomStatus
          ? _value.roomStatus
          : roomStatus // ignore: cast_nullable_to_non_nullable
              as RoomStatus?,
      additions: freezed == additions
          ? _value.additions
          : additions // ignore: cast_nullable_to_non_nullable
              as List<AdditionModel>?,
    ) as $Val);
  }

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RoomStatusCopyWith<$Res>? get service {
    if (_value.service == null) {
      return null;
    }

    return $RoomStatusCopyWith<$Res>(_value.service!, (value) {
      return _then(_value.copyWith(service: value) as $Val);
    });
  }

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RoomStatusCopyWith<$Res>? get serviceCategory {
    if (_value.serviceCategory == null) {
      return null;
    }

    return $RoomStatusCopyWith<$Res>(_value.serviceCategory!, (value) {
      return _then(_value.copyWith(serviceCategory: value) as $Val);
    });
  }

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RoomStatusCopyWith<$Res>? get roomStatus {
    if (_value.roomStatus == null) {
      return null;
    }

    return $RoomStatusCopyWith<$Res>(_value.roomStatus!, (value) {
      return _then(_value.copyWith(roomStatus: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RoomsModelImplCopyWith<$Res>
    implements $RoomModelCopyWith<$Res> {
  factory _$$RoomsModelImplCopyWith(
          _$RoomsModelImpl value, $Res Function(_$RoomsModelImpl) then) =
      __$$RoomsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "hall_id") int? hallId,
      @JsonKey(name: "service_id") int? serviceId,
      @JsonKey(name: "service_category_id") int? serviceCategoryId,
      @JsonKey(name: "room_status_id") int? roomStatusId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "single_price") double? singlePrice,
      @JsonKey(name: "multi_price") double? multiPrice,
      @JsonKey(name: "capacity") int? capacity,
      @JsonKey(name: "notes") String? notes,
      @JsonKey(name: "service") RoomStatus? service,
      @JsonKey(name: "service_category") RoomStatus? serviceCategory,
      @JsonKey(name: "room_status") RoomStatus? roomStatus,
      @JsonKey(name: "additions") List<AdditionModel>? additions});

  @override
  $RoomStatusCopyWith<$Res>? get service;
  @override
  $RoomStatusCopyWith<$Res>? get serviceCategory;
  @override
  $RoomStatusCopyWith<$Res>? get roomStatus;
}

/// @nodoc
class __$$RoomsModelImplCopyWithImpl<$Res>
    extends _$RoomModelCopyWithImpl<$Res, _$RoomsModelImpl>
    implements _$$RoomsModelImplCopyWith<$Res> {
  __$$RoomsModelImplCopyWithImpl(
      _$RoomsModelImpl _value, $Res Function(_$RoomsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? hallId = freezed,
    Object? serviceId = freezed,
    Object? serviceCategoryId = freezed,
    Object? roomStatusId = freezed,
    Object? name = freezed,
    Object? price = freezed,
    Object? singlePrice = freezed,
    Object? multiPrice = freezed,
    Object? capacity = freezed,
    Object? notes = freezed,
    Object? service = freezed,
    Object? serviceCategory = freezed,
    Object? roomStatus = freezed,
    Object? additions = freezed,
  }) {
    return _then(_$RoomsModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      hallId: freezed == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceId: freezed == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int?,
      serviceCategoryId: freezed == serviceCategoryId
          ? _value.serviceCategoryId
          : serviceCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      roomStatusId: freezed == roomStatusId
          ? _value.roomStatusId
          : roomStatusId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      singlePrice: freezed == singlePrice
          ? _value.singlePrice
          : singlePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      multiPrice: freezed == multiPrice
          ? _value.multiPrice
          : multiPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      capacity: freezed == capacity
          ? _value.capacity
          : capacity // ignore: cast_nullable_to_non_nullable
              as int?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      service: freezed == service
          ? _value.service
          : service // ignore: cast_nullable_to_non_nullable
              as RoomStatus?,
      serviceCategory: freezed == serviceCategory
          ? _value.serviceCategory
          : serviceCategory // ignore: cast_nullable_to_non_nullable
              as RoomStatus?,
      roomStatus: freezed == roomStatus
          ? _value.roomStatus
          : roomStatus // ignore: cast_nullable_to_non_nullable
              as RoomStatus?,
      additions: freezed == additions
          ? _value._additions
          : additions // ignore: cast_nullable_to_non_nullable
              as List<AdditionModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RoomsModelImpl implements _RoomsModel {
  const _$RoomsModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "hall_id") this.hallId,
      @JsonKey(name: "service_id") this.serviceId,
      @JsonKey(name: "service_category_id") this.serviceCategoryId,
      @JsonKey(name: "room_status_id") this.roomStatusId,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "single_price") this.singlePrice,
      @JsonKey(name: "multi_price") this.multiPrice,
      @JsonKey(name: "capacity") this.capacity,
      @JsonKey(name: "notes") this.notes,
      @JsonKey(name: "service") this.service,
      @JsonKey(name: "service_category") this.serviceCategory,
      @JsonKey(name: "room_status") this.roomStatus,
      @JsonKey(name: "additions") final List<AdditionModel>? additions})
      : _additions = additions;

  factory _$RoomsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomsModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "hall_id")
  final int? hallId;
  @override
  @JsonKey(name: "service_id")
  final int? serviceId;
  @override
  @JsonKey(name: "service_category_id")
  final int? serviceCategoryId;
  @override
  @JsonKey(name: "room_status_id")
  final int? roomStatusId;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "price")
  final double? price;
  @override
  @JsonKey(name: "single_price")
  final double? singlePrice;
  @override
  @JsonKey(name: "multi_price")
  final double? multiPrice;
  @override
  @JsonKey(name: "capacity")
  final int? capacity;
  @override
  @JsonKey(name: "notes")
  final String? notes;
  @override
  @JsonKey(name: "service")
  final RoomStatus? service;
  @override
  @JsonKey(name: "service_category")
  final RoomStatus? serviceCategory;
  @override
  @JsonKey(name: "room_status")
  final RoomStatus? roomStatus;
  final List<AdditionModel>? _additions;
  @override
  @JsonKey(name: "additions")
  List<AdditionModel>? get additions {
    final value = _additions;
    if (value == null) return null;
    if (_additions is EqualUnmodifiableListView) return _additions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RoomModel(id: $id, hallId: $hallId, serviceId: $serviceId, serviceCategoryId: $serviceCategoryId, roomStatusId: $roomStatusId, name: $name, price: $price, singlePrice: $singlePrice, multiPrice: $multiPrice, capacity: $capacity, notes: $notes, service: $service, serviceCategory: $serviceCategory, roomStatus: $roomStatus, additions: $additions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.hallId, hallId) || other.hallId == hallId) &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId) &&
            (identical(other.serviceCategoryId, serviceCategoryId) ||
                other.serviceCategoryId == serviceCategoryId) &&
            (identical(other.roomStatusId, roomStatusId) ||
                other.roomStatusId == roomStatusId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.singlePrice, singlePrice) ||
                other.singlePrice == singlePrice) &&
            (identical(other.multiPrice, multiPrice) ||
                other.multiPrice == multiPrice) &&
            (identical(other.capacity, capacity) ||
                other.capacity == capacity) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.service, service) || other.service == service) &&
            (identical(other.serviceCategory, serviceCategory) ||
                other.serviceCategory == serviceCategory) &&
            (identical(other.roomStatus, roomStatus) ||
                other.roomStatus == roomStatus) &&
            const DeepCollectionEquality()
                .equals(other._additions, _additions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      hallId,
      serviceId,
      serviceCategoryId,
      roomStatusId,
      name,
      price,
      singlePrice,
      multiPrice,
      capacity,
      notes,
      service,
      serviceCategory,
      roomStatus,
      const DeepCollectionEquality().hash(_additions));

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomsModelImplCopyWith<_$RoomsModelImpl> get copyWith =>
      __$$RoomsModelImplCopyWithImpl<_$RoomsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomsModelImplToJson(
      this,
    );
  }
}

abstract class _RoomsModel implements RoomModel {
  const factory _RoomsModel(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "hall_id") final int? hallId,
          @JsonKey(name: "service_id") final int? serviceId,
          @JsonKey(name: "service_category_id") final int? serviceCategoryId,
          @JsonKey(name: "room_status_id") final int? roomStatusId,
          @JsonKey(name: "name") final String? name,
          @JsonKey(name: "price") final double? price,
          @JsonKey(name: "single_price") final double? singlePrice,
          @JsonKey(name: "multi_price") final double? multiPrice,
          @JsonKey(name: "capacity") final int? capacity,
          @JsonKey(name: "notes") final String? notes,
          @JsonKey(name: "service") final RoomStatus? service,
          @JsonKey(name: "service_category") final RoomStatus? serviceCategory,
          @JsonKey(name: "room_status") final RoomStatus? roomStatus,
          @JsonKey(name: "additions") final List<AdditionModel>? additions}) =
      _$RoomsModelImpl;

  factory _RoomsModel.fromJson(Map<String, dynamic> json) =
      _$RoomsModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "hall_id")
  int? get hallId;
  @override
  @JsonKey(name: "service_id")
  int? get serviceId;
  @override
  @JsonKey(name: "service_category_id")
  int? get serviceCategoryId;
  @override
  @JsonKey(name: "room_status_id")
  int? get roomStatusId;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "price")
  double? get price;
  @override
  @JsonKey(name: "single_price")
  double? get singlePrice;
  @override
  @JsonKey(name: "multi_price")
  double? get multiPrice;
  @override
  @JsonKey(name: "capacity")
  int? get capacity;
  @override
  @JsonKey(name: "notes")
  String? get notes;
  @override
  @JsonKey(name: "service")
  RoomStatus? get service;
  @override
  @JsonKey(name: "service_category")
  RoomStatus? get serviceCategory;
  @override
  @JsonKey(name: "room_status")
  RoomStatus? get roomStatus;
  @override
  @JsonKey(name: "additions")
  List<AdditionModel>? get additions;

  /// Create a copy of RoomModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RoomsModelImplCopyWith<_$RoomsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RoomStatus _$RoomStatusFromJson(Map<String, dynamic> json) {
  return _RoomStatus.fromJson(json);
}

/// @nodoc
mixin _$RoomStatus {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "handle")
  String? get handle => throw _privateConstructorUsedError;
  @JsonKey(name: "price")
  double? get price => throw _privateConstructorUsedError;
  @JsonKey(name: "categories")
  List<RoomStatus>? get categories => throw _privateConstructorUsedError;
  @JsonKey(name: "service_id")
  int? get serviceId => throw _privateConstructorUsedError;

  /// Serializes this RoomStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RoomStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RoomStatusCopyWith<RoomStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomStatusCopyWith<$Res> {
  factory $RoomStatusCopyWith(
          RoomStatus value, $Res Function(RoomStatus) then) =
      _$RoomStatusCopyWithImpl<$Res, RoomStatus>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "handle") String? handle,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "categories") List<RoomStatus>? categories,
      @JsonKey(name: "service_id") int? serviceId});
}

/// @nodoc
class _$RoomStatusCopyWithImpl<$Res, $Val extends RoomStatus>
    implements $RoomStatusCopyWith<$Res> {
  _$RoomStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RoomStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? handle = freezed,
    Object? price = freezed,
    Object? categories = freezed,
    Object? serviceId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      handle: freezed == handle
          ? _value.handle
          : handle // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      categories: freezed == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<RoomStatus>?,
      serviceId: freezed == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RoomStatusImplCopyWith<$Res>
    implements $RoomStatusCopyWith<$Res> {
  factory _$$RoomStatusImplCopyWith(
          _$RoomStatusImpl value, $Res Function(_$RoomStatusImpl) then) =
      __$$RoomStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "handle") String? handle,
      @JsonKey(name: "price") double? price,
      @JsonKey(name: "categories") List<RoomStatus>? categories,
      @JsonKey(name: "service_id") int? serviceId});
}

/// @nodoc
class __$$RoomStatusImplCopyWithImpl<$Res>
    extends _$RoomStatusCopyWithImpl<$Res, _$RoomStatusImpl>
    implements _$$RoomStatusImplCopyWith<$Res> {
  __$$RoomStatusImplCopyWithImpl(
      _$RoomStatusImpl _value, $Res Function(_$RoomStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of RoomStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? handle = freezed,
    Object? price = freezed,
    Object? categories = freezed,
    Object? serviceId = freezed,
  }) {
    return _then(_$RoomStatusImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      handle: freezed == handle
          ? _value.handle
          : handle // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      categories: freezed == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<RoomStatus>?,
      serviceId: freezed == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RoomStatusImpl implements _RoomStatus {
  const _$RoomStatusImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "handle") this.handle,
      @JsonKey(name: "price") this.price,
      @JsonKey(name: "categories") final List<RoomStatus>? categories,
      @JsonKey(name: "service_id") this.serviceId})
      : _categories = categories;

  factory _$RoomStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomStatusImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "handle")
  final String? handle;
  @override
  @JsonKey(name: "price")
  final double? price;
  final List<RoomStatus>? _categories;
  @override
  @JsonKey(name: "categories")
  List<RoomStatus>? get categories {
    final value = _categories;
    if (value == null) return null;
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: "service_id")
  final int? serviceId;

  @override
  String toString() {
    return 'RoomStatus(id: $id, name: $name, handle: $handle, price: $price, categories: $categories, serviceId: $serviceId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.price, price) || other.price == price) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, handle, price,
      const DeepCollectionEquality().hash(_categories), serviceId);

  /// Create a copy of RoomStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomStatusImplCopyWith<_$RoomStatusImpl> get copyWith =>
      __$$RoomStatusImplCopyWithImpl<_$RoomStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomStatusImplToJson(
      this,
    );
  }
}

abstract class _RoomStatus implements RoomStatus {
  const factory _RoomStatus(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "name") final String? name,
      @JsonKey(name: "handle") final String? handle,
      @JsonKey(name: "price") final double? price,
      @JsonKey(name: "categories") final List<RoomStatus>? categories,
      @JsonKey(name: "service_id") final int? serviceId}) = _$RoomStatusImpl;

  factory _RoomStatus.fromJson(Map<String, dynamic> json) =
      _$RoomStatusImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "handle")
  String? get handle;
  @override
  @JsonKey(name: "price")
  double? get price;
  @override
  @JsonKey(name: "categories")
  List<RoomStatus>? get categories;
  @override
  @JsonKey(name: "service_id")
  int? get serviceId;

  /// Create a copy of RoomStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RoomStatusImplCopyWith<_$RoomStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
