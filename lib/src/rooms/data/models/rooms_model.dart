// To parse this JSON data, do
//
//     final roomsModel = roomsModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

import 'package:r2/src/splash/data/models/splash_model.dart';

part 'rooms_model.freezed.dart';
part 'rooms_model.g.dart';

RoomModel roomsModelFromJson(String str) =>
    RoomModel.fromJson(json.decode(str));

String roomsModelToJson(RoomModel data) => json.encode(data.toJson());

@freezed
class RoomModel with _$RoomModel {
  const factory RoomModel({
    @Json<PERSON>ey(name: "id") int? id,
    @Json<PERSON>ey(name: "hall_id") int? hallId,
    @Json<PERSON>ey(name: "service_id") int? serviceId,
    @JsonKey(name: "service_category_id") int? serviceCategoryId,
    @Json<PERSON><PERSON>(name: "room_status_id") int? roomStatusId,
    @Json<PERSON>ey(name: "name") String? name,
    @JsonKey(name: "price") double? price,
    @Json<PERSON>ey(name: "single_price") double? singlePrice,
    @Json<PERSON><PERSON>(name: "multi_price") double? multiPrice,
    @JsonKey(name: "capacity") int? capacity,
    @JsonKey(name: "notes") String? notes,
    @JsonKey(name: "service") RoomStatus? service,
    @JsonKey(name: "service_category") RoomStatus? serviceCategory,
    @JsonKey(name: "room_status") RoomStatus? roomStatus,
    @JsonKey(name: "additions") List<AdditionModel>? additions,
  }) = _RoomsModel;

  factory RoomModel.fromJson(Map<String, dynamic> json) =>
      _$RoomModelFromJson(json);
}

@freezed
class RoomStatus with _$RoomStatus {
  const factory RoomStatus({
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "handle") String? handle,
    @JsonKey(name: "price") double? price,
    @JsonKey(name: "categories") List<RoomStatus>? categories,
    @JsonKey(name: "service_id") int? serviceId,
  }) = _RoomStatus;

  factory RoomStatus.fromJson(Map<String, dynamic> json) =>
      _$RoomStatusFromJson(json);
}
