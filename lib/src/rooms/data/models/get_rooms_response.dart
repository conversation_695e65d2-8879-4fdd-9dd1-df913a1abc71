import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/rooms/data/models/rooms_model.dart';

part 'get_rooms_response.freezed.dart';
part 'get_rooms_response.g.dart';

@JsonSerializable()
class GetRoomsResponse extends BaseResponse {
  final Data? data;

  GetRoomsResponse({this.data, required super.errors, required super.message});
  factory GetRoomsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetRoomsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetRoomsResponseToJson(this);
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "available") List<RoomModel>? available,
    @JsonKey(name: "reserved") List<RoomModel>? reserved,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFrom<PERSON>son(json);
}
