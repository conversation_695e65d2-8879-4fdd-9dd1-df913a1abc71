// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rooms_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomsModelImpl _$$RoomsModelImplFromJson(Map<String, dynamic> json) =>
    _$RoomsModelImpl(
      id: (json['id'] as num?)?.toInt(),
      hallId: (json['hall_id'] as num?)?.toInt(),
      serviceId: (json['service_id'] as num?)?.toInt(),
      serviceCategoryId: (json['service_category_id'] as num?)?.toInt(),
      roomStatusId: (json['room_status_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      singlePrice: (json['single_price'] as num?)?.toDouble(),
      multiPrice: (json['multi_price'] as num?)?.toDouble(),
      capacity: (json['capacity'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      service: json['service'] == null
          ? null
          : RoomStatus.fromJson(json['service'] as Map<String, dynamic>),
      serviceCategory: json['service_category'] == null
          ? null
          : RoomStatus.fromJson(
              json['service_category'] as Map<String, dynamic>),
      roomStatus: json['room_status'] == null
          ? null
          : RoomStatus.fromJson(json['room_status'] as Map<String, dynamic>),
      additions: (json['additions'] as List<dynamic>?)
          ?.map((e) => AdditionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$RoomsModelImplToJson(_$RoomsModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'hall_id': instance.hallId,
      'service_id': instance.serviceId,
      'service_category_id': instance.serviceCategoryId,
      'room_status_id': instance.roomStatusId,
      'name': instance.name,
      'price': instance.price,
      'single_price': instance.singlePrice,
      'multi_price': instance.multiPrice,
      'capacity': instance.capacity,
      'notes': instance.notes,
      'service': instance.service,
      'service_category': instance.serviceCategory,
      'room_status': instance.roomStatus,
      'additions': instance.additions,
    };

_$RoomStatusImpl _$$RoomStatusImplFromJson(Map<String, dynamic> json) =>
    _$RoomStatusImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      handle: json['handle'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => RoomStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      serviceId: (json['service_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RoomStatusImplToJson(_$RoomStatusImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'handle': instance.handle,
      'price': instance.price,
      'categories': instance.categories,
      'service_id': instance.serviceId,
    };
