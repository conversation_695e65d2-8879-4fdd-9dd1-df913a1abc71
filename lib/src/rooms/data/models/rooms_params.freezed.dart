// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rooms_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetRoomsParams _$GetRoomsParamsFromJson(Map<String, dynamic> json) {
  return _GetRoomsParams.fromJson(json);
}

/// @nodoc
mixin _$GetRoomsParams {
  @JsonKey(name: "hall_id")
  int? get hallId => throw _privateConstructorUsedError;

  /// Serializes this GetRoomsParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetRoomsParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetRoomsParamsCopyWith<GetRoomsParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetRoomsParamsCopyWith<$Res> {
  factory $GetRoomsParamsCopyWith(
          GetRoomsParams value, $Res Function(GetRoomsParams) then) =
      _$GetRoomsParamsCopyWithImpl<$Res, GetRoomsParams>;
  @useResult
  $Res call({@JsonKey(name: "hall_id") int? hallId});
}

/// @nodoc
class _$GetRoomsParamsCopyWithImpl<$Res, $Val extends GetRoomsParams>
    implements $GetRoomsParamsCopyWith<$Res> {
  _$GetRoomsParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetRoomsParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hallId = freezed,
  }) {
    return _then(_value.copyWith(
      hallId: freezed == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetRoomsParamsImplCopyWith<$Res>
    implements $GetRoomsParamsCopyWith<$Res> {
  factory _$$GetRoomsParamsImplCopyWith(_$GetRoomsParamsImpl value,
          $Res Function(_$GetRoomsParamsImpl) then) =
      __$$GetRoomsParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: "hall_id") int? hallId});
}

/// @nodoc
class __$$GetRoomsParamsImplCopyWithImpl<$Res>
    extends _$GetRoomsParamsCopyWithImpl<$Res, _$GetRoomsParamsImpl>
    implements _$$GetRoomsParamsImplCopyWith<$Res> {
  __$$GetRoomsParamsImplCopyWithImpl(
      _$GetRoomsParamsImpl _value, $Res Function(_$GetRoomsParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetRoomsParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hallId = freezed,
  }) {
    return _then(_$GetRoomsParamsImpl(
      hallId: freezed == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GetRoomsParamsImpl implements _GetRoomsParams {
  const _$GetRoomsParamsImpl({@JsonKey(name: "hall_id") this.hallId});

  factory _$GetRoomsParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetRoomsParamsImplFromJson(json);

  @override
  @JsonKey(name: "hall_id")
  final int? hallId;

  @override
  String toString() {
    return 'GetRoomsParams(hallId: $hallId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetRoomsParamsImpl &&
            (identical(other.hallId, hallId) || other.hallId == hallId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, hallId);

  /// Create a copy of GetRoomsParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetRoomsParamsImplCopyWith<_$GetRoomsParamsImpl> get copyWith =>
      __$$GetRoomsParamsImplCopyWithImpl<_$GetRoomsParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetRoomsParamsImplToJson(
      this,
    );
  }
}

abstract class _GetRoomsParams implements GetRoomsParams {
  const factory _GetRoomsParams({@JsonKey(name: "hall_id") final int? hallId}) =
      _$GetRoomsParamsImpl;

  factory _GetRoomsParams.fromJson(Map<String, dynamic> json) =
      _$GetRoomsParamsImpl.fromJson;

  @override
  @JsonKey(name: "hall_id")
  int? get hallId;

  /// Create a copy of GetRoomsParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetRoomsParamsImplCopyWith<_$GetRoomsParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
