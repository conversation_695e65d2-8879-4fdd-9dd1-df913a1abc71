// To parse this JSON data, do
//
//     final getRoomsParams = getRoomsParamsFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

import 'package:r2/src/splash/data/models/splash_model.dart';

part 'rooms_params.freezed.dart';
part 'rooms_params.g.dart';

GetRoomsParams getRoomsParamsFromJson(String str) =>
    GetRoomsParams.fromJson(json.decode(str));

String getRoomsParamsToJson(GetRoomsParams data) => json.encode(data.toJson());

@freezed
class GetRoomsParams with _$GetRoomsParams {
  const factory GetRoomsParams({
    @JsonKey(name: "hall_id") int? hallId,
  }) = _GetRoomsParams;

  factory GetRoomsParams.fromJson(Map<String, dynamic> json) =>
      _$GetRoomsParamsFromJson(json);
}
// To parse this JSON data, do
//
//     final addRoomParams = addRoomParamsFromJson(jsonString);

AddRoomParams addRoomParamsFromJson(String str) =>
    AddRoomParams.fromJson(json.decode(str));

String addRoomParamsToJson(AddRoomParams data) => json.encode(data.toJson());

@JsonSerializable()
class AddRoomParams {
  @JsonKey(name: "hall_id")
  int? hallId;
  @JsonKey(name: "room_id")
  int? roomId;
  @JsonKey(name: "service_id")
  int? serviceId;
  @JsonKey(name: "service_category_id")
  int? serviceCategoryId;
  @JsonKey(name: "additions")
  List<AdditionModel>? additions;
  @JsonKey(name: "name")
  String? name;
  @JsonKey(name: "price")
  double? price;
  @JsonKey(name: "single_price")
  double? singlePrice;
  @JsonKey(name: "multi_price")
  double? multiPrice;
  @JsonKey(name: "capacity")
  int? capacity;
  @JsonKey(name: "notes")
  String? notes;

  AddRoomParams({
    this.roomId,
    this.hallId,
    this.serviceId,
    this.serviceCategoryId,
    this.additions,
    this.name,
    this.price,
    this.singlePrice,
    this.multiPrice,
    this.capacity,
    this.notes,
  });

  factory AddRoomParams.fromJson(Map<String, dynamic> json) =>
      _$AddRoomParamsFromJson(json);

  Map<String, dynamic> toJson() => _$AddRoomParamsToJson(this);
}

@JsonSerializable()
class Addition {
  @JsonKey(name: "id")
  int? id;
  @JsonKey(name: "price")
  double? price;

  Addition({
    this.id,
    this.price,
  });

  factory Addition.fromJson(Map<String, dynamic> json) =>
      _$AdditionFromJson(json);

  Map<String, dynamic> toJson() => _$AdditionToJson(this);
}
