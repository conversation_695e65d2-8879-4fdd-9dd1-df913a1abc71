import 'package:json_annotation/json_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';
part 'category_response.g.dart';
@JsonSerializable()
class CategoryResponse extends BaseResponse {
  final CategoryModel? data;

  CategoryResponse({
    this.data,
    required super.message,
    required super.errors,
  });
  factory CategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$CategoryResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$CategoryResponseToJson(this);
}
@JsonSerializable()
class GetCategoryResponse extends BaseResponse {
  final List<CafeCategoryModel>? data;

  GetCategoryResponse({
    this.data,
    required super.message,
    required super.errors,
  });
  factory GetCategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$GetCategoryResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetCategoryResponseToJson(this);
}
