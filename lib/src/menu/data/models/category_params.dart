// To parse this JSON data, do
//
//     final categoryParams = categoryParamsFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'category_params.g.dart';

CategoryParams categoryParamsFromJson(String str) => CategoryParams.fromJson(json.decode(str));

String categoryParamsToJson(CategoryParams data) => json.encode(data.toJson());

@JsonSerializable()
class CategoryParams {
    @JsonKey(name: "name")
    String name;
    @Json<PERSON>ey(name: "icon")
    String icon;

    CategoryParams({
        required this.name,
        required this.icon,
    });

    factory CategoryParams.fromJson(Map<String, dynamic> json) => _$CategoryParamsFromJson(json);

    Map<String, dynamic> toJson() => _$CategoryParamsToJson(this);
}
