// To parse this JSON data, do
//
//     final cafeProductParams = cafeProductParamsFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'products_params.g.dart';

CafeProductParams cafeProductParamsFromJson(String str) =>
    CafeProductParams.fromJson(json.decode(str));

String cafeProductParamsToJson(CafeProductParams data) =>
    json.encode(data.toJson());

@JsonSerializable()
class CafeProductParams {
  @Json<PERSON>ey(name: "hall_id")
  final int? hallId;
  @Json<PERSON>ey(name: "cafe_category_id")
  final int cafeCategoryId;
  @JsonKey(name: "search")
  final String? search;
  final String? icon;
  final String? name;
  final List<int>? halls;
  final double? price;

  CafeProductParams({
      this.icon,
    this.name,
    this.halls,
    this.price,
    this.hallId,
    required this.cafeCategoryId,
    this.search,
  });

  factory CafeProductParams.fromJson(Map<String, dynamic> json) =>
      _$CafeProductParamsFromJson(json);

  Map<String, dynamic> toJson() => _$CafeProductParamsToJson(this);
}
