// To parse this JSON data, do
//
//     final cafeProductModel = cafeProductModelFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'product_model.g.dart';

CafeProductModel cafeProductModelFromJson(String str) =>
    CafeProductModel.fromJson(json.decode(str));

String cafeProductModelToJson(CafeProductModel data) =>
    json.encode(data.toJson());

@JsonSerializable()
class CafeProductModel {
    @JsonKey(name: "id")
    int? id;
    @Json<PERSON>ey(name: "category_id")
    int? categoryId;
    @J<PERSON><PERSON><PERSON>(name: "name")
    String? name;
    @JsonKey(name: "price")
    double? price;
    @Json<PERSON>ey(name: "icon")
    String? icon;
    @JsonKey(name: "is_available")
    bool? isAvailable;
  
    CafeProductModel({
    this.id,
    this.categoryId,
    this.name,
    this.price,
    this.icon,
    this.isAvailable,
  });


    factory CafeProductModel.fromJson(Map<String, dynamic> json) => _$CafeProductModelFromJson(json);

    Map<String, dynamic> toJson() => _$CafeProductModelToJson(this);





}
