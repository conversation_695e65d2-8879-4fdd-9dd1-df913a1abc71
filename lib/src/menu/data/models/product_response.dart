import 'package:json_annotation/json_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
part 'product_response.g.dart';
@JsonSerializable()
class GetProductsResponse extends BaseResponse {
  final List<CafeProductModel>? data;

  GetProductsResponse({
    this.data,
    required super.message,
    required super.errors,
  });
  factory GetProductsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetProductsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetProductsResponseToJson(this);
}
@JsonSerializable()
class ProductsResponse extends BaseResponse {
  final CafeProductModel? data;

  ProductsResponse({
    this.data,
    required super.message,
    required super.errors,
  });
  factory ProductsResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$ProductsResponseToJson(this);
}