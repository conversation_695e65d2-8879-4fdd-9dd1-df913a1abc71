// To parse this JSON data, do
//
//     final categoryModel = categoryModelFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';

part 'category_model.g.dart';

@JsonSerializable()
class CafeCategoryModel {
    @J<PERSON><PERSON><PERSON>(name: "id")
    int? id;
    @J<PERSON><PERSON><PERSON>(name: "name")
    String? name;
    @Json<PERSON>ey(name: "icon")
    String? icon;
    @JsonKey(name: "products_count")
    int? productsCount;

    CafeCategoryModel({
         this.id,
         this.name,
         this.icon,
         this.productsCount,
    });

    factory CafeCategoryModel.fromJson(Map<String, dynamic> json) => _$CafeCategoryModelFromJson(json);

    Map<String, dynamic> toJson() => _$CafeCategoryModelToJson(this);

}
