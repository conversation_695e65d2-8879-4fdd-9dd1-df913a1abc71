import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/menu/data/api_service/menu_api_service.dart';
import 'package:r2/src/menu/data/models/category_params.dart';
import 'package:r2/src/menu/data/models/category_response.dart';
import 'package:r2/src/menu/data/models/product_response.dart';
import 'package:r2/src/menu/data/models/products_params.dart';

class MenuRepo {
  final MenuApiService _menuApiService;
  MenuRepo(this._menuApiService);
  Future<Result<CategoryResponse>> addCategory(CategoryParams params) async =>
      await errorHandlerAsync(
          () async => await _menuApiService.addCategory(params));
  Future<Result<CategoryResponse>> deleteCategory(int id) async =>
      await errorHandlerAsync(
          () async => await _menuApiService.deleteCategory(id));
  Future<Result<GetCategoryResponse>> getCategories() async =>
      await errorHandlerAsync(
          () async => await _menuApiService.getCategories());
  Future<Result<CategoryResponse>> updateCategory(
          int id, CategoryParams params) async =>
      await errorHandlerAsync(
          () async => await _menuApiService.updateCategory(id, params));
  Future<Result<GetProductsResponse>> getProducts(
          CafeProductParams params) async =>
      await errorHandlerAsync(
          () async => await _menuApiService.getProducts(params));
  Future<Result<ProductsResponse>> addProduct(CafeProductParams params) async =>
      await errorHandlerAsync(
          () async => await _menuApiService.addProduct(params));
  Future<Result<ProductsResponse>> updateProduct(
          int id, CafeProductParams params) async =>
      await errorHandlerAsync(
          () async => await _menuApiService.updateProduct(id, params));
  Future<Result<ProductsResponse>> deleteProduct(int id) async =>
      await errorHandlerAsync(
        () async => await _menuApiService.deleteProduct(id),
      );
}
