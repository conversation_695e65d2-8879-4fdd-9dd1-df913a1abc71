import 'package:dio/dio.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/menu/data/models/category_params.dart';
import 'package:r2/src/menu/data/models/category_response.dart';
import 'package:r2/src/menu/data/models/product_response.dart';
import 'package:r2/src/menu/data/models/products_params.dart';
import 'package:retrofit/retrofit.dart';
part 'menu_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class MenuApiService {
  factory MenuApiService(
    Dio dio, {
    String baseUrl,
  }) = _MenuApiService;
// Categories
  @POST(EndPoints.categories)
  Future<CategoryResponse> addCategory(@Body() CategoryParams params);
  @POST('${EndPoints.categories}/{id}/delete')
  Future<CategoryResponse> deleteCategory(@Path('id') int id);
  @POST('${EndPoints.categories}/{id}/update')
  Future<CategoryResponse> updateCategory(
      @Path('id') int id, @Body() CategoryParams params);
  @GET(EndPoints.categories)
  Future<GetCategoryResponse> getCategories();
//Products
  @GET(
    EndPoints.products,
  )
  Future<GetProductsResponse> getProducts(@Queries() CafeProductParams params);
  @POST(EndPoints.products)
  Future<ProductsResponse> addProduct(@Body() CafeProductParams params);
  @POST('${EndPoints.products}/{id}/update')
  Future<ProductsResponse> updateProduct(
      @Path('id') int id, @Body() CafeProductParams params);
  @POST('${EndPoints.products}/{id}/delete')
  Future<ProductsResponse> deleteProduct(@Path('id') int id);
}
