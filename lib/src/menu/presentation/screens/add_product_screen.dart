import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/menu/presentation/widgets/add_product_form.dart';

@RoutePage()
class AddProductScreen extends StatefulWidget implements AutoRouteWrapper {
  final CafeCategoryModel? cafeCategoryModel;
  final CafeProductModel? cafeProductModel;
  const AddProductScreen(
      {super.key, this.cafeCategoryModel, this.cafeProductModel});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
  
  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
        value: injector<MenuCubit>()..initProductControllers(), child: this);
  }
}

class _AddProductScreenState extends State<AddProductScreen> {
  @override
  dispose() {
    injector<MenuCubit>().disposeProductControllers();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return BlocListener<MenuCubit, MenuState>(
      listenWhen: (previous, current) => current.maybeWhen(
        addProductSuccess: () => true,
        addProductFailure: (message) => true,
        loading: () => true,
        orElse: () => false,
        updateProductSuccess: () => true,
        updateProductFailure: (message) => true,
      ),
      listener: (context, state) {
        state.whenOrNull(
            loading: () => UiHelper.onLoading(context),
            addProductSuccess: () {
              UiHelper.onSuccess();
              context.router.maybePop();
              injector<MenuCubit>().getProducts(widget.cafeCategoryModel?.id ?? 0);
              injector<MenuCubit>().clearProductData();
            },
            addProductFailure: (message) =>
                UiHelper.onFailure(context, message),
            updateProductSuccess: () {
              UiHelper.onSuccess();

              context.router.maybePop();

              injector<MenuCubit>().getProducts(widget.cafeCategoryModel?.id ?? 0);
              injector<MenuCubit>().clearProductData();
            },
            updateProductFailure: (message) =>
                UiHelper.onFailure(context, message));
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: defaultAppBar(context: context, title: context.l10n.addProduct),
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32)
                        .copyWith(bottom: 8),
                child: AddProductForm(
                  icon: widget.cafeCategoryModel?.icon,
                  product: widget.cafeProductModel,
                )),
          ),
        ),
      ),
    );
  }

}
