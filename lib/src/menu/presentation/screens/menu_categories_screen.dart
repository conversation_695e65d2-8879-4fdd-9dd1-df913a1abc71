import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/widgets/shared/add_widget.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/menu/presentation/widgets/add_category_modal.dart';
import 'package:r2/src/menu/presentation/widgets/menu_category_item.dart';

@RoutePage()
class MenuCategoriesScreen extends StatefulWidget implements AutoRouteWrapper {
  const MenuCategoriesScreen({super.key});

  @override
  State<MenuCategoriesScreen> createState() => _MenuCategoriesScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(value: injector<MenuCubit>()..initMenuControllers()..getCategories(), child: this);
  }
}

class _MenuCategoriesScreenState extends State<MenuCategoriesScreen> {
  @override
  void initState() {
    super.initState();
    // injector<MenuCubit>().getCategories();
  }
@override
  void dispose() {
    injector<MenuCubit>().disposeMenuControllers();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return BlocListener<MenuCubit, MenuState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        addCategoryFailure: (_) => true,
        addCategorySuccess: () => true,
        getCategoriesFailure: (_) => true,
        getCategoriesSuccess: () => true,
        deleteCategoryFailure: (_) => true,
        deleteCategorySuccess: () => true,
        updateCategorySuccess: () => true,
        updateCategoryFailure: (_) => true,
        orElse: () {
          return false;
        },
      ),
      listener: (context, state) {
        state.whenOrNull(
          getCategoriesSuccess: () => UiHelper.onSuccess(),
          getCategoriesFailure: (message) =>
              UiHelper.onFailure(context, message),
          addCategoryFailure: (message) => UiHelper.onFailure(context, message),
          addCategorySuccess: () async {
            UiHelper.onSuccess();
            await injector<MenuCubit>().getCategories();
            if (context.mounted) {
              context.router.maybePop();
            }
          },
          deleteCategorySuccess: () async {
            UiHelper.onSuccess();
            await injector<MenuCubit>().getCategories();
          },
          deleteCategoryFailure: (message) =>
              UiHelper.onFailure(context, message),
          loading: () => UiHelper.onLoading(context),
          updateCategorySuccess: () async {
            UiHelper.onSuccess();
            await injector<MenuCubit>().getCategories();
            if (context.mounted) {
              context.router.maybePop();
            }
          },
          updateCategoryFailure: (message) =>
              UiHelper.onFailure(context, message),
        );
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: defaultAppBar(context: context, title: context.l10n.menu),
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32)
                      .copyWith(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AddWidget(
                    title: context.l10n.addCategory,
                    onTap: () {
                      showModalBottomSheet(
                          isScrollControlled: true,
                          backgroundColor: AppColors.darkPurple,
                          context: context,
                          builder: (context) => AddCategoryModal());
                    },
                  ),
                  // AddBranchForm(),
                  SizedBox(
                    height: 16,
                  ),
                  BlocBuilder<MenuCubit, MenuState>(
                    builder: (context, state) {
                      return Expanded(
                        child: ListView.separated(
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 16),
                          itemCount:
                              injector<MenuCubit>().categories?.length ?? 0,
                          itemBuilder: (context, index) {
                            return MenuCategoryItem(
                              category:
                                  injector<MenuCubit>().categories?[index],
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
