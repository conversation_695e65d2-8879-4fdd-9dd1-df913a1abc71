import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/shared/add_widget.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/menu/presentation/widgets/filter_modal.dart';
import 'package:r2/src/menu/presentation/widgets/product_card.dart';

@RoutePage()
class MenuProductsScreen extends StatefulWidget implements AutoRouteWrapper {
  final CafeCategoryModel? cafeCategoryModel;
  const MenuProductsScreen({super.key, this.cafeCategoryModel});

  @override
  State<MenuProductsScreen> createState() => _MenuProductsScreenState();
  
  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
        value: injector<MenuCubit>()..getProducts(cafeCategoryModel?.id ?? 0),
        child: this);
  }
}

class _MenuProductsScreenState extends State<MenuProductsScreen> {
  @override
  void dispose() {
    injector<MenuCubit>().disposeMenuControllers();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {

    return BlocListener<MenuCubit, MenuState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        getProducrsFailure: (_) => true,
        getProductsSuccess: () => true,
        deleteProductSuccess: () => true,
        deleteProductFailure: (_) => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
            loading: () => UiHelper.onLoading(context),
            getProductsSuccess: () => {
                  UiHelper.onSuccess(),
                },
            getProducrsFailure: (message) =>
                UiHelper.onFailure(context, message),
            deleteProductSuccess: () async {
              UiHelper.onSuccess();
              await injector<MenuCubit>()
                  .getProducts(widget.cafeCategoryModel?.id ?? 0);
             

            },
            deleteProductFailure: (message) =>
                UiHelper.onFailure(context, message));
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: defaultAppBar(
            context: context, title: widget.cafeCategoryModel?.name ?? ''),
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32)
                      .copyWith(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.l10n.products,
                          style: TextStyles.label14
                              .copyWith(color: AppColors.darkGrey),
                        ),
                        IconButton(
                          onPressed: () {
                            showModalBottomSheet(
                                isScrollControlled: true,
                                backgroundColor: AppColors.darkPurple,
                                context: context,
                                builder: (_) => FilterModal());
                          },
                          icon: Icon(
                            Icons.tune,
                            color: AppColors.darkGrey,
                            size: 24,
                          ),
                        ),
                      ]),
                  SizedBox(
                    height: 8,
                  ),
                  AddWidget(
                    title: context.l10n.add,
                    onTap: () {
                      context.router.push(AddProductRoute(
                          cafeCategoryModel: widget.cafeCategoryModel));
                    },
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  BlocBuilder<MenuCubit, MenuState>(
                    builder: (context, state) {
                      return Expanded(
                        child: ListView.separated(
                          separatorBuilder: (context, index) =>
                              const SizedBox(height: 16),
                          itemCount:
                              injector<MenuCubit>().products?.length ?? 0,
                          itemBuilder: (context, index) {
                            return ProductCard(
                              product: injector<MenuCubit>().products![index],
                              category: widget.cafeCategoryModel,
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }


}
