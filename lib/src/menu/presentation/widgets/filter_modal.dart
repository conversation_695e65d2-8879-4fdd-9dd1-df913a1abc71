import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';

class FilterModal extends StatelessWidget {
  const FilterModal({super.key});

  @override
  Widget build(BuildContext context) {
    List branches = ["فرع١", "فرع١", "فرع١"];

    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(
            top: 32,
            bottom: MediaQuery.of(context).viewInsets.bottom,
            right: 16,
            left: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              context.l10n.filter,
              style: TextStyles.title16.copyWith(fontWeight: FontWeight.w700),
            ),
            SizedBox(
              height: 32,
            ),
            LabeledField(
              title: context.l10n.branch,
              field: CustomDropDownButton(
                onValidate: (value) {
                  if (value == null) {
                    return context.l10n.pleaseSelectBranch;
                  }
                  return null;
                },
                hintText: context.l10n.branch,
                filled: true,
                items: branches
                    .map(
                      (e) => DropdownMenuItem(
                        value: e,
                        child: Text(e),
                      ),
                    )
                    .toList(),
              ),
            ),
            SizedBox(
              height: 32,
            ),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: CustomElevatedButton(
                    isLight: false,
                    child: Text(context.l10n.confirm),
                  ),
                ),
                SizedBox(
                  width: 16,
                ),
                Expanded(
                  flex: 1,
                  child: CustomElevatedButton(
                    backgroundColor: Colors.transparent,
                    borderColor: AppColors.primary,
                    isLight: false,
                    child: Text(
                      context.l10n.back,
                      style:
                          TextStyles.title16.copyWith(color: AppColors.primary),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 32,
            ),
          ],
        ),
      ),
    );
  }
}
