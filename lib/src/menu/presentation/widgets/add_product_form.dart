import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';

class AddProductForm extends StatefulWidget {
  final String? icon;
  final CafeProductModel? product;
  const AddProductForm({super.key, this.icon, this.product});

  @override
  State<AddProductForm> createState() => _AddProductFormState();
}

class _AddProductFormState extends State<AddProductForm> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  void onSubmit() async {
    if (!formKey.currentState!.validate()) return;
    formKey.currentState!.save();
    injector<MenuCubit>().productIcon = widget.icon ?? widget.product?.icon;
    if (widget.product?.id != null) {
      await injector<MenuCubit>().updateProduct(widget.product?.id ?? 0);
    } else {
      await injector<MenuCubit>().addProduct();
    }
  }

  void initCtrls() {
    injector<MenuCubit>().productNameController?.text =
        widget.product?.name ?? '';
    injector<MenuCubit>().productPriceController?.text =
        widget.product?.price.toString() ?? '';
  }

  final halls = injector<HallsCubit>().halls ?? [];
  final selectedHalls = injector<MenuCubit>().selectedHalls;

  bool selectAll = false;
  @override
  initState() {
    super.initState();
    if (widget.product?.id != null) initCtrls();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LabeledField(
              field: CustomTextField(
                controller: injector<MenuCubit>().productNameController,
                hintText: context.l10n.productName,
                validator: (value) {
                  if (value != null && value.isEmpty) {
                    return context.l10n.pleaseEnterProductName;
                  }
                  return null;
                },
              ),
              title: context.l10n.productName,
            ),
            SizedBox(
              height: 16,
            ),
            LabeledField(
              field: CustomTextField(
                controller: injector<MenuCubit>().productPriceController,
                keyboardType: TextInputType.number,
                hintText: context.l10n.productPrice,
                validator: (value) {
                  if (value != null && value.isEmpty) {
                    return context.l10n.pleaseEnterProductPrice;
                  }
                  return null;
                },
              ),
              title: context.l10n.productPrice,
            ),
            SizedBox(
              height: 32,
            ),
            Text(context.l10n.validBranches),
            SizedBox(
              height: 8,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Checkbox(
                      fillColor: WidgetStateProperty.all(AppColors.darkPurple),
                      checkColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      value: halls.isNotEmpty &&
                          selectedHalls.length == halls.length,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            if (value) {
                              selectedHalls
                                ..clear()
                                ..addAll(halls.map((hall) => hall.id ?? 0));
                            } else {
                              selectedHalls.clear();
                            }
                          });
                        }
                      },
                    ),
                    const SizedBox(width: 16),
                    Text(context.l10n.allBranches),
                  ],
                ),
                const SizedBox(height: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: List.generate(halls.length, (index) {
                    final hall = halls[index];
                    final isSelected = selectedHalls.contains(hall.id);

                    return Column(
                      children: [
                        Row(
                          children: [
                            Checkbox(
                              fillColor:
                                  WidgetStateProperty.all(AppColors.darkPurple),
                              checkColor: AppColors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                              side: BorderSide(
                                color: isSelected
                                    ? AppColors.primary
                                    : Colors.transparent,
                                width: 2,
                              ),
                              value: isSelected,
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    if (value) {
                                      selectedHalls.add(hall.id ?? 0);
                                    } else {
                                      selectedHalls.remove(hall.id);
                                    }
                                  });
                                }
                              },
                            ),
                            const SizedBox(width: 16),
                            Text(hall.name ?? '-'),
                          ],
                        ),
                        if (index != halls.length - 1)
                          const SizedBox(height: 8),
                      ],
                    );
                  }),
                ),
              ],
            ),
            const SizedBox(height: 32),
            CustomElevatedButton(
              onPressed: onSubmit,
              child: Text(widget.product?.id != null
                  ? context.l10n.update
                  : context.l10n.addProduct),
            )
          ],
        ),
      ),
    );
  }
}
  // branches[index]['isSelected'] = value!;
                              // if (index == 0) {
                              //   toggleAllBranches(value);
                              // } else {
                              //   if (!value) {
                              //     branches[0]['isSelected'] = false;
                              //   }
                              // }