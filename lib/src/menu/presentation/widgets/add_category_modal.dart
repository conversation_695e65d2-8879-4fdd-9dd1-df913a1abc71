import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/menu/data/models/category_icon_enum.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';

class AddCategoryModal extends StatefulWidget {
  final CafeCategoryModel? cafeCategoryModel;
  const AddCategoryModal({super.key, this.cafeCategoryModel});

  @override
  State<AddCategoryModal> createState() => _AddCategoryModalState();
}

class _AddCategoryModalState extends State<AddCategoryModal> {
  late final ToggleCubit<CategoryIconEnum> gamesToggleCubit;
  @override
  initState() {
    super.initState();
    injector<MenuCubit>().initMenuControllers();
    gamesToggleCubit =
        ToggleCubit<CategoryIconEnum>(value: CategoryIconEnum.hotDrinks);
    if (widget.cafeCategoryModel?.id != null) {
      initCtrls();
    }
  }

  CategoryIconEnum? getEnum(String icon) {
    return CategoryIconEnum.values.firstWhere(
      (e) => e.toString().split('.').last == icon,
      // Return null if no match is found
    );
  }

  void initCtrls() {
    injector<MenuCubit>().categoryNameController?.text =
        widget.cafeCategoryModel?.name ?? '';
    gamesToggleCubit.value = getEnum(widget.cafeCategoryModel?.icon ?? '');
  }

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  void onSubmit() async {
    if (!formKey.currentState!.validate()) return;
    formKey.currentState!.save();
    injector<MenuCubit>().categoryIcon = gamesToggleCubit.value?.name;
    // print(injector<MenuCubit>().createCategoryParams().toJson());
    if (widget.cafeCategoryModel?.id == null) {
      await injector<MenuCubit>().addCategory();
    } else {
      await injector<MenuCubit>()
          .updateCategory(widget.cafeCategoryModel?.id ?? 0);
    }
  }

  List preIcons = [
    {
      'img': Assets.icons.hot.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.hotDrinks
    },
    {
      'img': Assets.icons.cold.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.coldDrinks
    },
    {
      'img': Assets.icons.meals.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.meals
    },
    {
      'img': Assets.icons.fizzy.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.fizzy
    },
    {
      'img': Assets.icons.dessert.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.desserts
    },
    {
      'img': Assets.icons.sandwich.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.sandwiches
    },
    {
      'img': Assets.icons.soup.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.soups
    },
    {
      'img': Assets.icons.pasta.svg(
        width: 40,
        height: 40,
        fit: BoxFit.fill,
      ),
      'index': CategoryIconEnum.pasta
    },
  ];
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(
            top: 32,
            bottom: MediaQuery.of(context).viewInsets.bottom,
            right: 16,
            left: 16),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                context.l10n.addCategory,
                style: TextStyles.title16.copyWith(fontWeight: FontWeight.w700),
              ),
              SizedBox(
                height: 32,
              ),
              LabeledField(
                title: context.l10n.categoryName,
                field: CustomTextField(
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return context.l10n.pleaseEnterCategory;
                    }
                    return null;
                  },
                  controller: injector<MenuCubit>().categoryNameController,
                  hintText: context.l10n.categoryName,
                  fillColor: AppColors.darkGrey,
                ),
              ),
              SizedBox(
                height: 32,
              ),
              BlocProvider(
                create: (context) => gamesToggleCubit,
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 60,
                    runSpacing: 10,
                    children: List.generate(
                      preIcons.length,
                      (index) => BlocBuilder<ToggleCubit<CategoryIconEnum>,
                          ToggleState>(
                        builder: (context, state) {
                          return GestureDetector(
                            onTap: () {
                              gamesToggleCubit.toggle(preIcons[index]['index']);
                            },
                            child: Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color: gamesToggleCubit.value ==
                                            preIcons[index]['index']
                                        ? AppColors.border
                                        : Colors.transparent,
                                    width: 2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: preIcons[index]['img'],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 32,
              ),
              CustomElevatedButton(
                isLight: false,
                onPressed: onSubmit,
                child: Text(widget.cafeCategoryModel?.id == null? context.l10n.add: context.l10n.update),
              ),
              SizedBox(
                height: 32,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
