import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';

class ProductCard extends StatelessWidget {
  final CafeProductModel? product;
  final CafeCategoryModel? category;
  const ProductCard({super.key, this.product, this.category});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 16, left: 16, bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.darkPurple,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.card,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
            width: 66,
            height: 60,
            padding: const EdgeInsets.all(8),
            child: injector<MenuCubit>().returnedIcon(product?.icon ?? ''),
          ),
          SizedBox(
            width: 16,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                product?.name ?? '',
                style: TextStyles.title16.copyWith(fontWeight: FontWeight.w400),
              ),
              Text(
                '${product?.price} جم',
                style: TextStyles.title14.copyWith(fontWeight: FontWeight.w700),
              ),
            ],
          ),
          Spacer(),
          IconButton(
              onPressed: () {
                context.router.push(AddProductRoute(
                    cafeProductModel: product, cafeCategoryModel: category));
              },
              icon: Icon(
                Icons.create_outlined,
                size: 24,
              )),
          IconButton(
            onPressed: () => UiHelper.showCustomDialog(
              barrierDismissible: false,
              context: context,
              dialog: AlertDialog(
                backgroundColor: AppColors.darkPurple,
                icon: Assets.icons.trashIcon2.svg(),
                title: Text(
                  context.l10n.confirmDelete,
                  style:
                      TextStyles.title16.copyWith(fontWeight: FontWeight.w700),
                ),
                titlePadding: EdgeInsets.only(top: 10),
                actionsPadding:
                    EdgeInsets.only(top: 40, bottom: 24, right: 30, left: 30),
                iconPadding: EdgeInsets.only(top: 30, bottom: 10),
                actions: [
                  CustomElevatedButton(
                    width: 250,
                    isLight: false,
                    child: Text(context.l10n.yes),
                    onPressed: () async {
                      await injector<MenuCubit>().deleteProduct(
                        product?.id ?? 0,
                      );
                      if (context.mounted) {
                        context.router.maybePop();
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  CustomElevatedButton(
                    width: 250,
                    isLight: false,
                    backgroundColor: AppColors.darkPurple,
                    borderColor: AppColors.primary,
                    child: Text(context.l10n.no),
                    onPressed: () => context.router.maybePop(),
                  ),
                ],
              ),
            ),
            icon: Icon(
              Icons.delete,
              size: 24,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}
