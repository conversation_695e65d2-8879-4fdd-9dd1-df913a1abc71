import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/src/menu/data/repository/menu_repo.dart';
import 'package:r2/src/menu/presentation/cubit/menu_operations.dart';

part 'menu_state.dart';
part 'menu_cubit.freezed.dart';

class MenuCubit extends Cubit<MenuState> with MenuOperations {
  final MenuRepo _menuRepo;
  MenuCubit(this._menuRepo) : super(MenuState.initial());
  Future<void> addCategory() async {
    emit(MenuState.loading());
    final result = await _menuRepo.addCategory(createCategoryParams());
    result.when(
        success: (response) {
          emit(MenuState.addCategorySuccess());
        },
        failure: (message) => emit(MenuState.addCategoryFailure(message)));
  }

  Future<void> getCategories() async {
    emit(MenuState.loading());
    final result = await _menuRepo.getCategories();
    result.when(
        success: (response) {
          emit(MenuState.getCategoriesSuccess());
          categories = response.data ?? [];
        },
        failure: (message) => emit(MenuState.getCategoriesFailure(message)));
  }

  Future<void> deleteCategory(int id) async {
    emit(MenuState.loading());
    final result = await _menuRepo.deleteCategory(id);
    result.when(
        success: (response) {
          emit(MenuState.deleteCategorySuccess());
        },
        failure: (message) => emit(MenuState.deleteCategoryFailure(message)));
  }

  Future<void> updateCategory(int id) async {
    emit(MenuState.loading());
    final result = await _menuRepo.updateCategory(id, createCategoryParams());
    result.when(
        success: (response) {
          emit(MenuState.updateCategorySuccess());
        },
        failure: (message) => emit(MenuState.updateCategoryFailure(message)));
  }

  Future<void> getProducts(int id) async {
    emit(MenuState.loading());
    selectedCategoryId = id;
    final result = await _menuRepo.getProducts(createCafeProductParams());
    result.when(
        success: (response) {
          emit(MenuState.getProductsSuccess());
          products = response.data ?? [];
        },
        failure: (message) => emit(MenuState.getProducrsFailure(message)));
  }

  Future<void> addProduct() async {
    emit(MenuState.loading());
    final result = await _menuRepo.addProduct(createCafeProductParams());
    result.when(
        success: (response) {
          emit(MenuState.addProductSuccess());
        },
        failure: (message) => emit(MenuState.addProductFailure(message)));
  }
  Future<void> updateProduct(int id)async{
    emit(MenuState.loading());
    final result = await _menuRepo.updateProduct(id, createCafeProductParams());
    result.when(
        success: (response) {
          emit(MenuState.updateProductSuccess());
        },
        failure: (message) => emit(MenuState.updateProductFailure(message)));
  }
  Future<void>deleteProduct(int id)async{
    emit(MenuState.loading());
    final result = await _menuRepo.deleteProduct(id);
    result.when(
        success: (response) {
          emit(MenuState.deleteProductSuccess());
        },
        failure: (message) => emit(MenuState.deleteProductFailure(message)));
  }
}
