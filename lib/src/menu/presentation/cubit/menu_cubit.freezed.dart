// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'menu_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MenuState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MenuStateCopyWith<$Res> {
  factory $MenuStateCopyWith(MenuState value, $Res Function(MenuState) then) =
      _$MenuStateCopyWithImpl<$Res, MenuState>;
}

/// @nodoc
class _$MenuStateCopyWithImpl<$Res, $Val extends MenuState>
    implements $MenuStateCopyWith<$Res> {
  _$MenuStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'MenuState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements MenuState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'MenuState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements MenuState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$AddCategorySuccessImplCopyWith<$Res> {
  factory _$$AddCategorySuccessImplCopyWith(_$AddCategorySuccessImpl value,
          $Res Function(_$AddCategorySuccessImpl) then) =
      __$$AddCategorySuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddCategorySuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$AddCategorySuccessImpl>
    implements _$$AddCategorySuccessImplCopyWith<$Res> {
  __$$AddCategorySuccessImplCopyWithImpl(_$AddCategorySuccessImpl _value,
      $Res Function(_$AddCategorySuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AddCategorySuccessImpl implements AddCategorySuccess {
  const _$AddCategorySuccessImpl();

  @override
  String toString() {
    return 'MenuState.addCategorySuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AddCategorySuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return addCategorySuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return addCategorySuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addCategorySuccess != null) {
      return addCategorySuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return addCategorySuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return addCategorySuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addCategorySuccess != null) {
      return addCategorySuccess(this);
    }
    return orElse();
  }
}

abstract class AddCategorySuccess implements MenuState {
  const factory AddCategorySuccess() = _$AddCategorySuccessImpl;
}

/// @nodoc
abstract class _$$AddCategoryFaiulreImplCopyWith<$Res> {
  factory _$$AddCategoryFaiulreImplCopyWith(_$AddCategoryFaiulreImpl value,
          $Res Function(_$AddCategoryFaiulreImpl) then) =
      __$$AddCategoryFaiulreImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$AddCategoryFaiulreImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$AddCategoryFaiulreImpl>
    implements _$$AddCategoryFaiulreImplCopyWith<$Res> {
  __$$AddCategoryFaiulreImplCopyWithImpl(_$AddCategoryFaiulreImpl _value,
      $Res Function(_$AddCategoryFaiulreImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$AddCategoryFaiulreImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddCategoryFaiulreImpl implements AddCategoryFaiulre {
  const _$AddCategoryFaiulreImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.addCategoryFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddCategoryFaiulreImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddCategoryFaiulreImplCopyWith<_$AddCategoryFaiulreImpl> get copyWith =>
      __$$AddCategoryFaiulreImplCopyWithImpl<_$AddCategoryFaiulreImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return addCategoryFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return addCategoryFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addCategoryFailure != null) {
      return addCategoryFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return addCategoryFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return addCategoryFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addCategoryFailure != null) {
      return addCategoryFailure(this);
    }
    return orElse();
  }
}

abstract class AddCategoryFaiulre implements MenuState {
  const factory AddCategoryFaiulre(final String message) =
      _$AddCategoryFaiulreImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddCategoryFaiulreImplCopyWith<_$AddCategoryFaiulreImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetCategoriesSuccessImplCopyWith<$Res> {
  factory _$$GetCategoriesSuccessImplCopyWith(_$GetCategoriesSuccessImpl value,
          $Res Function(_$GetCategoriesSuccessImpl) then) =
      __$$GetCategoriesSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetCategoriesSuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$GetCategoriesSuccessImpl>
    implements _$$GetCategoriesSuccessImplCopyWith<$Res> {
  __$$GetCategoriesSuccessImplCopyWithImpl(_$GetCategoriesSuccessImpl _value,
      $Res Function(_$GetCategoriesSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetCategoriesSuccessImpl implements GetCategoriesSuccess {
  const _$GetCategoriesSuccessImpl();

  @override
  String toString() {
    return 'MenuState.getCategoriesSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCategoriesSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return getCategoriesSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return getCategoriesSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getCategoriesSuccess != null) {
      return getCategoriesSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return getCategoriesSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return getCategoriesSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getCategoriesSuccess != null) {
      return getCategoriesSuccess(this);
    }
    return orElse();
  }
}

abstract class GetCategoriesSuccess implements MenuState {
  const factory GetCategoriesSuccess() = _$GetCategoriesSuccessImpl;
}

/// @nodoc
abstract class _$$GetCategoriesFailureImplCopyWith<$Res> {
  factory _$$GetCategoriesFailureImplCopyWith(_$GetCategoriesFailureImpl value,
          $Res Function(_$GetCategoriesFailureImpl) then) =
      __$$GetCategoriesFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$GetCategoriesFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$GetCategoriesFailureImpl>
    implements _$$GetCategoriesFailureImplCopyWith<$Res> {
  __$$GetCategoriesFailureImplCopyWithImpl(_$GetCategoriesFailureImpl _value,
      $Res Function(_$GetCategoriesFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$GetCategoriesFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetCategoriesFailureImpl implements GetCategoriesFailure {
  const _$GetCategoriesFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.getCategoriesFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetCategoriesFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetCategoriesFailureImplCopyWith<_$GetCategoriesFailureImpl>
      get copyWith =>
          __$$GetCategoriesFailureImplCopyWithImpl<_$GetCategoriesFailureImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return getCategoriesFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return getCategoriesFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getCategoriesFailure != null) {
      return getCategoriesFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return getCategoriesFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return getCategoriesFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getCategoriesFailure != null) {
      return getCategoriesFailure(this);
    }
    return orElse();
  }
}

abstract class GetCategoriesFailure implements MenuState {
  const factory GetCategoriesFailure(final String message) =
      _$GetCategoriesFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetCategoriesFailureImplCopyWith<_$GetCategoriesFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteCategorySuccessImplCopyWith<$Res> {
  factory _$$DeleteCategorySuccessImplCopyWith(
          _$DeleteCategorySuccessImpl value,
          $Res Function(_$DeleteCategorySuccessImpl) then) =
      __$$DeleteCategorySuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteCategorySuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$DeleteCategorySuccessImpl>
    implements _$$DeleteCategorySuccessImplCopyWith<$Res> {
  __$$DeleteCategorySuccessImplCopyWithImpl(_$DeleteCategorySuccessImpl _value,
      $Res Function(_$DeleteCategorySuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteCategorySuccessImpl implements DeleteCategorySuccess {
  const _$DeleteCategorySuccessImpl();

  @override
  String toString() {
    return 'MenuState.deleteCategorySuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteCategorySuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return deleteCategorySuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return deleteCategorySuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteCategorySuccess != null) {
      return deleteCategorySuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return deleteCategorySuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return deleteCategorySuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteCategorySuccess != null) {
      return deleteCategorySuccess(this);
    }
    return orElse();
  }
}

abstract class DeleteCategorySuccess implements MenuState {
  const factory DeleteCategorySuccess() = _$DeleteCategorySuccessImpl;
}

/// @nodoc
abstract class _$$DeleteCategoryFailureImplCopyWith<$Res> {
  factory _$$DeleteCategoryFailureImplCopyWith(
          _$DeleteCategoryFailureImpl value,
          $Res Function(_$DeleteCategoryFailureImpl) then) =
      __$$DeleteCategoryFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DeleteCategoryFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$DeleteCategoryFailureImpl>
    implements _$$DeleteCategoryFailureImplCopyWith<$Res> {
  __$$DeleteCategoryFailureImplCopyWithImpl(_$DeleteCategoryFailureImpl _value,
      $Res Function(_$DeleteCategoryFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$DeleteCategoryFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteCategoryFailureImpl implements DeleteCategoryFailure {
  const _$DeleteCategoryFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.deleteCategoryFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteCategoryFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteCategoryFailureImplCopyWith<_$DeleteCategoryFailureImpl>
      get copyWith => __$$DeleteCategoryFailureImplCopyWithImpl<
          _$DeleteCategoryFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return deleteCategoryFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return deleteCategoryFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteCategoryFailure != null) {
      return deleteCategoryFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return deleteCategoryFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return deleteCategoryFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteCategoryFailure != null) {
      return deleteCategoryFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteCategoryFailure implements MenuState {
  const factory DeleteCategoryFailure(final String message) =
      _$DeleteCategoryFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteCategoryFailureImplCopyWith<_$DeleteCategoryFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateCategorySuccessImplCopyWith<$Res> {
  factory _$$UpdateCategorySuccessImplCopyWith(
          _$UpdateCategorySuccessImpl value,
          $Res Function(_$UpdateCategorySuccessImpl) then) =
      __$$UpdateCategorySuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateCategorySuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$UpdateCategorySuccessImpl>
    implements _$$UpdateCategorySuccessImplCopyWith<$Res> {
  __$$UpdateCategorySuccessImplCopyWithImpl(_$UpdateCategorySuccessImpl _value,
      $Res Function(_$UpdateCategorySuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateCategorySuccessImpl implements UpdateCategorySuccess {
  const _$UpdateCategorySuccessImpl();

  @override
  String toString() {
    return 'MenuState.updateCategorySuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCategorySuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return updateCategorySuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return updateCategorySuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateCategorySuccess != null) {
      return updateCategorySuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return updateCategorySuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return updateCategorySuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateCategorySuccess != null) {
      return updateCategorySuccess(this);
    }
    return orElse();
  }
}

abstract class UpdateCategorySuccess implements MenuState {
  const factory UpdateCategorySuccess() = _$UpdateCategorySuccessImpl;
}

/// @nodoc
abstract class _$$UpdateCategoryFailureImplCopyWith<$Res> {
  factory _$$UpdateCategoryFailureImplCopyWith(
          _$UpdateCategoryFailureImpl value,
          $Res Function(_$UpdateCategoryFailureImpl) then) =
      __$$UpdateCategoryFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UpdateCategoryFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$UpdateCategoryFailureImpl>
    implements _$$UpdateCategoryFailureImplCopyWith<$Res> {
  __$$UpdateCategoryFailureImplCopyWithImpl(_$UpdateCategoryFailureImpl _value,
      $Res Function(_$UpdateCategoryFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$UpdateCategoryFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateCategoryFailureImpl implements UpdateCategoryFailure {
  const _$UpdateCategoryFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.updateCategoryFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCategoryFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCategoryFailureImplCopyWith<_$UpdateCategoryFailureImpl>
      get copyWith => __$$UpdateCategoryFailureImplCopyWithImpl<
          _$UpdateCategoryFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return updateCategoryFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return updateCategoryFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateCategoryFailure != null) {
      return updateCategoryFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return updateCategoryFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return updateCategoryFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateCategoryFailure != null) {
      return updateCategoryFailure(this);
    }
    return orElse();
  }
}

abstract class UpdateCategoryFailure implements MenuState {
  const factory UpdateCategoryFailure(final String message) =
      _$UpdateCategoryFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCategoryFailureImplCopyWith<_$UpdateCategoryFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetProductsSuccessImplCopyWith<$Res> {
  factory _$$GetProductsSuccessImplCopyWith(_$GetProductsSuccessImpl value,
          $Res Function(_$GetProductsSuccessImpl) then) =
      __$$GetProductsSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetProductsSuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$GetProductsSuccessImpl>
    implements _$$GetProductsSuccessImplCopyWith<$Res> {
  __$$GetProductsSuccessImplCopyWithImpl(_$GetProductsSuccessImpl _value,
      $Res Function(_$GetProductsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetProductsSuccessImpl implements GetProductsSuccess {
  const _$GetProductsSuccessImpl();

  @override
  String toString() {
    return 'MenuState.getProductsSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetProductsSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return getProductsSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return getProductsSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getProductsSuccess != null) {
      return getProductsSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return getProductsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return getProductsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getProductsSuccess != null) {
      return getProductsSuccess(this);
    }
    return orElse();
  }
}

abstract class GetProductsSuccess implements MenuState {
  const factory GetProductsSuccess() = _$GetProductsSuccessImpl;
}

/// @nodoc
abstract class _$$GetProducrsFailureImplCopyWith<$Res> {
  factory _$$GetProducrsFailureImplCopyWith(_$GetProducrsFailureImpl value,
          $Res Function(_$GetProducrsFailureImpl) then) =
      __$$GetProducrsFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$GetProducrsFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$GetProducrsFailureImpl>
    implements _$$GetProducrsFailureImplCopyWith<$Res> {
  __$$GetProducrsFailureImplCopyWithImpl(_$GetProducrsFailureImpl _value,
      $Res Function(_$GetProducrsFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$GetProducrsFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetProducrsFailureImpl implements GetProducrsFailure {
  const _$GetProducrsFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.getProducrsFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetProducrsFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetProducrsFailureImplCopyWith<_$GetProducrsFailureImpl> get copyWith =>
      __$$GetProducrsFailureImplCopyWithImpl<_$GetProducrsFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return getProducrsFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return getProducrsFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getProducrsFailure != null) {
      return getProducrsFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return getProducrsFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return getProducrsFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (getProducrsFailure != null) {
      return getProducrsFailure(this);
    }
    return orElse();
  }
}

abstract class GetProducrsFailure implements MenuState {
  const factory GetProducrsFailure(final String message) =
      _$GetProducrsFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetProducrsFailureImplCopyWith<_$GetProducrsFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddProductSuccessImplCopyWith<$Res> {
  factory _$$AddProductSuccessImplCopyWith(_$AddProductSuccessImpl value,
          $Res Function(_$AddProductSuccessImpl) then) =
      __$$AddProductSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddProductSuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$AddProductSuccessImpl>
    implements _$$AddProductSuccessImplCopyWith<$Res> {
  __$$AddProductSuccessImplCopyWithImpl(_$AddProductSuccessImpl _value,
      $Res Function(_$AddProductSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AddProductSuccessImpl implements AddProductSuccess {
  const _$AddProductSuccessImpl();

  @override
  String toString() {
    return 'MenuState.addProductSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AddProductSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return addProductSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return addProductSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addProductSuccess != null) {
      return addProductSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return addProductSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return addProductSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addProductSuccess != null) {
      return addProductSuccess(this);
    }
    return orElse();
  }
}

abstract class AddProductSuccess implements MenuState {
  const factory AddProductSuccess() = _$AddProductSuccessImpl;
}

/// @nodoc
abstract class _$$AddProductFailureImplCopyWith<$Res> {
  factory _$$AddProductFailureImplCopyWith(_$AddProductFailureImpl value,
          $Res Function(_$AddProductFailureImpl) then) =
      __$$AddProductFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$AddProductFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$AddProductFailureImpl>
    implements _$$AddProductFailureImplCopyWith<$Res> {
  __$$AddProductFailureImplCopyWithImpl(_$AddProductFailureImpl _value,
      $Res Function(_$AddProductFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$AddProductFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddProductFailureImpl implements AddProductFailure {
  const _$AddProductFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.addProductFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddProductFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddProductFailureImplCopyWith<_$AddProductFailureImpl> get copyWith =>
      __$$AddProductFailureImplCopyWithImpl<_$AddProductFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return addProductFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return addProductFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addProductFailure != null) {
      return addProductFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return addProductFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return addProductFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (addProductFailure != null) {
      return addProductFailure(this);
    }
    return orElse();
  }
}

abstract class AddProductFailure implements MenuState {
  const factory AddProductFailure(final String message) =
      _$AddProductFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddProductFailureImplCopyWith<_$AddProductFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateProductSuccessImplCopyWith<$Res> {
  factory _$$UpdateProductSuccessImplCopyWith(_$UpdateProductSuccessImpl value,
          $Res Function(_$UpdateProductSuccessImpl) then) =
      __$$UpdateProductSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateProductSuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$UpdateProductSuccessImpl>
    implements _$$UpdateProductSuccessImplCopyWith<$Res> {
  __$$UpdateProductSuccessImplCopyWithImpl(_$UpdateProductSuccessImpl _value,
      $Res Function(_$UpdateProductSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateProductSuccessImpl implements UpdateProductSuccess {
  const _$UpdateProductSuccessImpl();

  @override
  String toString() {
    return 'MenuState.updateProductSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateProductSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return updateProductSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return updateProductSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateProductSuccess != null) {
      return updateProductSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return updateProductSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return updateProductSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateProductSuccess != null) {
      return updateProductSuccess(this);
    }
    return orElse();
  }
}

abstract class UpdateProductSuccess implements MenuState {
  const factory UpdateProductSuccess() = _$UpdateProductSuccessImpl;
}

/// @nodoc
abstract class _$$UpdateProductFailureImplCopyWith<$Res> {
  factory _$$UpdateProductFailureImplCopyWith(_$UpdateProductFailureImpl value,
          $Res Function(_$UpdateProductFailureImpl) then) =
      __$$UpdateProductFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UpdateProductFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$UpdateProductFailureImpl>
    implements _$$UpdateProductFailureImplCopyWith<$Res> {
  __$$UpdateProductFailureImplCopyWithImpl(_$UpdateProductFailureImpl _value,
      $Res Function(_$UpdateProductFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$UpdateProductFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateProductFailureImpl implements UpdateProductFailure {
  const _$UpdateProductFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.updateProductFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateProductFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateProductFailureImplCopyWith<_$UpdateProductFailureImpl>
      get copyWith =>
          __$$UpdateProductFailureImplCopyWithImpl<_$UpdateProductFailureImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return updateProductFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return updateProductFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateProductFailure != null) {
      return updateProductFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return updateProductFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return updateProductFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (updateProductFailure != null) {
      return updateProductFailure(this);
    }
    return orElse();
  }
}

abstract class UpdateProductFailure implements MenuState {
  const factory UpdateProductFailure(final String message) =
      _$UpdateProductFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateProductFailureImplCopyWith<_$UpdateProductFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteProductSuccessImplCopyWith<$Res> {
  factory _$$DeleteProductSuccessImplCopyWith(_$DeleteProductSuccessImpl value,
          $Res Function(_$DeleteProductSuccessImpl) then) =
      __$$DeleteProductSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteProductSuccessImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$DeleteProductSuccessImpl>
    implements _$$DeleteProductSuccessImplCopyWith<$Res> {
  __$$DeleteProductSuccessImplCopyWithImpl(_$DeleteProductSuccessImpl _value,
      $Res Function(_$DeleteProductSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteProductSuccessImpl implements DeleteProductSuccess {
  const _$DeleteProductSuccessImpl();

  @override
  String toString() {
    return 'MenuState.deleteProductSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteProductSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return deleteProductSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return deleteProductSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteProductSuccess != null) {
      return deleteProductSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return deleteProductSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return deleteProductSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteProductSuccess != null) {
      return deleteProductSuccess(this);
    }
    return orElse();
  }
}

abstract class DeleteProductSuccess implements MenuState {
  const factory DeleteProductSuccess() = _$DeleteProductSuccessImpl;
}

/// @nodoc
abstract class _$$DeleteProductFailureImplCopyWith<$Res> {
  factory _$$DeleteProductFailureImplCopyWith(_$DeleteProductFailureImpl value,
          $Res Function(_$DeleteProductFailureImpl) then) =
      __$$DeleteProductFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DeleteProductFailureImplCopyWithImpl<$Res>
    extends _$MenuStateCopyWithImpl<$Res, _$DeleteProductFailureImpl>
    implements _$$DeleteProductFailureImplCopyWith<$Res> {
  __$$DeleteProductFailureImplCopyWithImpl(_$DeleteProductFailureImpl _value,
      $Res Function(_$DeleteProductFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$DeleteProductFailureImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteProductFailureImpl implements DeleteProductFailure {
  const _$DeleteProductFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'MenuState.deleteProductFailure(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteProductFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteProductFailureImplCopyWith<_$DeleteProductFailureImpl>
      get copyWith =>
          __$$DeleteProductFailureImplCopyWithImpl<_$DeleteProductFailureImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() addCategorySuccess,
    required TResult Function(String message) addCategoryFailure,
    required TResult Function() getCategoriesSuccess,
    required TResult Function(String message) getCategoriesFailure,
    required TResult Function() deleteCategorySuccess,
    required TResult Function(String message) deleteCategoryFailure,
    required TResult Function() updateCategorySuccess,
    required TResult Function(String message) updateCategoryFailure,
    required TResult Function() getProductsSuccess,
    required TResult Function(String message) getProducrsFailure,
    required TResult Function() addProductSuccess,
    required TResult Function(String message) addProductFailure,
    required TResult Function() updateProductSuccess,
    required TResult Function(String message) updateProductFailure,
    required TResult Function() deleteProductSuccess,
    required TResult Function(String message) deleteProductFailure,
  }) {
    return deleteProductFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? addCategorySuccess,
    TResult? Function(String message)? addCategoryFailure,
    TResult? Function()? getCategoriesSuccess,
    TResult? Function(String message)? getCategoriesFailure,
    TResult? Function()? deleteCategorySuccess,
    TResult? Function(String message)? deleteCategoryFailure,
    TResult? Function()? updateCategorySuccess,
    TResult? Function(String message)? updateCategoryFailure,
    TResult? Function()? getProductsSuccess,
    TResult? Function(String message)? getProducrsFailure,
    TResult? Function()? addProductSuccess,
    TResult? Function(String message)? addProductFailure,
    TResult? Function()? updateProductSuccess,
    TResult? Function(String message)? updateProductFailure,
    TResult? Function()? deleteProductSuccess,
    TResult? Function(String message)? deleteProductFailure,
  }) {
    return deleteProductFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? addCategorySuccess,
    TResult Function(String message)? addCategoryFailure,
    TResult Function()? getCategoriesSuccess,
    TResult Function(String message)? getCategoriesFailure,
    TResult Function()? deleteCategorySuccess,
    TResult Function(String message)? deleteCategoryFailure,
    TResult Function()? updateCategorySuccess,
    TResult Function(String message)? updateCategoryFailure,
    TResult Function()? getProductsSuccess,
    TResult Function(String message)? getProducrsFailure,
    TResult Function()? addProductSuccess,
    TResult Function(String message)? addProductFailure,
    TResult Function()? updateProductSuccess,
    TResult Function(String message)? updateProductFailure,
    TResult Function()? deleteProductSuccess,
    TResult Function(String message)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteProductFailure != null) {
      return deleteProductFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(AddCategorySuccess value) addCategorySuccess,
    required TResult Function(AddCategoryFaiulre value) addCategoryFailure,
    required TResult Function(GetCategoriesSuccess value) getCategoriesSuccess,
    required TResult Function(GetCategoriesFailure value) getCategoriesFailure,
    required TResult Function(DeleteCategorySuccess value)
        deleteCategorySuccess,
    required TResult Function(DeleteCategoryFailure value)
        deleteCategoryFailure,
    required TResult Function(UpdateCategorySuccess value)
        updateCategorySuccess,
    required TResult Function(UpdateCategoryFailure value)
        updateCategoryFailure,
    required TResult Function(GetProductsSuccess value) getProductsSuccess,
    required TResult Function(GetProducrsFailure value) getProducrsFailure,
    required TResult Function(AddProductSuccess value) addProductSuccess,
    required TResult Function(AddProductFailure value) addProductFailure,
    required TResult Function(UpdateProductSuccess value) updateProductSuccess,
    required TResult Function(UpdateProductFailure value) updateProductFailure,
    required TResult Function(DeleteProductSuccess value) deleteProductSuccess,
    required TResult Function(DeleteProductFailure value) deleteProductFailure,
  }) {
    return deleteProductFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(AddCategorySuccess value)? addCategorySuccess,
    TResult? Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult? Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult? Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult? Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult? Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult? Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult? Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult? Function(GetProductsSuccess value)? getProductsSuccess,
    TResult? Function(GetProducrsFailure value)? getProducrsFailure,
    TResult? Function(AddProductSuccess value)? addProductSuccess,
    TResult? Function(AddProductFailure value)? addProductFailure,
    TResult? Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult? Function(UpdateProductFailure value)? updateProductFailure,
    TResult? Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult? Function(DeleteProductFailure value)? deleteProductFailure,
  }) {
    return deleteProductFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(AddCategorySuccess value)? addCategorySuccess,
    TResult Function(AddCategoryFaiulre value)? addCategoryFailure,
    TResult Function(GetCategoriesSuccess value)? getCategoriesSuccess,
    TResult Function(GetCategoriesFailure value)? getCategoriesFailure,
    TResult Function(DeleteCategorySuccess value)? deleteCategorySuccess,
    TResult Function(DeleteCategoryFailure value)? deleteCategoryFailure,
    TResult Function(UpdateCategorySuccess value)? updateCategorySuccess,
    TResult Function(UpdateCategoryFailure value)? updateCategoryFailure,
    TResult Function(GetProductsSuccess value)? getProductsSuccess,
    TResult Function(GetProducrsFailure value)? getProducrsFailure,
    TResult Function(AddProductSuccess value)? addProductSuccess,
    TResult Function(AddProductFailure value)? addProductFailure,
    TResult Function(UpdateProductSuccess value)? updateProductSuccess,
    TResult Function(UpdateProductFailure value)? updateProductFailure,
    TResult Function(DeleteProductSuccess value)? deleteProductSuccess,
    TResult Function(DeleteProductFailure value)? deleteProductFailure,
    required TResult orElse(),
  }) {
    if (deleteProductFailure != null) {
      return deleteProductFailure(this);
    }
    return orElse();
  }
}

abstract class DeleteProductFailure implements MenuState {
  const factory DeleteProductFailure(final String message) =
      _$DeleteProductFailureImpl;

  String get message;

  /// Create a copy of MenuState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteProductFailureImplCopyWith<_$DeleteProductFailureImpl>
      get copyWith => throw _privateConstructorUsedError;
}
