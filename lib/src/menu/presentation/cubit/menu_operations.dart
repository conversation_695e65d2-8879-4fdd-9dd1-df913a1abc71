import 'package:flutter/material.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/data/models/category_params.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/menu/data/models/products_params.dart';

mixin MenuOperations {
  TextEditingController? categoryNameController;
  TextEditingController? productNameController;
  TextEditingController? productPriceController;
  List<int> selectedHalls = [];
  String? productIcon;

  String? categoryIcon;
  List<CafeCategoryModel>? categories;
  int selectedCategoryId = 0;
  List<CafeProductModel>? products;

  CategoryParams createCategoryParams() => CategoryParams(
        name: categoryNameController!.text,
        icon: categoryIcon ?? '',
      );
  CafeProductParams createCafeProductParams() => CafeProductParams(
      hallId: injector<HallsCubit>().gethallData()?.id ?? 0,
      cafeCategoryId: selectedCategoryId,
      name: productNameController?.text ?? '',
      price: double.parse(productPriceController?.text ?? '0.0'),
      icon: productIcon ?? '',
      halls: selectedHalls);
  void initMenuControllers() {
    categoryNameController = TextEditingController();
  }

  void initProductControllers() {
    productNameController = TextEditingController();
    productPriceController = TextEditingController();
  }

  Widget returnedIcon(String icon) {
    switch (icon) {
      case 'hotDrinks':
        return Assets.icons.hot.svg();
      case 'coldDrinks':
        return Assets.icons.cold.svg();
      case 'fizzy':
        return Assets.icons.fizzy.svg();
      case 'meals':
        return Assets.icons.meals.svg();
      case 'desserts':
        return Assets.icons.dessert.svg();
      case 'soups':
        return Assets.icons.soup.svg();
      case 'sandwiches':
        return Assets.icons.sandwich.svg();
      case 'pasta':
        return Assets.icons.pasta.svg();
      default:
        return Assets.icons.pasta.svg();
    }
  }

  void disposeMenuControllers() {
    categoryNameController?.dispose();
    categoryIcon = null;
  }
  void disposeProductControllers() {
    productNameController?.dispose();
    productPriceController?.dispose();
  }

  void clearProductData() {
    productNameController?.text = '';
    productPriceController?.text = '0.0';
    selectedHalls = [];
    productIcon = '';
  }
}
