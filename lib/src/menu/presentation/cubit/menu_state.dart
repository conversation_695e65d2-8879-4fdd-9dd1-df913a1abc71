part of 'menu_cubit.dart';

@freezed
class MenuState with _$MenuState {
  const factory MenuState.initial() = _Initial;
  const factory MenuState.loading() = Loading;
  const factory MenuState.addCategorySuccess() = AddCategorySuccess;
  const factory MenuState.addCategoryFailure(String message) =
      AddCategoryFaiulre;
  const factory MenuState.getCategoriesSuccess() = GetCategoriesSuccess;
  const factory MenuState.getCategoriesFailure(String message) =
      GetCategoriesFailure;
  const factory MenuState.deleteCategorySuccess() = DeleteCategorySuccess;
  const factory MenuState.deleteCategoryFailure(String message) =
      DeleteCategoryFailure;
  const factory MenuState.updateCategorySuccess() = UpdateCategorySuccess;
  const factory MenuState.updateCategoryFailure(String message) =
      UpdateCategoryFailure;
  const factory MenuState.getProductsSuccess() = GetProductsSuccess;
  const factory MenuState.getProducrsFailure(String message) =
      GetProducrsFailure;

  const factory MenuState.addProductSuccess() = AddProductSuccess;

   const factory MenuState.addProductFailure(String message) =AddProductFailure;

   const factory MenuState.updateProductSuccess() = UpdateProductSuccess;

   const factory MenuState.updateProductFailure(String message) =UpdateProductFailure;

  const factory MenuState.deleteProductSuccess() = DeleteProductSuccess;

  const factory MenuState.deleteProductFailure(String message) =DeleteProductFailure;
}
