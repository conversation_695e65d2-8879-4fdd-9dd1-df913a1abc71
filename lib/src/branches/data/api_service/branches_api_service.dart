import 'package:dio/dio.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/branches/data/models/hall_response.dart';
import 'package:retrofit/retrofit.dart';
part 'branches_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class BranchesApiService {
  factory BranchesApiService(
    Dio dio, {
    String baseUrl,
  }) = _BranchesApiService;

  @POST(EndPoints.hall)
  @MultiPart()
  Future<HallResponse> addHall(@Body() FormData params);
  @GET(EndPoints.hall)
  Future<GetHallsResponse> getHalls();
  @POST('${EndPoints.hall}/{id}/delete')
  Future<HallResponse> deleteHall(@Path('id') int id);
  @POST('${EndPoints.hall}/{id}/update')
  Future<HallResponse> updateHall(@Path('id') int id, @Body() FormData params);
}
