import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
part 'hall_response.g.dart';
@JsonSerializable()
class HallResponse extends BaseResponse {
  final HallModel? data;

  HallResponse({this.data, required super.errors, required super.message});

  factory HallResponse.fromJson(Map<String, dynamic> json) =>
      _$HallResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$HallResponseToJson(this);    
}
@JsonSerializable()
class GetHallsResponse extends BaseResponse {
  final List<HallModel>? data;

  GetHallsResponse({this.data, required super.errors, required super.message});

  factory GetHallsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetHallsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetHallsResponseToJson(this);    
}
