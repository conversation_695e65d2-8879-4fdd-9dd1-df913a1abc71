// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hall_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HallResponse _$HallResponse<PERSON>rom<PERSON>son(Map<String, dynamic> json) => HallResponse(
      data: json['data'] == null
          ? null
          : HallModel.fromJson(json['data'] as Map<String, dynamic>),
      errors: json['errors'],
      message: json['message'] as String?,
    );

Map<String, dynamic> _$HallResponseToJson(HallResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };

GetHallsResponse _$GetHallsResponseFromJson(Map<String, dynamic> json) =>
    GetHallsResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => HallModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      errors: json['errors'],
      message: json['message'] as String?,
    );

Map<String, dynamic> _$GetHallsResponseToJson(GetHallsResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };
