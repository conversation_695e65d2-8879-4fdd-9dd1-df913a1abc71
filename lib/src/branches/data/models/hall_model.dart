// To parse this JSON data, do
//
//     final hallModel = hallModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

import 'package:r2/src/splash/data/models/splash_model.dart';

part 'hall_model.freezed.dart';
part 'hall_model.g.dart';

HallModel hallModelFromJson(String str) => HallModel.fromJson(json.decode(str));

String hallModelToJson(HallModel data) => json.encode(data.toJson());

@freezed
class HallModel with _$HallModel {
    const factory HallModel({
        @Json<PERSON>ey(name: "id")
        int? id,
        @Json<PERSON>ey(name: "city_id")
        int? cityId,
        @Json<PERSON>ey(name: "name")
        String? name,
        @JsonKey(name: "phone_dialing_code")
        String? phoneDialingCode,
        @JsonKey(name: "phone")
        String? phone,
        @<PERSON>son<PERSON>ey(name: "whatsapp_dialing_code")
        String? whatsappDialingCode,
        @Json<PERSON>ey(name: "whatsapp")
        String? whatsapp,
        @J<PERSON><PERSON><PERSON>(name: "lat")
        String? lat,
        @J<PERSON><PERSON><PERSON>(name: "lng")
        String? lng,
        @JsonKey(name: "address")
        String? address,
        @JsonKey(name: "available_rooms_count")
        int? availableRoomsCount,
        @JsonKey(name: "reserved_rooms_count")
        int? reservedRoomsCount,
        @JsonKey(name: "city")
        CityModel? city,
        @JsonKey(name: "services")
        List<ServiceModel>? services,
        @JsonKey(name: "image")
        String? image
    }) = _HallModel;

    factory HallModel.fromJson(Map<String, dynamic> json) => _$HallModelFromJson(json);
}





