// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hall_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HallParams _$HallParamsFromJson(Map<String, dynamic> json) => HallParams(
      cityId: (json['city_id'] as num).toInt(),
      services: (json['services'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      name: json['name'] as String,
      phoneDialingCode: json['phone_dialing_code'] as String,
      phone: json['phone'] as String,
      whatsappDialingCode: json['whatsapp_dialing_code'] as String,
      whatsapp: json['whatsapp'] as String,
      lat: json['lat'] as String,
      lng: json['lng'] as String,
      address: json['address'] as String,
      image: json['image'] as String?,
    );

Map<String, dynamic> _$HallParamsToJson(HallParams instance) =>
    <String, dynamic>{
      'city_id': instance.cityId,
      'services': AppHelper.formDataListToJson(instance.services),
      'name': instance.name,
      'phone_dialing_code': instance.phoneDialingCode,
      'phone': instance.phone,
      'whatsapp_dialing_code': instance.whatsappDialingCode,
      'whatsapp': instance.whatsapp,
      'lat': instance.lat,
      'lng': instance.lng,
      'address': instance.address,
      'image': AppHelper.imageToJson(instance.image),
    };
