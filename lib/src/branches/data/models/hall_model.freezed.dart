// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hall_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HallModel _$HallModelFromJson(Map<String, dynamic> json) {
  return _HallModel.fromJson(json);
}

/// @nodoc
mixin _$HallModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "city_id")
  int? get cityId => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "phone_dialing_code")
  String? get phoneDialingCode => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "whatsapp_dialing_code")
  String? get whatsappDialingCode => throw _privateConstructorUsedError;
  @JsonKey(name: "whatsapp")
  String? get whatsapp => throw _privateConstructorUsedError;
  @JsonKey(name: "lat")
  String? get lat => throw _privateConstructorUsedError;
  @JsonKey(name: "lng")
  String? get lng => throw _privateConstructorUsedError;
  @JsonKey(name: "address")
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: "available_rooms_count")
  int? get availableRoomsCount => throw _privateConstructorUsedError;
  @JsonKey(name: "reserved_rooms_count")
  int? get reservedRoomsCount => throw _privateConstructorUsedError;
  @JsonKey(name: "city")
  CityModel? get city => throw _privateConstructorUsedError;
  @JsonKey(name: "services")
  List<ServiceModel>? get services => throw _privateConstructorUsedError;
  @JsonKey(name: "image")
  String? get image => throw _privateConstructorUsedError;

  /// Serializes this HallModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HallModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HallModelCopyWith<HallModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HallModelCopyWith<$Res> {
  factory $HallModelCopyWith(HallModel value, $Res Function(HallModel) then) =
      _$HallModelCopyWithImpl<$Res, HallModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "city_id") int? cityId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "phone_dialing_code") String? phoneDialingCode,
      @JsonKey(name: "phone") String? phone,
      @JsonKey(name: "whatsapp_dialing_code") String? whatsappDialingCode,
      @JsonKey(name: "whatsapp") String? whatsapp,
      @JsonKey(name: "lat") String? lat,
      @JsonKey(name: "lng") String? lng,
      @JsonKey(name: "address") String? address,
      @JsonKey(name: "available_rooms_count") int? availableRoomsCount,
      @JsonKey(name: "reserved_rooms_count") int? reservedRoomsCount,
      @JsonKey(name: "city") CityModel? city,
      @JsonKey(name: "services") List<ServiceModel>? services,
      @JsonKey(name: "image") String? image});

  $CityModelCopyWith<$Res>? get city;
}

/// @nodoc
class _$HallModelCopyWithImpl<$Res, $Val extends HallModel>
    implements $HallModelCopyWith<$Res> {
  _$HallModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HallModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cityId = freezed,
    Object? name = freezed,
    Object? phoneDialingCode = freezed,
    Object? phone = freezed,
    Object? whatsappDialingCode = freezed,
    Object? whatsapp = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
    Object? address = freezed,
    Object? availableRoomsCount = freezed,
    Object? reservedRoomsCount = freezed,
    Object? city = freezed,
    Object? services = freezed,
    Object? image = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneDialingCode: freezed == phoneDialingCode
          ? _value.phoneDialingCode
          : phoneDialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      whatsappDialingCode: freezed == whatsappDialingCode
          ? _value.whatsappDialingCode
          : whatsappDialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      whatsapp: freezed == whatsapp
          ? _value.whatsapp
          : whatsapp // ignore: cast_nullable_to_non_nullable
              as String?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      availableRoomsCount: freezed == availableRoomsCount
          ? _value.availableRoomsCount
          : availableRoomsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      reservedRoomsCount: freezed == reservedRoomsCount
          ? _value.reservedRoomsCount
          : reservedRoomsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as CityModel?,
      services: freezed == services
          ? _value.services
          : services // ignore: cast_nullable_to_non_nullable
              as List<ServiceModel>?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of HallModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CityModelCopyWith<$Res>? get city {
    if (_value.city == null) {
      return null;
    }

    return $CityModelCopyWith<$Res>(_value.city!, (value) {
      return _then(_value.copyWith(city: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HallModelImplCopyWith<$Res>
    implements $HallModelCopyWith<$Res> {
  factory _$$HallModelImplCopyWith(
          _$HallModelImpl value, $Res Function(_$HallModelImpl) then) =
      __$$HallModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "city_id") int? cityId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "phone_dialing_code") String? phoneDialingCode,
      @JsonKey(name: "phone") String? phone,
      @JsonKey(name: "whatsapp_dialing_code") String? whatsappDialingCode,
      @JsonKey(name: "whatsapp") String? whatsapp,
      @JsonKey(name: "lat") String? lat,
      @JsonKey(name: "lng") String? lng,
      @JsonKey(name: "address") String? address,
      @JsonKey(name: "available_rooms_count") int? availableRoomsCount,
      @JsonKey(name: "reserved_rooms_count") int? reservedRoomsCount,
      @JsonKey(name: "city") CityModel? city,
      @JsonKey(name: "services") List<ServiceModel>? services,
      @JsonKey(name: "image") String? image});

  @override
  $CityModelCopyWith<$Res>? get city;
}

/// @nodoc
class __$$HallModelImplCopyWithImpl<$Res>
    extends _$HallModelCopyWithImpl<$Res, _$HallModelImpl>
    implements _$$HallModelImplCopyWith<$Res> {
  __$$HallModelImplCopyWithImpl(
      _$HallModelImpl _value, $Res Function(_$HallModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? cityId = freezed,
    Object? name = freezed,
    Object? phoneDialingCode = freezed,
    Object? phone = freezed,
    Object? whatsappDialingCode = freezed,
    Object? whatsapp = freezed,
    Object? lat = freezed,
    Object? lng = freezed,
    Object? address = freezed,
    Object? availableRoomsCount = freezed,
    Object? reservedRoomsCount = freezed,
    Object? city = freezed,
    Object? services = freezed,
    Object? image = freezed,
  }) {
    return _then(_$HallModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      cityId: freezed == cityId
          ? _value.cityId
          : cityId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneDialingCode: freezed == phoneDialingCode
          ? _value.phoneDialingCode
          : phoneDialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      whatsappDialingCode: freezed == whatsappDialingCode
          ? _value.whatsappDialingCode
          : whatsappDialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      whatsapp: freezed == whatsapp
          ? _value.whatsapp
          : whatsapp // ignore: cast_nullable_to_non_nullable
              as String?,
      lat: freezed == lat
          ? _value.lat
          : lat // ignore: cast_nullable_to_non_nullable
              as String?,
      lng: freezed == lng
          ? _value.lng
          : lng // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      availableRoomsCount: freezed == availableRoomsCount
          ? _value.availableRoomsCount
          : availableRoomsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      reservedRoomsCount: freezed == reservedRoomsCount
          ? _value.reservedRoomsCount
          : reservedRoomsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as CityModel?,
      services: freezed == services
          ? _value._services
          : services // ignore: cast_nullable_to_non_nullable
              as List<ServiceModel>?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HallModelImpl implements _HallModel {
  const _$HallModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "city_id") this.cityId,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "phone_dialing_code") this.phoneDialingCode,
      @JsonKey(name: "phone") this.phone,
      @JsonKey(name: "whatsapp_dialing_code") this.whatsappDialingCode,
      @JsonKey(name: "whatsapp") this.whatsapp,
      @JsonKey(name: "lat") this.lat,
      @JsonKey(name: "lng") this.lng,
      @JsonKey(name: "address") this.address,
      @JsonKey(name: "available_rooms_count") this.availableRoomsCount,
      @JsonKey(name: "reserved_rooms_count") this.reservedRoomsCount,
      @JsonKey(name: "city") this.city,
      @JsonKey(name: "services") final List<ServiceModel>? services,
      @JsonKey(name: "image") this.image})
      : _services = services;

  factory _$HallModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$HallModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "city_id")
  final int? cityId;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "phone_dialing_code")
  final String? phoneDialingCode;
  @override
  @JsonKey(name: "phone")
  final String? phone;
  @override
  @JsonKey(name: "whatsapp_dialing_code")
  final String? whatsappDialingCode;
  @override
  @JsonKey(name: "whatsapp")
  final String? whatsapp;
  @override
  @JsonKey(name: "lat")
  final String? lat;
  @override
  @JsonKey(name: "lng")
  final String? lng;
  @override
  @JsonKey(name: "address")
  final String? address;
  @override
  @JsonKey(name: "available_rooms_count")
  final int? availableRoomsCount;
  @override
  @JsonKey(name: "reserved_rooms_count")
  final int? reservedRoomsCount;
  @override
  @JsonKey(name: "city")
  final CityModel? city;
  final List<ServiceModel>? _services;
  @override
  @JsonKey(name: "services")
  List<ServiceModel>? get services {
    final value = _services;
    if (value == null) return null;
    if (_services is EqualUnmodifiableListView) return _services;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: "image")
  final String? image;

  @override
  String toString() {
    return 'HallModel(id: $id, cityId: $cityId, name: $name, phoneDialingCode: $phoneDialingCode, phone: $phone, whatsappDialingCode: $whatsappDialingCode, whatsapp: $whatsapp, lat: $lat, lng: $lng, address: $address, availableRoomsCount: $availableRoomsCount, reservedRoomsCount: $reservedRoomsCount, city: $city, services: $services, image: $image)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HallModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityId, cityId) || other.cityId == cityId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneDialingCode, phoneDialingCode) ||
                other.phoneDialingCode == phoneDialingCode) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.whatsappDialingCode, whatsappDialingCode) ||
                other.whatsappDialingCode == whatsappDialingCode) &&
            (identical(other.whatsapp, whatsapp) ||
                other.whatsapp == whatsapp) &&
            (identical(other.lat, lat) || other.lat == lat) &&
            (identical(other.lng, lng) || other.lng == lng) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.availableRoomsCount, availableRoomsCount) ||
                other.availableRoomsCount == availableRoomsCount) &&
            (identical(other.reservedRoomsCount, reservedRoomsCount) ||
                other.reservedRoomsCount == reservedRoomsCount) &&
            (identical(other.city, city) || other.city == city) &&
            const DeepCollectionEquality().equals(other._services, _services) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      cityId,
      name,
      phoneDialingCode,
      phone,
      whatsappDialingCode,
      whatsapp,
      lat,
      lng,
      address,
      availableRoomsCount,
      reservedRoomsCount,
      city,
      const DeepCollectionEquality().hash(_services),
      image);

  /// Create a copy of HallModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HallModelImplCopyWith<_$HallModelImpl> get copyWith =>
      __$$HallModelImplCopyWithImpl<_$HallModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HallModelImplToJson(
      this,
    );
  }
}

abstract class _HallModel implements HallModel {
  const factory _HallModel(
      {@JsonKey(name: "id") final int? id,
      @JsonKey(name: "city_id") final int? cityId,
      @JsonKey(name: "name") final String? name,
      @JsonKey(name: "phone_dialing_code") final String? phoneDialingCode,
      @JsonKey(name: "phone") final String? phone,
      @JsonKey(name: "whatsapp_dialing_code") final String? whatsappDialingCode,
      @JsonKey(name: "whatsapp") final String? whatsapp,
      @JsonKey(name: "lat") final String? lat,
      @JsonKey(name: "lng") final String? lng,
      @JsonKey(name: "address") final String? address,
      @JsonKey(name: "available_rooms_count") final int? availableRoomsCount,
      @JsonKey(name: "reserved_rooms_count") final int? reservedRoomsCount,
      @JsonKey(name: "city") final CityModel? city,
      @JsonKey(name: "services") final List<ServiceModel>? services,
      @JsonKey(name: "image") final String? image}) = _$HallModelImpl;

  factory _HallModel.fromJson(Map<String, dynamic> json) =
      _$HallModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "city_id")
  int? get cityId;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "phone_dialing_code")
  String? get phoneDialingCode;
  @override
  @JsonKey(name: "phone")
  String? get phone;
  @override
  @JsonKey(name: "whatsapp_dialing_code")
  String? get whatsappDialingCode;
  @override
  @JsonKey(name: "whatsapp")
  String? get whatsapp;
  @override
  @JsonKey(name: "lat")
  String? get lat;
  @override
  @JsonKey(name: "lng")
  String? get lng;
  @override
  @JsonKey(name: "address")
  String? get address;
  @override
  @JsonKey(name: "available_rooms_count")
  int? get availableRoomsCount;
  @override
  @JsonKey(name: "reserved_rooms_count")
  int? get reservedRoomsCount;
  @override
  @JsonKey(name: "city")
  CityModel? get city;
  @override
  @JsonKey(name: "services")
  List<ServiceModel>? get services;
  @override
  @JsonKey(name: "image")
  String? get image;

  /// Create a copy of HallModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HallModelImplCopyWith<_$HallModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
