// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hall_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HallModelImpl _$$HallModelImplFromJson(Map<String, dynamic> json) =>
    _$HallModelImpl(
      id: (json['id'] as num?)?.toInt(),
      cityId: (json['city_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      phoneDialingCode: json['phone_dialing_code'] as String?,
      phone: json['phone'] as String?,
      whatsappDialingCode: json['whatsapp_dialing_code'] as String?,
      whatsapp: json['whatsapp'] as String?,
      lat: json['lat'] as String?,
      lng: json['lng'] as String?,
      address: json['address'] as String?,
      availableRoomsCount: (json['available_rooms_count'] as num?)?.toInt(),
      reservedRoomsCount: (json['reserved_rooms_count'] as num?)?.toInt(),
      city: json['city'] == null
          ? null
          : CityModel.fromJson(json['city'] as Map<String, dynamic>),
      services: (json['services'] as List<dynamic>?)
          ?.map((e) => ServiceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      image: json['image'] as String?,
    );

Map<String, dynamic> _$$HallModelImplToJson(_$HallModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'city_id': instance.cityId,
      'name': instance.name,
      'phone_dialing_code': instance.phoneDialingCode,
      'phone': instance.phone,
      'whatsapp_dialing_code': instance.whatsappDialingCode,
      'whatsapp': instance.whatsapp,
      'lat': instance.lat,
      'lng': instance.lng,
      'address': instance.address,
      'available_rooms_count': instance.availableRoomsCount,
      'reserved_rooms_count': instance.reservedRoomsCount,
      'city': instance.city,
      'services': instance.services,
      'image': instance.image,
    };
