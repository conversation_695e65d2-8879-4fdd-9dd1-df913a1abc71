// To parse this JSON data, do
//
//     final hallParams = hallParamsFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

import 'package:r2/core/helpers/app_helper.dart';

part 'hall_params.g.dart';

HallParams hallParamsFromJson(String str) => HallParams.fromJson(json.decode(str));

String hallParamsToJson(HallParams data) => json.encode(data.toJson());

@JsonSerializable()
class HallParams {
    @JsonKey(name: "city_id")
    int cityId;
    @<PERSON><PERSON><PERSON><PERSON>(name: "services", toJson: AppHelper.formDataListToJson)
    List<int> services;
    @JsonKey(name: "name")
    String name;
    @JsonKey(name: "phone_dialing_code")
    String phoneDialingCode;
    @JsonKey(name: "phone")
    String phone;
    @<PERSON><PERSON><PERSON><PERSON>(name: "whatsapp_dialing_code")
    String whatsappDialingCode;
    @Json<PERSON><PERSON>(name: "whatsapp")
    String whatsapp;
    @J<PERSON><PERSON><PERSON>(name: "lat")
    String lat;
    @Json<PERSON>ey(name: "lng")
    String lng;
    @JsonKey(name: "address")
    String address;
    @JsonKey(name: "image",toJson: AppHelper.imageToJson)
    String? image;
    HallParams({
        required this.cityId,
        required this.services,
        required this.name,
        required this.phoneDialingCode,
        required this.phone,
        required this.whatsappDialingCode,
        required this.whatsapp,
        required this.lat,
        required this.lng,
        required this.address,
        this.image
    });

    factory HallParams.fromJson(Map<String, dynamic> json) => _$HallParamsFromJson(json);

    Map<String, dynamic> toJson() => _$HallParamsToJson(this);
}
