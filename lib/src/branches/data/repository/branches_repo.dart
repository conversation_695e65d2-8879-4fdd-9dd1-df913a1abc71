import 'package:dio/dio.dart';
import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/branches/data/api_service/branches_api_service.dart';
import 'package:r2/src/branches/data/models/hall_params.dart';
import 'package:r2/src/branches/data/models/hall_response.dart';

class BranchesRepo {
  final BranchesApiService _branchesApiService;

  BranchesRepo(this._branchesApiService);
  Future<Result<HallResponse>> addHall(HallParams params) =>
      errorHandlerAsync(() async =>
          await _branchesApiService.addHall(FormData.fromMap(params.toJson())));
  Future<Result<GetHallsResponse>> getHalls() =>
      errorHandlerAsync(() async => await _branchesApiService.getHalls());
  Future<Result<HallResponse>> deleteHall(int id) =>
      errorHandlerAsync(() async => await _branchesApiService.deleteHall(id));
  Future<Result<HallResponse>> updateHall(int id, HallParams params) => 
      errorHandlerAsync(() async => await _branchesApiService.updateHall(id, FormData.fromMap(params.toJson())));   
}
