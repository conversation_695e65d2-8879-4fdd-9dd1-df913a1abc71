import 'package:flutter/material.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';

class AddCategoryItem extends StatefulWidget {
  final ServiceModel? service;
  const AddCategoryItem({super.key, this.service});

  @override
  State<AddCategoryItem> createState() => _AddCategoryItemState();
}

class _AddCategoryItemState extends State<AddCategoryItem> {
  @override
  Widget build(BuildContext context) {
    return  Row(
                    children: [
                      Checkbox(
                        fillColor:
                            WidgetStateProperty.all(AppColors.darkPurple),
                        checkColor: AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        value: injector<HallsCubit>()
                            .selectedCategoryIds
                            .contains(widget.service?.id),
                        onChanged: (value) {
                          setState(() {
                            if (value == true) {
                              injector<HallsCubit>()
                                  .selectedCategoryIds
                                  .add(widget.service?.id as int);
                            } else {
                              injector<HallsCubit>()
                                  .selectedCategoryIds
                                  .remove(widget.service?.id as int);
                            }
                          });
                        },
                      ),
                      Assets.images.ps.image(),
                      const SizedBox(width: 16),
                      Text(widget.service?.name ?? '-'),
                      const Spacer(),
                      Text(widget.service?.handle ?? '-'),
                    ],
                  );
  }
}