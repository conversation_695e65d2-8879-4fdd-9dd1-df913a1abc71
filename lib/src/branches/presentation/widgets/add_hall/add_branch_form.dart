import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_drop_down_menu.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/default_country_code_picker.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/branches/presentation/widgets/add_hall/add_category_item.dart';
import 'package:r2/src/splash/data/models/splash_model.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';

class AddBranchForm extends StatefulWidget {
  final HallModel? hall;

  const AddBranchForm({super.key, this.hall});

  @override
  State<AddBranchForm> createState() => _AddBranchFormState();
}

class _AddBranchFormState extends State<AddBranchForm> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  List<ServiceModel>? services;
  List<CountryModel>? countries;

  void onSubmit() async {
    if (!formKey.currentState!.validate()) return;
    formKey.currentState!.save();
    if (widget.hall?.id == null) {
      injector<HallsCubit>().addHall();
    } else {
      injector<HallsCubit>().updateHall(widget.hall?.id as int);
    }
    // context.router.push(const BranchesRoute());
  }

  File? _image;

  Future<void> _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      setState(() {
        _image = File(pickedFile.path);
        injector<HallsCubit>().image = _image;
      });
    }
  }

  Future<void> _showImagePickerOptions() async {
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: AppColors.darkPurple,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            ListTile(
              leading: const Icon(
                Icons.photo_library,
                color: AppColors.primary,
              ),
              title: const Text('Choose from Gallery'),
              onTap: () {
                _pickImage(ImageSource.gallery);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.photo_camera,
                color: AppColors.primary,
              ),
              title: const Text('Take a Photo'),
              onTap: () {
                _pickImage(ImageSource.camera);
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void initState() {
    services = injector<SplashCubit>().splashModel?.services;
    countries = injector<SplashCubit>().splashModel?.countries;
    setCtrls();
    super.initState();
  }

  setCtrls() {
    if (widget.hall?.id != null) {
      injector<HallsCubit>().initHallsControllers();
      injector<HallsCubit>().nameController!.text = widget.hall!.name!;
      injector<HallsCubit>().addressController!.text = widget.hall!.address!;
      injector<HallsCubit>().phoneController!.text = widget.hall!.phone!;
      injector<HallsCubit>().whatsappController!.text = widget.hall!.whatsapp!;
      injector<HallsCubit>().whatsappDialingCode =
          widget.hall!.whatsappDialingCode!;
      injector<HallsCubit>().cityId = widget.hall?.cityId;
      injector<HallsCubit>().selectedCategoryIds =
          widget.hall!.services!.map((e) => e.id!).toList();
      selectedCountryId = widget.hall!.city?.countryId;
      filteredCities = countries!
              .firstWhere((country) => country.id == selectedCountryId)
              .cities ??
          [];

    }
  }

  int? selectedCountryId;
  List<CityModel> filteredCities = [];
  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabeledField(
            field: CustomTextField(
              controller: injector<HallsCubit>().nameController,
              hintText: context.l10n.branchName,
              validator: (value) {
                if (value != null && value.isEmpty) {
                  return context.l10n.pleaseEnterHallName;
                }
                return null;
              },
            ),
            title: context.l10n.branchName,
          ),
          const SizedBox(height: 16),
          Text(
            context.l10n.category,
            style: TextStyles.body14,
          ),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(services!.length, (index) {
              final service = services![index];
              return Column(
                children: [
                  AddCategoryItem(service: service),
                  if (index != services!.length - 1) const SizedBox(height: 8),
                ],
              );
            }),
          ),
          const SizedBox(height: 16),
          LabeledField(
            field: SizedBox(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: CustomTextField(
                      controller: injector<HallsCubit>().phoneController,
                      keyboardType: TextInputType.phone,
                      onSaved: (value) {},
                      validator: (value) {
                        if (value != null && value.isEmpty) {
                          return context.l10n.pleaseEnterValidUserPhone;
                        }
                        return null;
                      },
                      labelText: context.l10n.phoneNumber,
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Expanded(child: DefaultCountryCodePicker(
                    onChanged: (value) {
                      injector<HallsCubit>().phoneDialingCode = value.dialCode;
                    },
                  ))
                ],
              ),
            ),
            title: context.l10n.phoneNumber,
          ),
          const SizedBox(height: 16),
          LabeledField(
            field: SizedBox(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: CustomTextField(
                      controller: injector<HallsCubit>().whatsappController,
                      keyboardType: TextInputType.phone,
                      onSaved: (value) {},
                      validator: (value) {
                        if (value != null && value.isEmpty) {
                          return context.l10n.pleaseEnterValidUserPhone;
                        }
                        return null;
                      },
                      labelText: context.l10n.whatsappNumber,
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Expanded(child: DefaultCountryCodePicker(
                    onChanged: (value) {
                      injector<HallsCubit>().whatsappDialingCode =
                          value.dialCode;
                    },
                  ))
                ],
              ),
            ),
            title: context.l10n.whatsappNumber,
          ),
          const SizedBox(height: 16),
          if (widget.hall?.id == null) ...[
            LabeledField(
              title: context.l10n.country,
              field: CustomDropDownButton(
                onChanged: (value) {
                  setState(() {
                    selectedCountryId = value;
                    // Filter cities based on selected country
                    filteredCities = countries!
                            .firstWhere(
                                (country) => country.id == selectedCountryId)
                            .cities ??
                        [];
                  });
                },
                onValidate: (value) {
                  if (value == null) {
                    return context.l10n.pleaseSelectCountry;
                  }
                  return null;
                },
                hintText: context.l10n.country,
                filled: true,
                items: countries!
                    .map(
                      (country) => DropdownMenuItem(
                        value: country.id,
                        child: Text(
                            country.name ?? '-'), 
                      ),
                    )
                    .toList(),
              ),
            ),
            const SizedBox(height: 16),
            LabeledField(
              key: Key('city'),
              title: context.l10n.city,
              field: CustomDropDownButton(
                onChanged: (value) {
                  injector<HallsCubit>().cityId = value?.id;
                },
                onValidate: (value) {
                  if (value == null) {
                    return context.l10n.pleaceSelectCity;
                  }
                  return null;
                },
                hintText: context.l10n.city,
                filled: true,
                items: filteredCities
                    .map(
                      (e) => DropdownMenuItem(
                        value: e,
                        child: Text(e.name ?? '-'),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
          const SizedBox(height: 16),
          LabeledField(
            field: CustomTextField(
              controller: injector<HallsCubit>().addressController,
              labelText: context.l10n.address,
              validator: (value) {
                if (value != null && value.isEmpty) {
                  return context.l10n.pleaseEnterAddress;
                }
                return null;
              },
            ),
            title: context.l10n.address,
          ),
          const SizedBox(height: 16),
          CustomElevatedButton(
            isLight: false,
            backgroundColor: Colors.transparent,
            borderColor: AppColors.primary,
            onPressed: onSubmit,
            child: Text(
              context.l10n.getLocationOnMap,
              style: TextStyles.body14.copyWith(color: AppColors.primary),
            ),
          ),
          const SizedBox(height: 16),
          LabeledField(
            title: context.l10n.branchLogo,
            field: GestureDetector(
              onTap: _showImagePickerOptions,
              child: DottedBorder(
                color: AppColors.darkPurple,
                strokeWidth: 2,
                dashPattern: [8, 8],
                borderType: BorderType.RRect,
                radius: const Radius.circular(8),
                child: SizedBox(
                  height: 100,
                  width: double.infinity,
                  child: _image == null 
                      ? Center(
                          child: Assets.icons.addLogoIcon.svg(),
                        )
                     
                          : Image.file(
                              _image!,
                              fit: BoxFit.fitHeight,
                            ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 64),
          CustomElevatedButton(
            onPressed: onSubmit,
            child: Text(context.l10n.startNow),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
