import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';

class BranchCard extends StatelessWidget {
  final HallModel? hall;
  const BranchCard({super.key, this.hall});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        injector<HallsCubit>().setHallData(hall);

        context.router.push(const BottomNavBarRoute());
      },
      child: Container(
        padding: EdgeInsets.only(
          top: 16,
          bottom: 12,
        ),
        decoration: BoxDecoration(
          color: AppColors.darkPurple,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 45,
                    width: 250,
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.card,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        bottomLeft: Radius.circular(12),
                      ),
                    ),
                    child: Text(
                      hall?.name ?? '-',
                      style: TextStyles.body16,
                    ),
                  ),
                  Row(
                    children: [
                      GestureDetector(
                          onTap: () => context.router.push(
                                AddBranchRoute(hall: hall),
                              ),
                          child: Assets.icons.editIcon.svg()),
                      const SizedBox(width: 20),
                      GestureDetector(
                        onTap: () => UiHelper.showCustomDialog(
                          barrierDismissible: false,
                          context: context,
                          dialog: AlertDialog(
                            backgroundColor: AppColors.darkPurple,
                            icon: Assets.icons.trashIcon2.svg(),
                            title: Text(
                              context.l10n.areYouSureYouWantDelete,
                              style: TextStyles.title16
                                  .copyWith(fontWeight: FontWeight.w700),
                            ),
                            titlePadding: EdgeInsets.only(top: 10),
                            actionsPadding: EdgeInsets.only(
                                top: 40, bottom: 24, right: 30, left: 30),
                            iconPadding: EdgeInsets.only(top: 30, bottom: 10),
                            actions: [
                              CustomElevatedButton(
                                width: 250,
                                isLight: false,
                                child: Text(context.l10n.yes),
                                onPressed: () async {
                                  await injector<HallsCubit>()
                                      .deleteHall(hall?.id ?? 0);
                                  if (context.mounted) {
                                    context.router.maybePop();
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              CustomElevatedButton(
                                width: 250,
                                isLight: false,
                                backgroundColor: AppColors.darkPurple,
                                borderColor: AppColors.primary,
                                child: Text(context.l10n.no),
                                onPressed: () => context.router.maybePop(),
                              )
                            ],
                          ),
                        ),
                        child: Assets.icons.trashIcon.svg(height: 20),
                      ),
                    ],
                  )
                ],
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Assets.icons.correctIconGreen.svg(),
                            const SizedBox(width: 8),
                            Text(
                                '${hall?.reservedRoomsCount} ${context.l10n.reservedRomms}',
                                style: TextStyles.body14)
                          ],
                        ),
                        const SizedBox(width: 30),
                        Row(
                          children: [
                            Text(
                              '${hall?.availableRoomsCount} ${context.l10n.availableRooms}',
                              style: TextStyles.body14
                                  .copyWith(color: AppColors.grey),
                            )
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Assets.icons.locationIcon.svg(),
                        const SizedBox(width: 8),
                        Text(
                          hall?.address ?? '-',
                          style:
                              TextStyles.body14.copyWith(color: AppColors.grey),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
