import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/gen/assets.gen.dart';

class R2Logo extends StatelessWidget {
  const R2Logo({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        margin: EdgeInsets.only(top: 20),
        padding: EdgeInsets.only(left: 20, top: 10, bottom: 10),
        height: .065.sh,
        width: .688.sw,
        decoration: BoxDecoration(
          color: AppColors.darkPurple,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(26),
            bottomLeft: Radius.circular(26),
          ),
        ),
        child: Assets.images.r2Logo.image(
          alignment: Alignment.centerLeft,
        ),
      ),
    );
  }
}
