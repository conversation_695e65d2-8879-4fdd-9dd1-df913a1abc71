import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';

class ProfileSection extends StatelessWidget {
  final String? name;
  const ProfileSection({
    super.key,
    this.name,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Assets.icons.profileIcon.image(),
      title: Text(
        context.l10n.welcome,
        style: TextStyles.body12.copyWith(
          fontWeight: FontWeight.w900,
        ),
      ),
      subtitle: Text(
        injector<AuthCubit>().getUserData()?.name ?? '-',
        style: TextStyles.body16,
      ),
      // trailing: Container(
      //   padding: const EdgeInsets.all(2),
      //   decoration: BoxDecoration(
      //     shape: BoxShape.circle,
      //     border: Border.all(color: AppColors.primary, width: 3),
      //   ),
      //   child: GestureDetector(
      //       onTap: () => context.router.push(const AddRoomRoute()),
      //       child: Icon(Icons.add, size: 28)),
      // ),
    );
  }
}
