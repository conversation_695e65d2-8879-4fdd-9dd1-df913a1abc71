// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'halls_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HallsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HallsStateCopyWith<$Res> {
  factory $HallsStateCopyWith(
          HallsState value, $Res Function(HallsState) then) =
      _$HallsStateCopyWithImpl<$Res, HallsState>;
}

/// @nodoc
class _$HallsStateCopyWithImpl<$Res, $Val extends HallsState>
    implements $HallsStateCopyWith<$Res> {
  _$HallsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'HallsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements HallsState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'HallsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements HallsState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$AddHallSuccessImplCopyWith<$Res> {
  factory _$$AddHallSuccessImplCopyWith(_$AddHallSuccessImpl value,
          $Res Function(_$AddHallSuccessImpl) then) =
      __$$AddHallSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({HallModel hall});

  $HallModelCopyWith<$Res> get hall;
}

/// @nodoc
class __$$AddHallSuccessImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$AddHallSuccessImpl>
    implements _$$AddHallSuccessImplCopyWith<$Res> {
  __$$AddHallSuccessImplCopyWithImpl(
      _$AddHallSuccessImpl _value, $Res Function(_$AddHallSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hall = null,
  }) {
    return _then(_$AddHallSuccessImpl(
      null == hall
          ? _value.hall
          : hall // ignore: cast_nullable_to_non_nullable
              as HallModel,
    ));
  }

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HallModelCopyWith<$Res> get hall {
    return $HallModelCopyWith<$Res>(_value.hall, (value) {
      return _then(_value.copyWith(hall: value));
    });
  }
}

/// @nodoc

class _$AddHallSuccessImpl implements _AddHallSuccess {
  const _$AddHallSuccessImpl(this.hall);

  @override
  final HallModel hall;

  @override
  String toString() {
    return 'HallsState.addHallSuccess(hall: $hall)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddHallSuccessImpl &&
            (identical(other.hall, hall) || other.hall == hall));
  }

  @override
  int get hashCode => Object.hash(runtimeType, hall);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddHallSuccessImplCopyWith<_$AddHallSuccessImpl> get copyWith =>
      __$$AddHallSuccessImplCopyWithImpl<_$AddHallSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return addHallSuccess(hall);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return addHallSuccess?.call(hall);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (addHallSuccess != null) {
      return addHallSuccess(hall);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return addHallSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return addHallSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (addHallSuccess != null) {
      return addHallSuccess(this);
    }
    return orElse();
  }
}

abstract class _AddHallSuccess implements HallsState {
  const factory _AddHallSuccess(final HallModel hall) = _$AddHallSuccessImpl;

  HallModel get hall;

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddHallSuccessImplCopyWith<_$AddHallSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddHallsFailedImplCopyWith<$Res> {
  factory _$$AddHallsFailedImplCopyWith(_$AddHallsFailedImpl value,
          $Res Function(_$AddHallsFailedImpl) then) =
      __$$AddHallsFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$AddHallsFailedImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$AddHallsFailedImpl>
    implements _$$AddHallsFailedImplCopyWith<$Res> {
  __$$AddHallsFailedImplCopyWithImpl(
      _$AddHallsFailedImpl _value, $Res Function(_$AddHallsFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$AddHallsFailedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddHallsFailedImpl implements _AddHallsFailed {
  const _$AddHallsFailedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'HallsState.addHallFailed(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddHallsFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddHallsFailedImplCopyWith<_$AddHallsFailedImpl> get copyWith =>
      __$$AddHallsFailedImplCopyWithImpl<_$AddHallsFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return addHallFailed(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return addHallFailed?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (addHallFailed != null) {
      return addHallFailed(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return addHallFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return addHallFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (addHallFailed != null) {
      return addHallFailed(this);
    }
    return orElse();
  }
}

abstract class _AddHallsFailed implements HallsState {
  const factory _AddHallsFailed(final String message) = _$AddHallsFailedImpl;

  String get message;

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddHallsFailedImplCopyWith<_$AddHallsFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetHallsSuccessImplCopyWith<$Res> {
  factory _$$GetHallsSuccessImplCopyWith(_$GetHallsSuccessImpl value,
          $Res Function(_$GetHallsSuccessImpl) then) =
      __$$GetHallsSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetHallsSuccessImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$GetHallsSuccessImpl>
    implements _$$GetHallsSuccessImplCopyWith<$Res> {
  __$$GetHallsSuccessImplCopyWithImpl(
      _$GetHallsSuccessImpl _value, $Res Function(_$GetHallsSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetHallsSuccessImpl implements _GetHallsSuccess {
  const _$GetHallsSuccessImpl();

  @override
  String toString() {
    return 'HallsState.getHallsSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetHallsSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return getHallsSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return getHallsSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (getHallsSuccess != null) {
      return getHallsSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return getHallsSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return getHallsSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (getHallsSuccess != null) {
      return getHallsSuccess(this);
    }
    return orElse();
  }
}

abstract class _GetHallsSuccess implements HallsState {
  const factory _GetHallsSuccess() = _$GetHallsSuccessImpl;
}

/// @nodoc
abstract class _$$GetHallsFailedImplCopyWith<$Res> {
  factory _$$GetHallsFailedImplCopyWith(_$GetHallsFailedImpl value,
          $Res Function(_$GetHallsFailedImpl) then) =
      __$$GetHallsFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$GetHallsFailedImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$GetHallsFailedImpl>
    implements _$$GetHallsFailedImplCopyWith<$Res> {
  __$$GetHallsFailedImplCopyWithImpl(
      _$GetHallsFailedImpl _value, $Res Function(_$GetHallsFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$GetHallsFailedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetHallsFailedImpl implements _GetHallsFailed {
  const _$GetHallsFailedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'HallsState.getHallsFailed(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetHallsFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetHallsFailedImplCopyWith<_$GetHallsFailedImpl> get copyWith =>
      __$$GetHallsFailedImplCopyWithImpl<_$GetHallsFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return getHallsFailed(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return getHallsFailed?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (getHallsFailed != null) {
      return getHallsFailed(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return getHallsFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return getHallsFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (getHallsFailed != null) {
      return getHallsFailed(this);
    }
    return orElse();
  }
}

abstract class _GetHallsFailed implements HallsState {
  const factory _GetHallsFailed(final String message) = _$GetHallsFailedImpl;

  String get message;

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetHallsFailedImplCopyWith<_$GetHallsFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteHallSuccessImplCopyWith<$Res> {
  factory _$$DeleteHallSuccessImplCopyWith(_$DeleteHallSuccessImpl value,
          $Res Function(_$DeleteHallSuccessImpl) then) =
      __$$DeleteHallSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteHallSuccessImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$DeleteHallSuccessImpl>
    implements _$$DeleteHallSuccessImplCopyWith<$Res> {
  __$$DeleteHallSuccessImplCopyWithImpl(_$DeleteHallSuccessImpl _value,
      $Res Function(_$DeleteHallSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteHallSuccessImpl implements _DeleteHallSuccess {
  const _$DeleteHallSuccessImpl();

  @override
  String toString() {
    return 'HallsState.deleteHallSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DeleteHallSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return deleteHallSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return deleteHallSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (deleteHallSuccess != null) {
      return deleteHallSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return deleteHallSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return deleteHallSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (deleteHallSuccess != null) {
      return deleteHallSuccess(this);
    }
    return orElse();
  }
}

abstract class _DeleteHallSuccess implements HallsState {
  const factory _DeleteHallSuccess() = _$DeleteHallSuccessImpl;
}

/// @nodoc
abstract class _$$DeleteHallFailedImplCopyWith<$Res> {
  factory _$$DeleteHallFailedImplCopyWith(_$DeleteHallFailedImpl value,
          $Res Function(_$DeleteHallFailedImpl) then) =
      __$$DeleteHallFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DeleteHallFailedImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$DeleteHallFailedImpl>
    implements _$$DeleteHallFailedImplCopyWith<$Res> {
  __$$DeleteHallFailedImplCopyWithImpl(_$DeleteHallFailedImpl _value,
      $Res Function(_$DeleteHallFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$DeleteHallFailedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteHallFailedImpl implements _DeleteHallFailed {
  const _$DeleteHallFailedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'HallsState.deleteHallFailed(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteHallFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteHallFailedImplCopyWith<_$DeleteHallFailedImpl> get copyWith =>
      __$$DeleteHallFailedImplCopyWithImpl<_$DeleteHallFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return deleteHallFailed(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return deleteHallFailed?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (deleteHallFailed != null) {
      return deleteHallFailed(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return deleteHallFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return deleteHallFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (deleteHallFailed != null) {
      return deleteHallFailed(this);
    }
    return orElse();
  }
}

abstract class _DeleteHallFailed implements HallsState {
  const factory _DeleteHallFailed(final String message) =
      _$DeleteHallFailedImpl;

  String get message;

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteHallFailedImplCopyWith<_$DeleteHallFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateHallSuccessImplCopyWith<$Res> {
  factory _$$UpdateHallSuccessImplCopyWith(_$UpdateHallSuccessImpl value,
          $Res Function(_$UpdateHallSuccessImpl) then) =
      __$$UpdateHallSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UpdateHallSuccessImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$UpdateHallSuccessImpl>
    implements _$$UpdateHallSuccessImplCopyWith<$Res> {
  __$$UpdateHallSuccessImplCopyWithImpl(_$UpdateHallSuccessImpl _value,
      $Res Function(_$UpdateHallSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UpdateHallSuccessImpl implements UpdateHallSuccess {
  const _$UpdateHallSuccessImpl();

  @override
  String toString() {
    return 'HallsState.updateHallSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UpdateHallSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return updateHallSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return updateHallSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (updateHallSuccess != null) {
      return updateHallSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return updateHallSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return updateHallSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (updateHallSuccess != null) {
      return updateHallSuccess(this);
    }
    return orElse();
  }
}

abstract class UpdateHallSuccess implements HallsState {
  const factory UpdateHallSuccess() = _$UpdateHallSuccessImpl;
}

/// @nodoc
abstract class _$$UpdateHallFailedImplCopyWith<$Res> {
  factory _$$UpdateHallFailedImplCopyWith(_$UpdateHallFailedImpl value,
          $Res Function(_$UpdateHallFailedImpl) then) =
      __$$UpdateHallFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UpdateHallFailedImplCopyWithImpl<$Res>
    extends _$HallsStateCopyWithImpl<$Res, _$UpdateHallFailedImpl>
    implements _$$UpdateHallFailedImplCopyWith<$Res> {
  __$$UpdateHallFailedImplCopyWithImpl(_$UpdateHallFailedImpl _value,
      $Res Function(_$UpdateHallFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$UpdateHallFailedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateHallFailedImpl implements UpdateHallFailed {
  const _$UpdateHallFailedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'HallsState.updateHallFailed(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateHallFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateHallFailedImplCopyWith<_$UpdateHallFailedImpl> get copyWith =>
      __$$UpdateHallFailedImplCopyWithImpl<_$UpdateHallFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(HallModel hall) addHallSuccess,
    required TResult Function(String message) addHallFailed,
    required TResult Function() getHallsSuccess,
    required TResult Function(String message) getHallsFailed,
    required TResult Function() deleteHallSuccess,
    required TResult Function(String message) deleteHallFailed,
    required TResult Function() updateHallSuccess,
    required TResult Function(String message) updateHallFailed,
  }) {
    return updateHallFailed(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(HallModel hall)? addHallSuccess,
    TResult? Function(String message)? addHallFailed,
    TResult? Function()? getHallsSuccess,
    TResult? Function(String message)? getHallsFailed,
    TResult? Function()? deleteHallSuccess,
    TResult? Function(String message)? deleteHallFailed,
    TResult? Function()? updateHallSuccess,
    TResult? Function(String message)? updateHallFailed,
  }) {
    return updateHallFailed?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(HallModel hall)? addHallSuccess,
    TResult Function(String message)? addHallFailed,
    TResult Function()? getHallsSuccess,
    TResult Function(String message)? getHallsFailed,
    TResult Function()? deleteHallSuccess,
    TResult Function(String message)? deleteHallFailed,
    TResult Function()? updateHallSuccess,
    TResult Function(String message)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (updateHallFailed != null) {
      return updateHallFailed(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_AddHallSuccess value) addHallSuccess,
    required TResult Function(_AddHallsFailed value) addHallFailed,
    required TResult Function(_GetHallsSuccess value) getHallsSuccess,
    required TResult Function(_GetHallsFailed value) getHallsFailed,
    required TResult Function(_DeleteHallSuccess value) deleteHallSuccess,
    required TResult Function(_DeleteHallFailed value) deleteHallFailed,
    required TResult Function(UpdateHallSuccess value) updateHallSuccess,
    required TResult Function(UpdateHallFailed value) updateHallFailed,
  }) {
    return updateHallFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_AddHallSuccess value)? addHallSuccess,
    TResult? Function(_AddHallsFailed value)? addHallFailed,
    TResult? Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult? Function(_GetHallsFailed value)? getHallsFailed,
    TResult? Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult? Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult? Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult? Function(UpdateHallFailed value)? updateHallFailed,
  }) {
    return updateHallFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_AddHallSuccess value)? addHallSuccess,
    TResult Function(_AddHallsFailed value)? addHallFailed,
    TResult Function(_GetHallsSuccess value)? getHallsSuccess,
    TResult Function(_GetHallsFailed value)? getHallsFailed,
    TResult Function(_DeleteHallSuccess value)? deleteHallSuccess,
    TResult Function(_DeleteHallFailed value)? deleteHallFailed,
    TResult Function(UpdateHallSuccess value)? updateHallSuccess,
    TResult Function(UpdateHallFailed value)? updateHallFailed,
    required TResult orElse(),
  }) {
    if (updateHallFailed != null) {
      return updateHallFailed(this);
    }
    return orElse();
  }
}

abstract class UpdateHallFailed implements HallsState {
  const factory UpdateHallFailed(final String message) = _$UpdateHallFailedImpl;

  String get message;

  /// Create a copy of HallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateHallFailedImplCopyWith<_$UpdateHallFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
