part of 'halls_cubit.dart';

@freezed
class HallsState with _$HallsState {
  const factory HallsState.initial() = _Initial;
  const factory HallsState.loading() = _Loading;
  const factory HallsState.addHallSuccess(HallModel hall) = _AddHallSuccess;
  const factory HallsState.addHallFailed(String message) = _AddHallsFailed;
  const factory HallsState.getHallsSuccess() = _GetHallsSuccess;
  const factory HallsState.getHallsFailed(String message) = _GetHallsFailed;
  const factory HallsState.deleteHallSuccess() = _DeleteHallSuccess;
  const factory HallsState.deleteHallFailed(String message) = _DeleteHallFailed;
  const factory HallsState.updateHallSuccess() = UpdateHallSuccess;
  const factory HallsState.updateHallFailed(String message) = UpdateHallFailed;
}
