import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:r2/core/helpers/shared_preferences_helper.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
import 'package:r2/src/branches/data/models/hall_params.dart';

mixin HallsOperations {
  TextEditingController? nameController;
  String? phoneDialingCode;
  TextEditingController? phoneController;
  String? whatsappDialingCode;
  TextEditingController? whatsappController;
  String? lat;
  String? lng;
  TextEditingController? addressController;
  List<int> selectedCategoryIds = [];
  int? cityId;
  File? image;
  List<HallModel>? halls;
  HallParams createHallParams() => HallParams(
        cityId: cityId ?? 0,
        services: selectedCategoryIds,
        name: nameController!.text,
        phoneDialingCode: phoneDialingCode ?? '+20',
        phone: phoneController!.text,
        whatsappDialingCode: whatsappDialingCode ?? '+20',
        whatsapp: whatsappController!.text,
        lat: lat ?? "0.0",
        lng: lng ?? "0.0",
        address: addressController!.text,
        image: image?.path,
      );

  void initHallsControllers() {
    nameController = TextEditingController();
    phoneController = TextEditingController();
    whatsappController = TextEditingController();
    addressController = TextEditingController();
  }

  void disposeHallsControllers() {
    nameController!.dispose();
    phoneController!.dispose();
    whatsappController!.dispose();
    addressController!.dispose();
    selectedCategoryIds = [];
    cityId = null;
    phoneDialingCode = null;
    whatsappDialingCode = null;
    lat = null;
    lng = null;
    image = null;
  }

  void setHallData(HallModel? hall) async {
    if (hall == null) return;
    await SharedPreferencesHelper.set<String>('hall', jsonEncode(hall));
  }

  HallModel? gethallData() {
    final hall = SharedPreferencesHelper.get('hall');
    if (hall == null) return null;
    return HallModel.fromJson(jsonDecode(hall));
  }
}
