import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
import 'package:r2/src/branches/data/repository/branches_repo.dart';
import 'package:r2/src/branches/presentation/cubit/halls_operations.dart';

part 'halls_state.dart';
part 'halls_cubit.freezed.dart';

class HallsCubit extends Cubit<HallsState> with HallsOperations {
  final BranchesRepo _branchesRepo;
  HallsCubit(this._branchesRepo) : super(HallsState.initial());

  Future<void> addHall() async {
    emit(HallsState.loading());
    final result = await _branchesRepo.addHall(createHallParams());
    result.when(
        success: (hall) {
          emit(HallsState.addHallSuccess(hall.data ?? HallModel()));
        },
        failure: (message) => emit(HallsState.addHallFailed(message)));
  }

  Future<void> getHalls() async {
    emit(HallsState.loading());
    final result = await _branchesRepo.getHalls();
    result.when(
        success: (response) {
          halls = response.data ?? [];
          emit(HallsState.getHallsSuccess());
        },
        failure: (message) => emit(HallsState.getHallsFailed(message)));
  }

  Future<void> deleteHall(int hallId) async {
    emit(HallsState.loading());
    final result = await _branchesRepo.deleteHall(hallId);
    result.when(
        success: (response) async {
          emit(HallsState.deleteHallSuccess());
          await getHalls();
        },
        failure: (message) => emit(HallsState.deleteHallFailed(message)));
  }
  Future<void>updateHall(int id)async{
    emit(HallsState.loading());
    final result = await _branchesRepo.updateHall(id, createHallParams());
    result.when(
        success: (response) async {
          emit(HallsState.updateHallSuccess());
          await getHalls();
        },
        failure: (message) => emit(HallsState.updateHallFailed(message)));
  }
}
