import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/add_widget.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/branches/presentation/widgets/branches/branch_card.dart';
import 'package:r2/src/branches/presentation/widgets/branches/profile_section.dart';
import 'package:r2/src/branches/presentation/widgets/branches/r2_logo.dart';

@RoutePage()
class BranchesScreen extends StatelessWidget implements AutoRouteWrapper {
  const BranchesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<HallsCubit, HallsState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        getHallsSuccess: () => true,
        getHallsFailed: (_) => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
            getHallsSuccess: () {
              UiHelper.onSuccess();
            },
            getHallsFailed: (message) {
              UiHelper.onFailure(context, message);
            },
            deleteHallSuccess: ()  {
                UiHelper.onSuccess();
              if (context.mounted) {

                UiHelper.showCustomSnackBar(context: context, message: '');
              }
            },
            deleteHallFailed: (message) => UiHelper.onFailure(context, message),
            loading: () => UiHelper.onLoading(context));
      },
      child: Scaffold(
          body: DefaultScaffoldGradient(
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                R2Logo(),
                const SizedBox(height: 20),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  child: Column(
                    children: [
                      ProfileSection(),
                      const SizedBox(height: 20),
                      AddWidget(
                        title: context.l10n.addBranch,
                        onTap: () =>
                            context.router.push( AddBranchRoute()),
                      ),
                      const SizedBox(height: 20),
                      BlocBuilder<HallsCubit, HallsState>(
                          builder: (context, state) {
                        return Column(
                          children: List.generate(
                            injector<HallsCubit>().halls?.length ?? 0,
                            (index) => Column(
                              children: [
                                BranchCard(
                                  hall: injector<HallsCubit>().halls?[index],
                                ),
                                SizedBox(height: 12),
                              ],
                            ),
                          ),
                        );
                      }),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      )),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
        value: injector<HallsCubit>()..getHalls(), child: this);
  }
}
