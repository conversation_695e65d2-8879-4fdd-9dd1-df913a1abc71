import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/branches/data/models/hall_model.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/branches/presentation/widgets/add_hall/add_branch_form.dart';

import '../../../../core/helpers/ui_helper.dart';

@RoutePage()
class AddBranchScreen extends StatefulWidget implements AutoRouteWrapper {
  final HallModel? hall;
  const AddBranchScreen({super.key, this.hall});

  @override
  State<AddBranchScreen> createState() => _AddBranchScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
      value: injector<HallsCubit>()..initHallsControllers(),
      child: this,
    );
  }
}

class _AddBranchScreenState extends State<AddBranchScreen> {
  @override
  void dispose() {
    injector<HallsCubit>().disposeHallsControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HallsCubit, HallsState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        addHallSuccess: (_) => true,
        addHallFailed: (_) => true,
        updateHallFailed: (_) => true,
        updateHallSuccess: () => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
          addHallSuccess: (h) {
            UiHelper.onSuccess();
            context.router.pushAndPopUntil(const BranchesRoute(),
                predicate: (route) => false);
          },
          updateHallSuccess: () {
            UiHelper.onSuccess();
            context.router.pushAndPopUntil(const BranchesRoute(),
                predicate: (route) => false);
          },
          addHallFailed: (message) => UiHelper.onFailure(context, message),
          updateHallFailed: (message) => UiHelper.onFailure(context, message),
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: defaultAppBar(
            context: context,
            title: widget.hall?.id == null
                ? context.l10n.addBranch
                : context.l10n.updateBranch),
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: Container(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32)
                      .copyWith(bottom: 8),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AddBranchForm(
                      hall: widget.hall,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
