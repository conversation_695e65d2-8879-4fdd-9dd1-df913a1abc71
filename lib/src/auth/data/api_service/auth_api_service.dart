import 'package:dio/dio.dart';
import 'package:r2/core/api/end_points.dart';
import 'package:r2/src/auth/data/models/user_params.dart';
import 'package:r2/src/auth/data/models/user_response.dart';
import 'package:retrofit/retrofit.dart';
part 'auth_api_service.g.dart';

@RestApi(baseUrl: EndPoints.baseUrl)
abstract class AuthApiService {
  factory AuthApiService(
    Dio dio, {
    String baseUrl,
  }) = _AuthApiService;

  @POST(EndPoints.login)
  Future<AuthResponse> login(@Body() LoginParams params);
  @POST(EndPoints.verify)
  Future<AuthResponse> verify(@Body() VerifyParams params);
  @POST(EndPoints.completeProfile)
  Future<AuthResponse> completeProfile(@Body() CompleteProfileParams params);
}
