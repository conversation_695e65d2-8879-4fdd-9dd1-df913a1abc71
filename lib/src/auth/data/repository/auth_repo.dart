import 'package:r2/core/error/error_handler.dart';
import 'package:r2/core/helpers/result.dart';
import 'package:r2/src/auth/data/api_service/auth_api_service.dart';
import 'package:r2/src/auth/data/models/user_params.dart';
import 'package:r2/src/auth/data/models/user_response.dart';
import 'package:r2/src/auth/presentation/cubit/auth_operations.dart';

class AuthRepo with AuthOperations {
  final AuthApiService _authApiService;

  AuthRepo(this._authApiService);
  Future<Result<AuthResponse>> login(LoginParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _authApiService.login(params);
        setUserData(result.data?.user);
        return result;
      });
  Future<Result<AuthResponse>> verify(VerifyParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _authApiService.verify(params);
        await setToken(result.data?.token);
        return result;
      });

  Future<Result<AuthResponse>> completeProfile(
          CompleteProfileParams params) async =>
      await errorHandlerAsync(() async {
        final result = await _authApiService.completeProfile(params);
        setUserData(result.data?.user);
        return result;
      });
}
