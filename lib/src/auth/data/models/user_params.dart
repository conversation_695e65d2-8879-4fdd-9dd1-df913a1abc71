import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_params.freezed.dart';
part 'user_params.g.dart';

@freezed
class LoginParams with _$LoginParams {
  const factory LoginParams({
    required String phone,
    @JsonKey(name: 'dialing_code') required String dialingCode,
  }) = _LoginParams;

  factory LoginParams.fromJson(Map<String, dynamic> json) =>
      _$LoginParamsFromJson(json);
}
@freezed 
class VerifyParams with _$VerifyParams {
  const factory VerifyParams({
    required String phone,
    required String otp
  }) = _VerifyParams;
  factory VerifyParams.fromJson(Map<String, dynamic> json) =>
      _$VerifyParamsFromJson(json);
}
@freezed 
class CompleteProfileParams with _$CompleteProfileParams {
  const factory CompleteProfileParams({
    required String name
  }) = _CompleteProfileParams;
  factory CompleteProfileParams.fromJson(Map<String, dynamic> json) =>
      _$CompleteProfileParamsFromJson(json);
}