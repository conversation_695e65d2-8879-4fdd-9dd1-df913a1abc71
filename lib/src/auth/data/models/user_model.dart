// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

@freezed
class UserModel with _$UserModel {
    const factory UserModel({
        @Json<PERSON>ey(name: "id")
        int? id,
        @<PERSON><PERSON><PERSON><PERSON>(name: "manager_id")
        int? managerId,
        @<PERSON><PERSON><PERSON><PERSON>(name: "hall_id")
        int? hallId,
        @Json<PERSON>ey(name: "name")
        String? name,
        @<PERSON><PERSON><PERSON><PERSON>(name: "dialing_code")
        String? dialingCode,
        @Json<PERSON>ey(name: "phone")
        String? phone,
        @<PERSON><PERSON><PERSON><PERSON>(name: "is_first_time")
        bool? isFirstTime,
        @J<PERSON><PERSON><PERSON>(name: "has_active_package")
        bool? hasActivePackage,
    }) = _UserModel;

    factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
}
