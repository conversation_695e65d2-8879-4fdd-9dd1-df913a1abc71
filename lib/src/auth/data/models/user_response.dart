import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/core/api/base_response.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
part 'user_response.freezed.dart';
part 'user_response.g.dart';
@JsonSerializable()
class AuthResponse extends BaseResponse {
  final Data? data;

  AuthResponse({this.data, required super.errors, required super.message});
  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@freezed
class Data with _$Data {
  const factory Data({
    UserModel? user,
    String? token,

  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}
