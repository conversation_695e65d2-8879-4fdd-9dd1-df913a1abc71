// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LoginParams _$LoginParamsFromJson(Map<String, dynamic> json) {
  return _LoginParams.fromJson(json);
}

/// @nodoc
mixin _$LoginParams {
  String get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'dialing_code')
  String get dialingCode => throw _privateConstructorUsedError;

  /// Serializes this LoginParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginParamsCopyWith<LoginParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginParamsCopyWith<$Res> {
  factory $LoginParamsCopyWith(
          LoginParams value, $Res Function(LoginParams) then) =
      _$LoginParamsCopyWithImpl<$Res, LoginParams>;
  @useResult
  $Res call({String phone, @JsonKey(name: 'dialing_code') String dialingCode});
}

/// @nodoc
class _$LoginParamsCopyWithImpl<$Res, $Val extends LoginParams>
    implements $LoginParamsCopyWith<$Res> {
  _$LoginParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = null,
    Object? dialingCode = null,
  }) {
    return _then(_value.copyWith(
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      dialingCode: null == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginParamsImplCopyWith<$Res>
    implements $LoginParamsCopyWith<$Res> {
  factory _$$LoginParamsImplCopyWith(
          _$LoginParamsImpl value, $Res Function(_$LoginParamsImpl) then) =
      __$$LoginParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String phone, @JsonKey(name: 'dialing_code') String dialingCode});
}

/// @nodoc
class __$$LoginParamsImplCopyWithImpl<$Res>
    extends _$LoginParamsCopyWithImpl<$Res, _$LoginParamsImpl>
    implements _$$LoginParamsImplCopyWith<$Res> {
  __$$LoginParamsImplCopyWithImpl(
      _$LoginParamsImpl _value, $Res Function(_$LoginParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = null,
    Object? dialingCode = null,
  }) {
    return _then(_$LoginParamsImpl(
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      dialingCode: null == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginParamsImpl implements _LoginParams {
  const _$LoginParamsImpl(
      {required this.phone,
      @JsonKey(name: 'dialing_code') required this.dialingCode});

  factory _$LoginParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginParamsImplFromJson(json);

  @override
  final String phone;
  @override
  @JsonKey(name: 'dialing_code')
  final String dialingCode;

  @override
  String toString() {
    return 'LoginParams(phone: $phone, dialingCode: $dialingCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginParamsImpl &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.dialingCode, dialingCode) ||
                other.dialingCode == dialingCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, phone, dialingCode);

  /// Create a copy of LoginParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginParamsImplCopyWith<_$LoginParamsImpl> get copyWith =>
      __$$LoginParamsImplCopyWithImpl<_$LoginParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginParamsImplToJson(
      this,
    );
  }
}

abstract class _LoginParams implements LoginParams {
  const factory _LoginParams(
          {required final String phone,
          @JsonKey(name: 'dialing_code') required final String dialingCode}) =
      _$LoginParamsImpl;

  factory _LoginParams.fromJson(Map<String, dynamic> json) =
      _$LoginParamsImpl.fromJson;

  @override
  String get phone;
  @override
  @JsonKey(name: 'dialing_code')
  String get dialingCode;

  /// Create a copy of LoginParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginParamsImplCopyWith<_$LoginParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerifyParams _$VerifyParamsFromJson(Map<String, dynamic> json) {
  return _VerifyParams.fromJson(json);
}

/// @nodoc
mixin _$VerifyParams {
  String get phone => throw _privateConstructorUsedError;
  String get otp => throw _privateConstructorUsedError;

  /// Serializes this VerifyParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerifyParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifyParamsCopyWith<VerifyParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyParamsCopyWith<$Res> {
  factory $VerifyParamsCopyWith(
          VerifyParams value, $Res Function(VerifyParams) then) =
      _$VerifyParamsCopyWithImpl<$Res, VerifyParams>;
  @useResult
  $Res call({String phone, String otp});
}

/// @nodoc
class _$VerifyParamsCopyWithImpl<$Res, $Val extends VerifyParams>
    implements $VerifyParamsCopyWith<$Res> {
  _$VerifyParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifyParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = null,
    Object? otp = null,
  }) {
    return _then(_value.copyWith(
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerifyParamsImplCopyWith<$Res>
    implements $VerifyParamsCopyWith<$Res> {
  factory _$$VerifyParamsImplCopyWith(
          _$VerifyParamsImpl value, $Res Function(_$VerifyParamsImpl) then) =
      __$$VerifyParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String phone, String otp});
}

/// @nodoc
class __$$VerifyParamsImplCopyWithImpl<$Res>
    extends _$VerifyParamsCopyWithImpl<$Res, _$VerifyParamsImpl>
    implements _$$VerifyParamsImplCopyWith<$Res> {
  __$$VerifyParamsImplCopyWithImpl(
      _$VerifyParamsImpl _value, $Res Function(_$VerifyParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phone = null,
    Object? otp = null,
  }) {
    return _then(_$VerifyParamsImpl(
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerifyParamsImpl implements _VerifyParams {
  const _$VerifyParamsImpl({required this.phone, required this.otp});

  factory _$VerifyParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerifyParamsImplFromJson(json);

  @override
  final String phone;
  @override
  final String otp;

  @override
  String toString() {
    return 'VerifyParams(phone: $phone, otp: $otp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyParamsImpl &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.otp, otp) || other.otp == otp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, phone, otp);

  /// Create a copy of VerifyParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyParamsImplCopyWith<_$VerifyParamsImpl> get copyWith =>
      __$$VerifyParamsImplCopyWithImpl<_$VerifyParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerifyParamsImplToJson(
      this,
    );
  }
}

abstract class _VerifyParams implements VerifyParams {
  const factory _VerifyParams(
      {required final String phone,
      required final String otp}) = _$VerifyParamsImpl;

  factory _VerifyParams.fromJson(Map<String, dynamic> json) =
      _$VerifyParamsImpl.fromJson;

  @override
  String get phone;
  @override
  String get otp;

  /// Create a copy of VerifyParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyParamsImplCopyWith<_$VerifyParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CompleteProfileParams _$CompleteProfileParamsFromJson(
    Map<String, dynamic> json) {
  return _CompleteProfileParams.fromJson(json);
}

/// @nodoc
mixin _$CompleteProfileParams {
  String get name => throw _privateConstructorUsedError;

  /// Serializes this CompleteProfileParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CompleteProfileParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CompleteProfileParamsCopyWith<CompleteProfileParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CompleteProfileParamsCopyWith<$Res> {
  factory $CompleteProfileParamsCopyWith(CompleteProfileParams value,
          $Res Function(CompleteProfileParams) then) =
      _$CompleteProfileParamsCopyWithImpl<$Res, CompleteProfileParams>;
  @useResult
  $Res call({String name});
}

/// @nodoc
class _$CompleteProfileParamsCopyWithImpl<$Res,
        $Val extends CompleteProfileParams>
    implements $CompleteProfileParamsCopyWith<$Res> {
  _$CompleteProfileParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CompleteProfileParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CompleteProfileParamsImplCopyWith<$Res>
    implements $CompleteProfileParamsCopyWith<$Res> {
  factory _$$CompleteProfileParamsImplCopyWith(
          _$CompleteProfileParamsImpl value,
          $Res Function(_$CompleteProfileParamsImpl) then) =
      __$$CompleteProfileParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name});
}

/// @nodoc
class __$$CompleteProfileParamsImplCopyWithImpl<$Res>
    extends _$CompleteProfileParamsCopyWithImpl<$Res,
        _$CompleteProfileParamsImpl>
    implements _$$CompleteProfileParamsImplCopyWith<$Res> {
  __$$CompleteProfileParamsImplCopyWithImpl(_$CompleteProfileParamsImpl _value,
      $Res Function(_$CompleteProfileParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CompleteProfileParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
  }) {
    return _then(_$CompleteProfileParamsImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CompleteProfileParamsImpl implements _CompleteProfileParams {
  const _$CompleteProfileParamsImpl({required this.name});

  factory _$CompleteProfileParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CompleteProfileParamsImplFromJson(json);

  @override
  final String name;

  @override
  String toString() {
    return 'CompleteProfileParams(name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompleteProfileParamsImpl &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name);

  /// Create a copy of CompleteProfileParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompleteProfileParamsImplCopyWith<_$CompleteProfileParamsImpl>
      get copyWith => __$$CompleteProfileParamsImplCopyWithImpl<
          _$CompleteProfileParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CompleteProfileParamsImplToJson(
      this,
    );
  }
}

abstract class _CompleteProfileParams implements CompleteProfileParams {
  const factory _CompleteProfileParams({required final String name}) =
      _$CompleteProfileParamsImpl;

  factory _CompleteProfileParams.fromJson(Map<String, dynamic> json) =
      _$CompleteProfileParamsImpl.fromJson;

  @override
  String get name;

  /// Create a copy of CompleteProfileParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompleteProfileParamsImplCopyWith<_$CompleteProfileParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
