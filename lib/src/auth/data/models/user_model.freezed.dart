// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
  @JsonKey(name: "id")
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "manager_id")
  int? get managerId => throw _privateConstructorUsedError;
  @JsonKey(name: "hall_id")
  int? get hallId => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "dialing_code")
  String? get dialingCode => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "is_first_time")
  bool? get isFirstTime => throw _privateConstructorUsedError;
  @JsonKey(name: "has_active_package")
  bool? get hasActivePackage => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "manager_id") int? managerId,
      @JsonKey(name: "hall_id") int? hallId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "dialing_code") String? dialingCode,
      @JsonKey(name: "phone") String? phone,
      @JsonKey(name: "is_first_time") bool? isFirstTime,
      @JsonKey(name: "has_active_package") bool? hasActivePackage});
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? managerId = freezed,
    Object? hallId = freezed,
    Object? name = freezed,
    Object? dialingCode = freezed,
    Object? phone = freezed,
    Object? isFirstTime = freezed,
    Object? hasActivePackage = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      managerId: freezed == managerId
          ? _value.managerId
          : managerId // ignore: cast_nullable_to_non_nullable
              as int?,
      hallId: freezed == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      dialingCode: freezed == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      isFirstTime: freezed == isFirstTime
          ? _value.isFirstTime
          : isFirstTime // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActivePackage: freezed == hasActivePackage
          ? _value.hasActivePackage
          : hasActivePackage // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") int? id,
      @JsonKey(name: "manager_id") int? managerId,
      @JsonKey(name: "hall_id") int? hallId,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "dialing_code") String? dialingCode,
      @JsonKey(name: "phone") String? phone,
      @JsonKey(name: "is_first_time") bool? isFirstTime,
      @JsonKey(name: "has_active_package") bool? hasActivePackage});
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? managerId = freezed,
    Object? hallId = freezed,
    Object? name = freezed,
    Object? dialingCode = freezed,
    Object? phone = freezed,
    Object? isFirstTime = freezed,
    Object? hasActivePackage = freezed,
  }) {
    return _then(_$UserModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      managerId: freezed == managerId
          ? _value.managerId
          : managerId // ignore: cast_nullable_to_non_nullable
              as int?,
      hallId: freezed == hallId
          ? _value.hallId
          : hallId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      dialingCode: freezed == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      isFirstTime: freezed == isFirstTime
          ? _value.isFirstTime
          : isFirstTime // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActivePackage: freezed == hasActivePackage
          ? _value.hasActivePackage
          : hasActivePackage // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserModelImpl implements _UserModel {
  const _$UserModelImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "manager_id") this.managerId,
      @JsonKey(name: "hall_id") this.hallId,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "dialing_code") this.dialingCode,
      @JsonKey(name: "phone") this.phone,
      @JsonKey(name: "is_first_time") this.isFirstTime,
      @JsonKey(name: "has_active_package") this.hasActivePackage});

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final int? id;
  @override
  @JsonKey(name: "manager_id")
  final int? managerId;
  @override
  @JsonKey(name: "hall_id")
  final int? hallId;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "dialing_code")
  final String? dialingCode;
  @override
  @JsonKey(name: "phone")
  final String? phone;
  @override
  @JsonKey(name: "is_first_time")
  final bool? isFirstTime;
  @override
  @JsonKey(name: "has_active_package")
  final bool? hasActivePackage;

  @override
  String toString() {
    return 'UserModel(id: $id, managerId: $managerId, hallId: $hallId, name: $name, dialingCode: $dialingCode, phone: $phone, isFirstTime: $isFirstTime, hasActivePackage: $hasActivePackage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.managerId, managerId) ||
                other.managerId == managerId) &&
            (identical(other.hallId, hallId) || other.hallId == hallId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.dialingCode, dialingCode) ||
                other.dialingCode == dialingCode) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.isFirstTime, isFirstTime) ||
                other.isFirstTime == isFirstTime) &&
            (identical(other.hasActivePackage, hasActivePackage) ||
                other.hasActivePackage == hasActivePackage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, managerId, hallId, name,
      dialingCode, phone, isFirstTime, hasActivePackage);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel implements UserModel {
  const factory _UserModel(
          {@JsonKey(name: "id") final int? id,
          @JsonKey(name: "manager_id") final int? managerId,
          @JsonKey(name: "hall_id") final int? hallId,
          @JsonKey(name: "name") final String? name,
          @JsonKey(name: "dialing_code") final String? dialingCode,
          @JsonKey(name: "phone") final String? phone,
          @JsonKey(name: "is_first_time") final bool? isFirstTime,
          @JsonKey(name: "has_active_package") final bool? hasActivePackage}) =
      _$UserModelImpl;

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

  @override
  @JsonKey(name: "id")
  int? get id;
  @override
  @JsonKey(name: "manager_id")
  int? get managerId;
  @override
  @JsonKey(name: "hall_id")
  int? get hallId;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "dialing_code")
  String? get dialingCode;
  @override
  @JsonKey(name: "phone")
  String? get phone;
  @override
  @JsonKey(name: "is_first_time")
  bool? get isFirstTime;
  @override
  @JsonKey(name: "has_active_package")
  bool? get hasActivePackage;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
