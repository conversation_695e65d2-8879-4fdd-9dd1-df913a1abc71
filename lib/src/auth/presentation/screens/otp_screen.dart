import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/auth/presentation/count_down_cubit/count_down_cubit.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:r2/src/auth/presentation/widgets/otp_form.dart';
import 'package:r2/src/auth/presentation/widgets/resrsnd_otp.dart';

@RoutePage()
class OtpScreen extends StatelessWidget implements AutoRouteWrapper {
  const OtpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        verifySuccess: (user) => true,
        verifyFailure: (message) => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
          verifySuccess: (user) {
            UiHelper.onSuccess();
            context.router.pushAndPopUntil(
                user.isFirstTime == true
                    ? const WelcomeRoute()
                    : BranchesRoute(),
                predicate: (route) => false);
            injector<AuthCubit>().clear();
          },
          verifyFailure: (message) {
            UiHelper.onFailure(context, message);
          },
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: defaultAppBar(
              context: context, title: context.l10n.verficationCode),
          body: DefaultScaffoldGradient(
            child: Container(
              width: 1.sw,
              height: 1.sh,
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 80)
                      .copyWith(bottom: 0),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Center(child: Assets.images.otp.image()),
                    const SizedBox(
                      height: 56,
                    ),
                    const OtpForm(),
                    const SizedBox(
                      height: 64,
                    ),
                    const ResendOtp(),
                  ],
                ),
              ),
            ),
          )),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => CountDownCubit(),
          child: this,
        ),
        BlocProvider.value(
          value: injector<AuthCubit>(),
          child: this,
        ),
      ],
      child: this,
    );
  }
}
