import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:r2/src/auth/presentation/widgets/login_form.dart';

@RoutePage()
class LoginScreen extends StatelessWidget implements AutoRouteWrapper {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loginSuccess: (_) => true,
        loginFailure: (_) => true,
        loading: () => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
          loginSuccess: (user) {
            UiHelper.onSuccess();
            context.router.push(const OtpRoute());
          },
          loginFailure: (message) => UiHelper.onFailure(context, message),
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
        body: DefaultScaffoldGradient(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 128)
                .copyWith(bottom: 0),
            child: SizedBox(
              height: 1.sh,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.images.login.image(),
                    const SizedBox(
                      height: 64,
                    ),
                    const LoginForm(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(value: injector<AuthCubit>()..initLoginControllers(), child: this);
  }
}
