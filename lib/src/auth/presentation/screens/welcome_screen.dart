import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:r2/src/auth/presentation/widgets/welcome_form.dart';

@RoutePage()
class WelcomeScreen extends StatelessWidget implements AutoRouteWrapper {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        completeProfileSuccess: (user) => true,
        completeProfileFailure: (message) => true,
        orElse: () => false,
      ),
      listener: (context, state) {
        state.whenOrNull(
          completeProfileSuccess: (user) {
            UiHelper.onSuccess();
            injector<AuthCubit>().nameController?.dispose();
            context.router.pushAndPopUntil(
              const BranchesRoute(),
              predicate: (route) => false,
            );
          },
          completeProfileFailure: (message) =>
              UiHelper.onFailure(context, message),
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: defaultAppBar(
          context: context,
        ),
        body: DefaultScaffoldGradient(
          child: Container(
            width: 1.sw,
            height: 1.sh,
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 80)
                .copyWith(bottom: 0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(child: Assets.images.welcome.image()),
                  const SizedBox(
                    height: 56,
                  ),
                  const WelcomeForm(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
        value: injector<AuthCubit>(),
        child: this);
  }
}
