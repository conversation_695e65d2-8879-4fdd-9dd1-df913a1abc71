import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:timer_count_down/timer_controller.dart';

part 'count_down_state.dart';

class CountDownCubit extends Cubit<CountDownState> {
  CountDownCubit() : super(CountDownState());
  bool isFinished = false;
  CountdownController controller = CountdownController(autoStart: true);
  void onCountDownEnd() {
    isFinished = true;
    emit(CountDownStatus());
  }

  void resetCount() {
    isFinished = false;
    controller.restart();
    emit(CountDownStatus());
  }
}
