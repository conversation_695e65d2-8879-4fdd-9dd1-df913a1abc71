import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:r2/src/auth/data/models/user_model.dart';
import 'package:r2/src/auth/data/models/user_params.dart';
import 'package:r2/src/auth/data/repository/auth_repo.dart';
import 'package:r2/src/auth/presentation/cubit/auth_operations.dart';

part 'auth_state.dart';
part 'auth_cubit.freezed.dart';

class AuthCubit extends Cubit<AuthState> with AuthOperations {
  final AuthRepo _authRepo;
  AuthCubit(this._authRepo) : super(AuthState.initial());
  Future<void> login() async {
    emit(AuthState.loading());
    final response = await _authRepo.login(
        LoginParams(phone: phoneController?.text??"", dialingCode: dialingCode));
    response.when(
      success: (result) {
        emit(AuthState.loginSuccess(result.data?.user ?? UserModel()));
      },
      failure: (message) => emit(
        AuthState.loginFailure(message),
      ),
    );
  }

  Future<void> verify() async {
    emit(AuthState.loading());
    final response = await _authRepo
        .verify(VerifyParams(phone: phoneController?.text??'', otp: otpVal));
    response.when(
      success: (result) {
        emit(AuthState.verifySuccess(result.data?.user ?? UserModel()));
      },
      failure: (message) => emit(
        AuthState.verifyFailure(message),
      ),
    );
  }

  Future<void> completeProfile() async {
    emit(AuthState.loading());
    final response = await _authRepo
        .completeProfile(CompleteProfileParams(name: nameController?.text??''));
    response.when(
      success: (result) {
        emit(
            AuthState.completeProfileSuccess(result.data?.user ?? UserModel()));
      },
      failure: (message) => emit(
        AuthState.completeProfileFailure(message),
      ),
    );
  }
}
