// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl with DiagnosticableTreeMixin implements _Initial {
  const _$InitialImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.initial()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'AuthState.initial'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements AuthState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl with DiagnosticableTreeMixin implements Loading {
  const _$LoadingImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.loading()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'AuthState.loading'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements AuthState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoginSuccessImplCopyWith<$Res> {
  factory _$$LoginSuccessImplCopyWith(
          _$LoginSuccessImpl value, $Res Function(_$LoginSuccessImpl) then) =
      __$$LoginSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserModel user});

  $UserModelCopyWith<$Res> get user;
}

/// @nodoc
class __$$LoginSuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoginSuccessImpl>
    implements _$$LoginSuccessImplCopyWith<$Res> {
  __$$LoginSuccessImplCopyWithImpl(
      _$LoginSuccessImpl _value, $Res Function(_$LoginSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$LoginSuccessImpl(
      null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel,
    ));
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserModelCopyWith<$Res> get user {
    return $UserModelCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$LoginSuccessImpl with DiagnosticableTreeMixin implements LoginSuccess {
  const _$LoginSuccessImpl(this.user);

  @override
  final UserModel user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.loginSuccess(user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.loginSuccess'))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginSuccessImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginSuccessImplCopyWith<_$LoginSuccessImpl> get copyWith =>
      __$$LoginSuccessImplCopyWithImpl<_$LoginSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return loginSuccess(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return loginSuccess?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (loginSuccess != null) {
      return loginSuccess(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return loginSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return loginSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (loginSuccess != null) {
      return loginSuccess(this);
    }
    return orElse();
  }
}

abstract class LoginSuccess implements AuthState {
  const factory LoginSuccess(final UserModel user) = _$LoginSuccessImpl;

  UserModel get user;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginSuccessImplCopyWith<_$LoginSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoginFailedImplCopyWith<$Res> {
  factory _$$LoginFailedImplCopyWith(
          _$LoginFailedImpl value, $Res Function(_$LoginFailedImpl) then) =
      __$$LoginFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$LoginFailedImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$LoginFailedImpl>
    implements _$$LoginFailedImplCopyWith<$Res> {
  __$$LoginFailedImplCopyWithImpl(
      _$LoginFailedImpl _value, $Res Function(_$LoginFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$LoginFailedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LoginFailedImpl with DiagnosticableTreeMixin implements LoginFailed {
  const _$LoginFailedImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.loginFailure(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.loginFailure'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginFailedImplCopyWith<_$LoginFailedImpl> get copyWith =>
      __$$LoginFailedImplCopyWithImpl<_$LoginFailedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return loginFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return loginFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (loginFailure != null) {
      return loginFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return loginFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return loginFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (loginFailure != null) {
      return loginFailure(this);
    }
    return orElse();
  }
}

abstract class LoginFailed implements AuthState {
  const factory LoginFailed(final String message) = _$LoginFailedImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginFailedImplCopyWith<_$LoginFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VerifySuccessImplCopyWith<$Res> {
  factory _$$VerifySuccessImplCopyWith(
          _$VerifySuccessImpl value, $Res Function(_$VerifySuccessImpl) then) =
      __$$VerifySuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserModel user});

  $UserModelCopyWith<$Res> get user;
}

/// @nodoc
class __$$VerifySuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$VerifySuccessImpl>
    implements _$$VerifySuccessImplCopyWith<$Res> {
  __$$VerifySuccessImplCopyWithImpl(
      _$VerifySuccessImpl _value, $Res Function(_$VerifySuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$VerifySuccessImpl(
      null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel,
    ));
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserModelCopyWith<$Res> get user {
    return $UserModelCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$VerifySuccessImpl
    with DiagnosticableTreeMixin
    implements VerifySuccess {
  const _$VerifySuccessImpl(this.user);

  @override
  final UserModel user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.verifySuccess(user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.verifySuccess'))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifySuccessImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifySuccessImplCopyWith<_$VerifySuccessImpl> get copyWith =>
      __$$VerifySuccessImplCopyWithImpl<_$VerifySuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return verifySuccess(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return verifySuccess?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (verifySuccess != null) {
      return verifySuccess(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return verifySuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return verifySuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (verifySuccess != null) {
      return verifySuccess(this);
    }
    return orElse();
  }
}

abstract class VerifySuccess implements AuthState {
  const factory VerifySuccess(final UserModel user) = _$VerifySuccessImpl;

  UserModel get user;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifySuccessImplCopyWith<_$VerifySuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$VerifyFailedImplCopyWith<$Res> {
  factory _$$VerifyFailedImplCopyWith(
          _$VerifyFailedImpl value, $Res Function(_$VerifyFailedImpl) then) =
      __$$VerifyFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$VerifyFailedImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$VerifyFailedImpl>
    implements _$$VerifyFailedImplCopyWith<$Res> {
  __$$VerifyFailedImplCopyWithImpl(
      _$VerifyFailedImpl _value, $Res Function(_$VerifyFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$VerifyFailedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$VerifyFailedImpl with DiagnosticableTreeMixin implements VerifyFailed {
  const _$VerifyFailedImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.verifyFailure(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.verifyFailure'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyFailedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyFailedImplCopyWith<_$VerifyFailedImpl> get copyWith =>
      __$$VerifyFailedImplCopyWithImpl<_$VerifyFailedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return verifyFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return verifyFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (verifyFailure != null) {
      return verifyFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return verifyFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return verifyFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (verifyFailure != null) {
      return verifyFailure(this);
    }
    return orElse();
  }
}

abstract class VerifyFailed implements AuthState {
  const factory VerifyFailed(final String message) = _$VerifyFailedImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyFailedImplCopyWith<_$VerifyFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompleteProfileSuccessImplCopyWith<$Res> {
  factory _$$CompleteProfileSuccessImplCopyWith(
          _$CompleteProfileSuccessImpl value,
          $Res Function(_$CompleteProfileSuccessImpl) then) =
      __$$CompleteProfileSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserModel userModel});

  $UserModelCopyWith<$Res> get userModel;
}

/// @nodoc
class __$$CompleteProfileSuccessImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$CompleteProfileSuccessImpl>
    implements _$$CompleteProfileSuccessImplCopyWith<$Res> {
  __$$CompleteProfileSuccessImplCopyWithImpl(
      _$CompleteProfileSuccessImpl _value,
      $Res Function(_$CompleteProfileSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userModel = null,
  }) {
    return _then(_$CompleteProfileSuccessImpl(
      null == userModel
          ? _value.userModel
          : userModel // ignore: cast_nullable_to_non_nullable
              as UserModel,
    ));
  }

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserModelCopyWith<$Res> get userModel {
    return $UserModelCopyWith<$Res>(_value.userModel, (value) {
      return _then(_value.copyWith(userModel: value));
    });
  }
}

/// @nodoc

class _$CompleteProfileSuccessImpl
    with DiagnosticableTreeMixin
    implements CompleteProfileSuccess {
  const _$CompleteProfileSuccessImpl(this.userModel);

  @override
  final UserModel userModel;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.completeProfileSuccess(userModel: $userModel)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.completeProfileSuccess'))
      ..add(DiagnosticsProperty('userModel', userModel));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompleteProfileSuccessImpl &&
            (identical(other.userModel, userModel) ||
                other.userModel == userModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userModel);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompleteProfileSuccessImplCopyWith<_$CompleteProfileSuccessImpl>
      get copyWith => __$$CompleteProfileSuccessImplCopyWithImpl<
          _$CompleteProfileSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return completeProfileSuccess(userModel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return completeProfileSuccess?.call(userModel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (completeProfileSuccess != null) {
      return completeProfileSuccess(userModel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return completeProfileSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return completeProfileSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (completeProfileSuccess != null) {
      return completeProfileSuccess(this);
    }
    return orElse();
  }
}

abstract class CompleteProfileSuccess implements AuthState {
  const factory CompleteProfileSuccess(final UserModel userModel) =
      _$CompleteProfileSuccessImpl;

  UserModel get userModel;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompleteProfileSuccessImplCopyWith<_$CompleteProfileSuccessImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompleteProfileFailedlImplCopyWith<$Res> {
  factory _$$CompleteProfileFailedlImplCopyWith(
          _$CompleteProfileFailedlImpl value,
          $Res Function(_$CompleteProfileFailedlImpl) then) =
      __$$CompleteProfileFailedlImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$CompleteProfileFailedlImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$CompleteProfileFailedlImpl>
    implements _$$CompleteProfileFailedlImplCopyWith<$Res> {
  __$$CompleteProfileFailedlImplCopyWithImpl(
      _$CompleteProfileFailedlImpl _value,
      $Res Function(_$CompleteProfileFailedlImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$CompleteProfileFailedlImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CompleteProfileFailedlImpl
    with DiagnosticableTreeMixin
    implements CompleteProfileFailedl {
  const _$CompleteProfileFailedlImpl(this.message);

  @override
  final String message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthState.completeProfileFailure(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthState.completeProfileFailure'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompleteProfileFailedlImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompleteProfileFailedlImplCopyWith<_$CompleteProfileFailedlImpl>
      get copyWith => __$$CompleteProfileFailedlImplCopyWithImpl<
          _$CompleteProfileFailedlImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(UserModel user) loginSuccess,
    required TResult Function(String message) loginFailure,
    required TResult Function(UserModel user) verifySuccess,
    required TResult Function(String message) verifyFailure,
    required TResult Function(UserModel userModel) completeProfileSuccess,
    required TResult Function(String message) completeProfileFailure,
  }) {
    return completeProfileFailure(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(UserModel user)? loginSuccess,
    TResult? Function(String message)? loginFailure,
    TResult? Function(UserModel user)? verifySuccess,
    TResult? Function(String message)? verifyFailure,
    TResult? Function(UserModel userModel)? completeProfileSuccess,
    TResult? Function(String message)? completeProfileFailure,
  }) {
    return completeProfileFailure?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(UserModel user)? loginSuccess,
    TResult Function(String message)? loginFailure,
    TResult Function(UserModel user)? verifySuccess,
    TResult Function(String message)? verifyFailure,
    TResult Function(UserModel userModel)? completeProfileSuccess,
    TResult Function(String message)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (completeProfileFailure != null) {
      return completeProfileFailure(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(LoginSuccess value) loginSuccess,
    required TResult Function(LoginFailed value) loginFailure,
    required TResult Function(VerifySuccess value) verifySuccess,
    required TResult Function(VerifyFailed value) verifyFailure,
    required TResult Function(CompleteProfileSuccess value)
        completeProfileSuccess,
    required TResult Function(CompleteProfileFailedl value)
        completeProfileFailure,
  }) {
    return completeProfileFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(LoginSuccess value)? loginSuccess,
    TResult? Function(LoginFailed value)? loginFailure,
    TResult? Function(VerifySuccess value)? verifySuccess,
    TResult? Function(VerifyFailed value)? verifyFailure,
    TResult? Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult? Function(CompleteProfileFailedl value)? completeProfileFailure,
  }) {
    return completeProfileFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(LoginSuccess value)? loginSuccess,
    TResult Function(LoginFailed value)? loginFailure,
    TResult Function(VerifySuccess value)? verifySuccess,
    TResult Function(VerifyFailed value)? verifyFailure,
    TResult Function(CompleteProfileSuccess value)? completeProfileSuccess,
    TResult Function(CompleteProfileFailedl value)? completeProfileFailure,
    required TResult orElse(),
  }) {
    if (completeProfileFailure != null) {
      return completeProfileFailure(this);
    }
    return orElse();
  }
}

abstract class CompleteProfileFailedl implements AuthState {
  const factory CompleteProfileFailedl(final String message) =
      _$CompleteProfileFailedlImpl;

  String get message;

  /// Create a copy of AuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompleteProfileFailedlImplCopyWith<_$CompleteProfileFailedlImpl>
      get copyWith => throw _privateConstructorUsedError;
}
