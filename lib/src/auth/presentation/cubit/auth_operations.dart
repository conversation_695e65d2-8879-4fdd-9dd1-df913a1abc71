import 'dart:convert';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/api/dio_factory.dart';
import 'package:r2/core/helpers/shared_preferences_helper.dart';
import 'package:r2/src/auth/data/models/user_model.dart';

mixin AuthOperations {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController? phoneController;
  TextEditingController? nameController;

  String dialingCode = '+20';
  String otpVal = '';

  void initLoginControllers() {
    phoneController = TextEditingController();
  }

  void initCompleteProfileControllers() {
    nameController = TextEditingController();
  }

  void disposeLoginControllers() {
    phoneController?.dispose();
  }

  void onLoginFailure(BuildContext context, String message) {
    context.router.maybePop();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void setUserData(UserModel? user) {
    if (user == null) return;
    SharedPreferencesHelper.set<String>('user', jsonEncode(user));
  }

  UserModel? getUserData() {
    final user = SharedPreferencesHelper.get('user');
    if (user == null) return null;
    return UserModel.fromJson(jsonDecode(user));
  }

  Future<bool> clearUserData() async {
    return await SharedPreferencesHelper.remove('user');
  }

  Future<void> setToken(String? token) async {
    if (token == null) return;
    await SharedPreferencesHelper.setSecuredString('token', token);
    await SharedPreferencesHelper.set('is_token_stored', true);
    DioFactory.setTokenIntoHeader(token);
  }

  Future<String?> getToken() async {
    return await SharedPreferencesHelper.getSecuredString('token');
  }

  bool isUserLoggedIn() {
    final isTokenStored =
        SharedPreferencesHelper.get('is_token_stored') ?? false;
    final user = getUserData();
    return isTokenStored && user != null;
  }

  Future<void> clearToken() async {
    await SharedPreferencesHelper.remove('is_token_stored');
    await SharedPreferencesHelper.remove('token');
  }

  void clear() {
    disposeLoginControllers();
    otpVal = '';
  }
}
