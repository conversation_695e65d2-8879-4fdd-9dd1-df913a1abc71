import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/text_styles.dart';

import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/default_country_code_picker.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  void onSubmit() async {
    if (!formKey.currentState!.validate()) return;
    formKey.currentState!.save();
      context.read<AuthCubit>().login();

  }
@override
  void initState() {
    super.initState();
  }
 
  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(context.l10n.enter, style: TextStyles.title20.copyWith()),
          SizedBox(
            height: 32,
          ),
          SizedBox(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: CustomTextField(
                    controller: injector<AuthCubit>().phoneController,
                    keyboardType: TextInputType.phone,
                    onSaved: (value) {},
                    validator: (value) {
                      if (value != null && value.isEmpty) {
                        return context.l10n.pleaseEnterValidUserPhone;
                      }
                      return null;
                    },
                    labelText: context.l10n.phoneNumber,
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                Expanded(child: DefaultCountryCodePicker(
                  onChanged: (value) {

                    injector<AuthCubit>().dialingCode = value.dialCode ?? "+20";
                  },
                ))
              ],
            ),
          ),
          const SizedBox(
            height: 48,
          ),
          CustomElevatedButton(
            onPressed: onSubmit,
            child: Text(context.l10n.enter),
          ),
          const SizedBox(
            height: 56,
          ),
        
        ],
      ),
    );
  }
}
