import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';

class WelcomeForm extends StatefulWidget {
  const WelcomeForm({super.key});

  @override
  State<WelcomeForm> createState() => _WelcomeFormState();
}

class _WelcomeFormState extends State<WelcomeForm> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  void onSubmit() async {
    if (!formKey.currentState!.validate()) return;
    formKey.currentState!.save();
    context.read<AuthCubit>().completeProfile();
  }
@override
  void initState() {
    injector<AuthCubit>().initCompleteProfileControllers();
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.l10n.welcome,
            style: TextStyles.title20,
          ),
          const SizedBox(
            height: 16,
          ),
          CustomTextField(
            controller: context.read<AuthCubit>().nameController,
            onSaved: (value) {},
            validator: (value) {
              if (value != null && value.isEmpty) {
                return context.l10n.pleaseEnterYourName;
              }
              return null;
            },
            labelText: context.l10n.name,
          ),
          const SizedBox(
            height: 16,
          ),
          Text(
            context.l10n.welcomeMessage,
            style: TextStyles.label14,
          ),
          const SizedBox(
            height: 64,
          ),
          CustomElevatedButton(
            onPressed: onSubmit,
            child: Text(context.l10n.startNow),
          )
        ],
      ),
    );
  }
}
