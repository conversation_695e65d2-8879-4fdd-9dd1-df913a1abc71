import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/src/auth/presentation/count_down_cubit/count_down_cubit.dart';
import 'package:timer_count_down/timer_count_down.dart';

class ResendOtp extends StatelessWidget {
  const ResendOtp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CountDownCubit, CountDownState>(
        builder: (context, state) {
      return !context.read<CountDownCubit>().isFinished
          ? Container(
              color: AppColors.background1,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.l10n.otpNotSent,
                    style: TextStyles.label11,
                  ),
                  Countdown(
                      controller: context.read<CountDownCubit>().controller,
                      seconds: 20,
                      onFinished: () =>
                          context.read<CountDownCubit>().onCountDownEnd(),
                      build: (context, time) {
                        int minutes = time ~/ 60;
                        double seconds = time % 60;
                        return Text(
                          '${minutes.toString().padLeft(2, '0')}:${seconds.toInt().toString().padLeft(2, '0')}',
                          style: TextStyle(
                              fontSize: 24.sp, color: AppColors.primary,fontWeight: FontWeight.w700),
                        );
                      })
                ],
              ))
          : CustomElevatedButton(
              backgroundColor: Colors.transparent,
              borderColor: AppColors.primary,
              isLight: false,
              onPressed: () => context.read<CountDownCubit>().resetCount(),
              child: Text(
                context.l10n.resesnd,
                style: TextStyles.body16.copyWith(color: AppColors.primary),
              ),
            );
    });
  }
}
