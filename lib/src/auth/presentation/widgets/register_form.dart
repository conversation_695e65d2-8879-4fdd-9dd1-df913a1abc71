import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/default_country_code_picker.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';

class RegisterForm extends StatefulWidget {
  const RegisterForm({super.key});

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
    void onSubmit() async {
    // if (!formKey.currentState!.validate()) return;
    // formKey.currentState!.save();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: form<PERSON><PERSON>,
      child: Column(
        children: [
          LabeledField(
            field: SizedBox(
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: CustomTextField(
                      keyboardType: TextInputType.phone,
                      onSaved: (value) {},
                      validator: (value) {
                        if (value != null && value.isEmpty) {
                          return context.l10n.pleaseEnterValidUserPhone;
                        }
                        return null;
                      },
                      hintText: context.l10n.phoneNumber,
                    ),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  const Expanded(child: DefaultCountryCodePicker())
                ],
              ),
            ),
            title: context.l10n.enter,
          ),
          const SizedBox(
            height: 24,
          ),
          CustomElevatedButton(
            onPressed: onSubmit,
            child: Text(context.l10n.enter),
          ),
          const SizedBox(
            height: 56,
          ),
          Text(context.l10n.donotHaveAccount),
          const SizedBox(
            height: 24,
          ),
          GestureDetector(
            onTap: () => context.router.push(const RegisterRoute()),
            child: Text(
              context.l10n.register,
              style: TextStyles.title20.copyWith(color: AppColors.primary),
            ),
          )
        ],
      ),
    );
  }
}
