import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';

class OtpForm extends StatelessWidget {
  const OtpForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          textAlign: TextAlign.right,
          context.l10n.pleaseEnterOtp,
          style: TextStyles.label14,
        ),
        const SizedBox(
          height: 16,
        ),
        Text(
          injector<AuthCubit>().getUserData()?.phone ?? '-',
          style: TextStyles.label14.copyWith(fontWeight: FontWeight.w700),
        ),
        const Sized<PERSON><PERSON>(
          height: 32,
        ),
        Directionality(
          textDirection: TextDirection.ltr,
          child: PinCodeTextField(
            onChanged: (value) {
              injector<AuthCubit>().otpVal = value;
            },
            onCompleted: (value) {
              injector<AuthCubit>().verify();
            },
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            appContext: context,
            length: 4,
            autoFocus: true,
            keyboardType: TextInputType.number,
            cursorColor: AppColors.primary,
            hintCharacter: '',
            obscureText: false,
            animationType: AnimationType.scale,
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              borderRadius: BorderRadius.circular(10),
              fieldHeight: 70,
              fieldWidth: 70,
              borderWidth: 0,
              activeColor: AppColors.background1,
              inactiveColor: AppColors.background1,
              selectedColor: AppColors.background1,
              activeFillColor: AppColors.background1,
              inactiveFillColor: AppColors.background1,
              selectedFillColor: AppColors.background1,
            ),
            enableActiveFill: true,
          ),
        ),
      ],
    );
  }
}
