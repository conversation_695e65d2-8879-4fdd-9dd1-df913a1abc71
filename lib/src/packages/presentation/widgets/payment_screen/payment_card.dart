import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/gen/assets.gen.dart';

class PaymentCard extends StatelessWidget {
  const PaymentCard({super.key, required this.index});
  final int index;

  @override
  Widget build(BuildContext context) {
    final toggleCubit = context.watch<ToggleCubit<int>>();
    return GestureDetector(
      onTap: () => toggleCubit.toggle(index),
      child: Container(
        padding: EdgeInsets.only(top: 6, right: 12, bottom: 16, left: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppColors.darkPurple,
          border: Border.all(
            color: toggleCubit.value == index
                ? AppColors.primary
                : Colors.transparent,
          ),
          boxShadow: toggleCubit.value == index
              ? [
                  BoxShadow(
                    color: AppColors.primary,
                    blurRadius: 18.0,
                    spreadRadius: 1,
                  )
                ]
              : [],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بطاقات الدفع',
              style: TextStyles.body14,
            ),
            const SizedBox(height: 8),
            Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              child: Assets.images.dummyData.image(
                width: 300,
                fit: BoxFit.fill,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
