import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/gen/assets.gen.dart';

class PackageCard extends StatelessWidget {
  const PackageCard({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.router.push(const PaymentRoute()),
      child: Container(
        padding: EdgeInsets.only(top: 16, bottom: 16),
        decoration: BoxDecoration(
          color: AppColors.darkPurple,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      height: 45,
                      padding:
                          EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppColors.card,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          bottomLeft: Radius.circular(12),
                        ),
                      ),
                      child: Text(
                        'باقه مجانيه',
                        style: TextStyles.body16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      context.l10n.subscripNow,
                      style: TextStyles.body12.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  )
                ],
              ),
            ),
            const SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IntrinsicWidth(
                        child: ListTile(
                          contentPadding: EdgeInsets.zero,
                          leading: Assets.icons.correctIcon.svg(),
                          visualDensity:
                              VisualDensity(horizontal: -4, vertical: -4),
                          title: Text(
                            '1 شهر',
                            style: TextStyles.body16,
                          ),
                        ),
                      ),
                      IntrinsicWidth(
                        child: ListTile(
                          contentPadding: EdgeInsets.zero,
                          visualDensity:
                              VisualDensity(horizontal: -4, vertical: -4),
                          title: Text(
                            "١ غرفه",
                            style: TextStyles.body16,
                          ),
                          leading: Assets.icons.correctIcon.svg(),
                        ),
                      )
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "خصم  30%",
                        style: TextStyles.body12.copyWith(
                            color: Colors.green, fontWeight: FontWeight.w700),
                      ),
                      Row(
                        children: [
                          Text(
                            '100',
                            style: TextStyles.title20.copyWith(
                              decoration: TextDecoration.lineThrough,
                              decorationColor: AppColors.grey,
                              color: AppColors.grey,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Assets.icons.moneyIcon.svg(),
                          const SizedBox(width: 8),
                          Text(
                            '200',
                            style: TextStyles.headline32.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
