import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';

class PromoCodeSection extends StatefulWidget {
  const PromoCodeSection({super.key});

  @override
  State<PromoCodeSection> createState() => _PromoCodeSectionState();
}

class _PromoCodeSectionState extends State<PromoCodeSection> {
  TextEditingController controller = TextEditingController();
  bool isEmpty = false;
  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LabeledField(
      title: context.l10n.doYouHavePromoCode,
      field: CustomTextField(
        controller: controller,
        onChanged: (value) {
          setState(() {
            if (value != null && value.isNotEmpty) {
              isEmpty = false;
            } else {
              isEmpty = true;
            }
          });
        },
        hintText: context.l10n.promoCode,
        suffixIcon: FittedBox(
          fit: BoxFit.contain,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18.0),
            child: CustomElevatedButton(
                width: 1,
                height: 1,
                isLight: false,
                backgroundColor:
                    !isEmpty ? Colors.transparent : AppColors.primary,
                borderColor: !isEmpty ? AppColors.primary : AppColors.primary,
                child: Text(
                  !isEmpty ? context.l10n.remove : context.l10n.confirm,
                  style: TextStyles.body14.copyWith(color: Colors.white),
                ),
                onPressed: () {
                  if (!isEmpty) {
                    controller.clear();
                    setState(() {
                      isEmpty = true;
                    });
                  }
                }),
          ),
        ),
      ),
    );
  }
}
