import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/gen/assets.gen.dart';

class ReceiptImageSection extends StatefulWidget {
  const ReceiptImageSection({super.key});

  @override
  State<ReceiptImageSection> createState() => _ReceiptImageSectionState();
}

class _ReceiptImageSectionState extends State<ReceiptImageSection> {
  File? _image;

  Future<void> _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      setState(() {
        _image = File(pickedFile.path);
      });
    }
  }

  Future<void> _showImagePickerOptions() async {
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: AppColors.darkPurple,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            ListTile(
              leading: const Icon(
                Icons.photo_library,
                color: AppColors.primary,
              ),
              title: const Text('Choose from Gallery'),
              onTap: () {
                _pickImage(ImageSource.gallery);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.photo_camera,
                color: AppColors.primary,
              ),
              title: const Text('Take a Photo'),
              onTap: () {
                _pickImage(ImageSource.camera);
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LabeledField(
          title: context.l10n.bankTransferReceiptEWallets,
          field: GestureDetector(
            onTap: _showImagePickerOptions,
            child: DottedBorder(
              color: AppColors.darkPurple,
              strokeWidth: 2,
              dashPattern: [8, 8],
              borderType: BorderType.RRect,
              radius: const Radius.circular(8),
              child: SizedBox(
                height: 100,
                width: double.infinity,
                child: _image == null
                    ? Center(child: Assets.icons.imagePickerIcon.svg())
                    : Image.file(
                        _image!,
                        fit: BoxFit.fitHeight,
                      ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 120),
        CustomElevatedButton(
          child: Text(context.l10n.pay),
          onPressed: () {
            UiHelper.showCustomDialog(
              barrierDismissible: false,
              context: context,
              dialog: AlertDialog(
                backgroundColor: AppColors.darkPurple,
                icon: Assets.icons.doneIcon.svg(),
                title: Text(
                  context.l10n.subscriptionSuccessfullyActivated,
                  style:
                      TextStyles.title16.copyWith(fontWeight: FontWeight.w700),
                ),
                titlePadding: EdgeInsets.only(),
                actionsPadding:
                    EdgeInsets.only(top: 60, bottom: 24, right: 30, left: 30),
                iconPadding: EdgeInsets.only(
                  top: 30,
                ),
                actions: [
                  CustomElevatedButton(
                    width: 250,
                    isLight: false,
                    child: Text(context.l10n.home),
                    onPressed: () => context.router.maybePop(),
                  )
                ],
              ),
            );
          },
        )
      ],
    );
  }
}
