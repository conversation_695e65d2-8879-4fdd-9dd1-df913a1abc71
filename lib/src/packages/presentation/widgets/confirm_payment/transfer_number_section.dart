import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';

class TransferNumberSection extends StatelessWidget {
  const TransferNumberSection({super.key});

  @override
  Widget build(BuildContext context) {
    return LabeledField(
      title: context.l10n.tansferNumber,
      field: CustomTextField(
        onChanged: (value) {},
        readOnly: true,
        initialValue: '01015620800',
        suffixIcon: FittedBox(
          fit: BoxFit.contain,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18.0),
            child: CustomElevatedButton(
              width: 1,
              height: 1,
              isLight: false,
              child: Text(
                context.l10n.copy,
                style: TextStyles.body14.copyWith(color: Colors.white),
              ),
              onPressed: () async {
                UiHelper.showCustomSnackBar(
                  context: context,
                  message: context.l10n.copied,
                  backgroundColor: AppColors.card,
                  textColor: AppColors.primary,
                );
                await Clipboard.setData(
                  ClipboardData(text: '01015620800'),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
