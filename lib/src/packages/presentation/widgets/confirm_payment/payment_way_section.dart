import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/widgets/shared/labeled_field.dart';
import 'package:r2/gen/assets.gen.dart';

class PaymentWaySection extends StatelessWidget {
  const PaymentWaySection({super.key});

  @override
  Widget build(BuildContext context) {
    return LabeledField(
      title: context.l10n.paymentWay,
      field: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.white,
        ),
        child: Assets.images.dummyData.image(
          width: double.infinity,
          fit: BoxFit.fill,
        ),
      ),
    );
  }
}
