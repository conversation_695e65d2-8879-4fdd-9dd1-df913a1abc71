import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/src/packages/presentation/widgets/my_subscription/previous_subscriptions_item.dart';

class PreviousSubscriptionsSection extends StatelessWidget {
  const PreviousSubscriptionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.previousSubscriptions,
          style: TextStyles.title16.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) => PreviousSubscriptionItem(),
          separatorBuilder: (context, index) => Divider(
            thickness: 1,
            color: Color(0xff484848),
            height: 32,
          ),
          itemCount: 6,
        )
      ],
    );
  }
}
