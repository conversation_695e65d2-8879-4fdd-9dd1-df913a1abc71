import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';

class SubscriptionSection extends StatelessWidget {
  const SubscriptionSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.l10n.currentSubscription,
            style: TextStyles.title16.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "باقة الحريف",
                style: TextStyles.body16.copyWith(
                  color: AppColors.primary,
                ),
              ),
              Text(
                "3 اشهر",
                style: TextStyles.body16.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.expiredAt,
                style: TextStyles.body16.copyWith(color: Color(0xFFBABABA)),
              ),
              Text(
                "12/7/2025",
                style: TextStyles.body16.copyWith(color: Color(0xFFBABABA)),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Divider(
            thickness: 1,
            color: Color(
              0xFF484848,
            ),
          )
        ],
      ),
    );
  }
}
