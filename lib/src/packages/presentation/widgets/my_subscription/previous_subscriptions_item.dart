import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/text_styles.dart';

class PreviousSubscriptionItem extends StatelessWidget {
  const PreviousSubscriptionItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "12/12/2024",
          style: TextStyles.body16.copyWith(
            color: Color(0xffBABABA),
          ),
        ),
        Text(
          "باقه الحريف",
          style: TextStyles.body16.copyWith(
            color: Color(0xffBABABA),
          ),
        ),
        Text(
          context.l10n.expired,
          style: TextStyles.body16.copyWith(
            color: Color(0xffBABABA),
          ),
        ),
      ],
    );
  }
}
