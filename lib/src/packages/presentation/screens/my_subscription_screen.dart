import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/packages/presentation/widgets/my_subscription/previous_subscriptions_section.dart';
import 'package:r2/src/packages/presentation/widgets/my_subscription/subscription_section.dart';

@RoutePage()
class MySubscriptionScreen extends StatelessWidget {
  const MySubscriptionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: defaultAppBar(
          context: context, title: context.l10n.previousSubscriptions),
      body: DefaultScaffoldGradient(
        child: Safe<PERSON>rea(
          child: SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
              child: <PERSON>um<PERSON>(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SubscriptionSection(),
                  const SizedBox(height: 32),
                  PreviousSubscriptionsSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
