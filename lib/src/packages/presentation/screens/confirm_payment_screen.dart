import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/packages/presentation/widgets/confirm_payment/payment_way_section.dart';
import 'package:r2/src/packages/presentation/widgets/confirm_payment/promo_code_section.dart';
import 'package:r2/src/packages/presentation/widgets/confirm_payment/receipt_image_section.dart';
import 'package:r2/src/packages/presentation/widgets/confirm_payment/transfer_number_section.dart';
import 'package:r2/src/packages/presentation/widgets/packages_screen/package_card.dart';

@RoutePage()
class ConfirmPaymentScreen extends StatelessWidget {
  const ConfirmPaymentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: defaultAppBar(
          context: context, title: context.l10n.confirmSubscriptionAndPayment),
      body: DefaultScaffoldGradient(
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
              child: Column(
                children: [
                  PackageCard(),
                  const SizedBox(height: 24),
                  PromoCodeSection(),
                  const SizedBox(height: 24),
                  PaymentWaySection(),
                  const SizedBox(height: 24),
                  TransferNumberSection(),
                  const SizedBox(height: 32),
                  ReceiptImageSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
