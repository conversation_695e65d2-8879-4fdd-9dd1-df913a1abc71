import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/helpers/cubits/toggle/toggle_cubit.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/packages/presentation/widgets/payment_screen/payment_card.dart';

@RoutePage()
class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  late final ToggleCubit<int> _toggleCubit;

  @override
  void initState() {
    super.initState();
    _toggleCubit = ToggleCubit<int>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: defaultAppBar(
          context: context, title: context.l10n.confirmSubscriptionAndPayment),
      body: DefaultScaffoldGradient(
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.pleaseChooseThePaymentMethodThatSuitsYou,
                    style: TextStyles.body16,
                  ),
                  const SizedBox(height: 20),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) => BlocProvider(
                      create: (context) => _toggleCubit,
                      child: PaymentCard(
                        index: index,
                      ),
                    ),
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 12),
                    itemCount: 4,
                  ),
                  const SizedBox(height: 40),
                  CustomElevatedButton(
                    child: Text(context.l10n.pay),
                    onPressed: () =>
                        context.router.push(const ConfirmPaymentRoute()),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
