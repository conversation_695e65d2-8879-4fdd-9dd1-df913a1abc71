import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/packages/presentation/widgets/packages_screen/package_card.dart';

@RoutePage()
class PackagesScreen extends StatelessWidget {
  const PackagesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: defaultAppBar(
          context: context, title: context.l10n.subscriptionPackages),
      body: DefaultScaffoldGradient(
        child: SafeArea(
          child: ListView.separated(
            padding: EdgeInsets.only(top: 24, right: 16, left: 16),
            itemBuilder: (context, index) => const PackageCard(),
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemCount: 9,
          ),
        ),
      ),
    );
  }
}
