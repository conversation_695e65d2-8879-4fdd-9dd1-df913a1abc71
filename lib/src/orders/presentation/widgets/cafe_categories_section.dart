import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/config/router/app_router.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';

class CafeCategoriesSection extends StatelessWidget {
  const CafeCategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          List.generate(injector<MenuCubit>().categories?.length ?? 0, (index) {
        return Column(
          children: [
            GestureDetector(
              onTap: () {
                context.router.push(ProductsRoute(
                    category: injector<MenuCubit>().categories![index]));
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: AppColors.darkPurple,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.card,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16), // Top-left corner
                          bottomLeft: Radius.circular(16), // Bottom-left corner
                        ),
                      ),
                      width: 90,
                      height: 60,
                      padding: const EdgeInsets.all(8),
                      child: injector<MenuCubit>().returnedIcon(
                          injector<MenuCubit>().categories?[index].icon ?? ''),
                    ),
                    SizedBox(
                      width: 16,
                    ),
                    Text(
                      injector<MenuCubit>().categories?[index].name ?? '',
                      style: TextStyles.title16,
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        );
      }),
    );
  }
}
