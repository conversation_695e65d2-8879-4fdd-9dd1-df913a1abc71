import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/core/widgets/custom/custom_elevated_button.dart';
import 'package:r2/core/widgets/custom/custom_text_field.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';

class AddItemModal extends StatefulWidget {
  const AddItemModal({super.key, this.product});
  final CafeProductModel? product;

  @override
  State<AddItemModal> createState() => _AddItemModalState();
}

class _AddItemModalState extends State<AddItemModal> {
  final reservationCubit = injector<ReservationCubit>();
  final _formKey = GlobalKey<FormState>();
  int quantity = 0;
  @override
  void dispose() {
    reservationCubit.clearMakeOrderParams();
    super.dispose();
  }

  void incrementQuantity() {
    setState(() {
      quantity++;
    });
    reservationCubit.quantity = quantity;
  }

  void decrementQuantity() {
    setState(() {
      quantity--;
    });
    if (quantity < 0) {
      quantity = 0;
    }
    reservationCubit.quantity = quantity;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(
            top: 32,
            bottom: MediaQuery.of(context).viewInsets.bottom,
            right: 16,
            left: 16),
        // width: double.infinity,
        // decoration: BoxDecoration(
        //   color: AppColors.darkPurple,
        //   borderRadius: BorderRadius.only(
        //     topLeft: Radius.circular(32),
        //     topRight: Radius.circular(32),
        //   ),
        // ),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.product?.name ?? '',
                style: TextStyles.title16.copyWith(fontWeight: FontWeight.w700),
              ),
              SizedBox(
                height: 32,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.l10n.quantity,
                    style: TextStyles.title16
                        .copyWith(fontWeight: FontWeight.w400),
                  ),
                  SizedBox(
                    width: 150,
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: incrementQuantity,
                          child: Container(
                            width: 40,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    width: 3, color: AppColors.primary)),
                            child: Center(
                              child: Icon(Icons.add),
                            ),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          quantity.toString(),
                          style: context.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: decrementQuantity,
                          child: Container(
                            width: 40,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    width: 3, color: AppColors.primary)),
                            child: Center(
                              child: Icon(Icons.remove),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 32,
              ),
              CustomTextField(
                maxLines: 3,
                hintText: context.l10n.notes,
                fillColor: AppColors.darkGrey,
                onSaved: (value) {
                  reservationCubit.notes = value;
                },
              ),
              SizedBox(
                height: 32,
              ),
              CustomElevatedButton(
                child: Text(context.l10n.add),
                onPressed: () {
                  _formKey.currentState?.save();
                  reservationCubit.productId = widget.product?.id ?? 0;
                  reservationCubit
                      .makeOrder(reservationCubit.reservationModel?.id ?? 0);
                },
              ),
              SizedBox(
                height: 32,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
