import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/core/theme/text_styles.dart';
import 'package:r2/src/menu/data/models/product_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/orders/presentation/widgets/add_item_modal.dart';

class ProductItem extends StatelessWidget {
  final bool? isValid;
  final CafeProductModel? product;
  const ProductItem({super.key, this.isValid = true, this.product});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 16, left: 16, bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.darkPurple,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.card,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
            width: 66,
            height: 60,
            padding: const EdgeInsets.all(8),
            child: injector<MenuCubit>().returnedIcon(product?.icon ?? ''),
          ),
          SizedBox(
            width: 16,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                product?.name ?? '-',
                style: TextStyles.title16.copyWith(fontWeight: FontWeight.w400),
              ),
              Text(
                '${product?.price ?? 0} جم',
                style: TextStyles.title14.copyWith(fontWeight: FontWeight.w700),
              ),
            ],
          ),
          Spacer(),
          product?.isAvailable ?? true == true
              ? Container(
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(width: 3, color: AppColors.primary)),
                  child: Center(
                    child: GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          isScrollControlled: true,
                          backgroundColor: AppColors.darkPurple,
                          context: context,
                          builder: (_) => AddItemModal(
                            product: product,
                          ),
                        );
                      },
                      child: const Icon(
                        Icons.add,
                        weight: 1,
                        size: 24,
                        color: Colors.white,
                      ),
                    ),
                  ),
                )
              : Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    color: Colors.red,
                  ),
                  child: Text(
                    context.l10n.notavailable,
                    style: TextStyles.body12.copyWith(color: Colors.white),
                  ))
        ],
      ),
    );
  }
}
