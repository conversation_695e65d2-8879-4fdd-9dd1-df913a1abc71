import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/orders/presentation/widgets/cafe_categories_section.dart';

@RoutePage()
class CafeCategoriesScreen extends StatelessWidget implements AutoRouteWrapper {
  const CafeCategoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<MenuCubit, MenuState>(
      listenWhen: (previous, current) => current.maybeWhen(
        loading: () => true,
        getCategoriesFailure: (_) => true,
        getCategoriesSuccess: () => true,
        orElse: () {
          return false;
        },
      ),
      listener: (context, state) {
        state.whenOrNull(
          getCategoriesSuccess: () => UiHelper.onSuccess(),
          getCategoriesFailure: (message) =>
              UiHelper.onFailure(context, message),
          loading: () => UiHelper.onLoading(context),
        );
      },
      child: Scaffold(
        appBar: defaultAppBar(context: context, title: context.l10n.cafeteria),
        extendBodyBehindAppBar: true,
        body: DefaultScaffoldGradient(
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(
                    top: 30.0, bottom: 40, right: 16, left: 16),
                child: Column(
                  children: [
                    Assets.images.cafe.image(),
                    SizedBox(height: 16),
                    BlocBuilder<MenuCubit, MenuState>(
                      builder: (context, state) {
                        return CafeCategoriesSection();
                      },
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
        value: injector<MenuCubit>()..getCategories(), child: this);
  }
}
