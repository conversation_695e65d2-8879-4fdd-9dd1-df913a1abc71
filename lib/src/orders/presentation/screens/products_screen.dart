import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/helpers/ui_helper.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/widgets/shared/default_app_bar.dart';
import 'package:r2/core/widgets/shared/default_scaffold_gradient.dart';
import 'package:r2/src/menu/data/models/category_model.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/orders/presentation/widgets/product_item.dart';

@RoutePage()
class ProductsScreen extends StatelessWidget implements AutoRouteWrapper {
  final CafeCategoryModel? category;
  const ProductsScreen({super.key, this.category});

  @override
  Widget build(BuildContext context) {
    return BlocListener<MenuCubit, MenuState>(
      listenWhen: (previous, current) => current.maybeWhen(
          orElse: () => false,
          getProducrsFailure: (message) => true,
          getProductsSuccess: () => true,
          loading: () => true),
      listener: (context, state) {
        state.whenOrNull(
          loading: () => UiHelper.onLoading(context),
          getProductsSuccess: () => UiHelper.onSuccess(),
          getProducrsFailure: (message) => UiHelper.onFailure(context, message),
        );
      },
      child: Scaffold(
        resizeToAvoidBottomInset:
            true, // This will allow resizing when keyboard shows up

        appBar: defaultAppBar(context: context, title: category?.name),
        extendBodyBehindAppBar: true,
        body: DefaultScaffoldGradient(
          child: SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.only(
                  top: 30.0, bottom: 16, right: 16, left: 16),
              child: BlocBuilder<MenuCubit, MenuState>(
                builder: (context, state) {
                  return ListView.separated(
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 16),
                    itemCount: injector<MenuCubit>().products?.length ?? 0,
                    itemBuilder: (context, index) {
                      return ProductItem(
                        product: injector<MenuCubit>().products?[index],
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider.value(
        value: injector<MenuCubit>()..getProducts(category?.id ?? 0),
        child: this);
  }
}
