import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/src/reports/presentation/screens/reports_view.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';
import 'package:r2/src/rooms/presentation/screens/rooms_view.dart';
import 'package:r2/src/settings/presentation/screens/settings_view.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';
import 'package:r2/src/worker/presentation/screens/worker_view.dart';
part 'bottom_nav_bar_state.dart';

class BottomNavBarCubit extends Cubit<BottomNavBarState> {
  BottomNavBarCubit() : super(BottomNavBarInitial());

  final List<Widget> bodies = [
    BlocProvider.value(
      value: injector<RoomsCubit>(),
      child: const RoomsView(),
    ),
    const ReportsView(),
    BlocProvider.value(
      value:  injector<WorkerCubit>(),
      child: const WorkerView(),
    ),
    const SettingsView(),
  ];

  void changeIndex(int index) {
    emit(BottomNavBarChanged(index));
  }
}
