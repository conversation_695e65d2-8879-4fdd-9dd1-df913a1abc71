part of 'bottom_nav_bar_cubit.dart';

class BottomNavBarState extends Equatable {
  final int index;
  const BottomNavBarState({this.index = 0});
  @override
  List<Object?> get props => [index];
}

class BottomNavBarInitial extends BottomNavBarState {}

class BottomNavBarChanged extends BottomNavBarState {
  const BottomNavBarChanged(int index) : super(index: index);
  @override
  List<Object?> get props => [index];
}
