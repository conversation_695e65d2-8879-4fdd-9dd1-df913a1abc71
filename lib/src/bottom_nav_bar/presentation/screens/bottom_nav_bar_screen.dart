import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/src/bottom_nav_bar/cubit/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:r2/src/bottom_nav_bar/presentation/widgets/default_bottom_nav_bar.dart';

@RoutePage()
class BottomNavBarScreen extends StatefulWidget implements AutoRouteWrapper {
  const BottomNavBarScreen({super.key});

  @override
  State<BottomNavBarScreen> createState() => _BottomNavBarScreenState();

  @override
  Widget wrappedRoute(BuildContext context) =>
      BlocProvider.value(value: injector<BottomNavBarCubit>(), child: this);
}

class _BottomNavBarScreenState extends State<BottomNavBarScreen> {
  @override
  void dispose() {
    injector<BottomNavBarCubit>().changeIndex(0);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      bottomNavigationBar: DefaultBottomNavBar(),
      extendBody: true,
      body: BlocBuilder<BottomNavBarCubit, BottomNavBarState>(
        builder: (context, state) {
          return injector<BottomNavBarCubit>().bodies[state.index];
        },
      ),
    );
  }
}
