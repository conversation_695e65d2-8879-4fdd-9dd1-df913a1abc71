import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/gen/assets.gen.dart';
import 'package:r2/src/bottom_nav_bar/cubit/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';

class DefaultBottomNavBar extends StatelessWidget {
  const DefaultBottomNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BottomNavBarCubit, BottomNavBarState>(
      builder: (context, state) {
        return Theme(
          data: ThemeData(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            hoverColor: Colors.transparent,
            splashFactory: NoSplash.splashFactory,
          ),
          child: BottomNavigationBar(
            currentIndex: state.index,
            onTap: (index) {
              if (context.router.current.name != 'BottomNavBarRoute') {
                context.router.popUntilRoot();
              }
              context.read<BottomNavBarCubit>().changeIndex(index);
            },
            backgroundColor: Colors.transparent,
            type: BottomNavigationBarType.fixed,
            enableFeedback: true,
            useLegacyColorScheme: false,
            fixedColor: Colors.transparent,
            elevation: 0,
            items: [
              BottomNavigationBarItem(
                backgroundColor: Colors.transparent,
                icon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.homeIconInactive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                activeIcon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.homeIconActive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                backgroundColor: Colors.transparent,
                icon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.reportsIconInactive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                activeIcon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.reportsIconActive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                backgroundColor: Colors.transparent,
                icon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.workerIconInactive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                activeIcon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.workerIconActive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                label: '',
              ),
              BottomNavigationBarItem(
                backgroundColor: Colors.transparent,
                icon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.settingIconInactive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                activeIcon: Container(
                  padding: const EdgeInsets.all(18),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xff141222),
                        Color(0xff363637),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary,
                        blurRadius: 24,
                        spreadRadius: 1,
                      )
                    ],
                  ),
                  child: Assets.icons.settingIconActive.svg(
                    alignment: Alignment.center,
                  ),
                ),
                label: '',
              ),
            ],
          ),
        );
      },
    );
  }
}
