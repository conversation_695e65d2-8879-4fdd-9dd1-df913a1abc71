import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextStyles {
  static TextStyle get label11 => TextStyle(
        fontSize: 11.sp,
        fontWeight: FontWeight.w500,
        // height: 16,
        wordSpacing: 0.5,
      );

  static TextStyle get label12 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        // height: 16,
        wordSpacing: 0.5,
      );

  static TextStyle get label14 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        // height: 20,
        wordSpacing: 0.1,
      );

  static TextStyle get body12 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        // height: 16,
        wordSpacing: 0.4,
      );

  static TextStyle get body14 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        // height: 20,
        wordSpacing: 0.25,
      );

  static TextStyle get body16 => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        // height: 24,
        wordSpacing: 0.15,
      );

  static TextStyle get title14 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        // height: 20,
        wordSpacing: 0.1,
      );

  static TextStyle get title16 => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        // height: 24,
        wordSpacing: 0.15,
      );
   static TextStyle get title20 => TextStyle(
        fontSize: 22.sp,
        fontWeight: FontWeight.w700,
        // height: 28,
        wordSpacing: 0,
      );    

  static TextStyle get title22 => TextStyle(
        fontSize: 22.sp,
        fontWeight: FontWeight.w500,
        // height: 28,
        wordSpacing: 0,
      );

  static TextStyle get headline24 => TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.w400,
        // height: 32,
        wordSpacing: 0,
      );

  static TextStyle get headline28 => TextStyle(
        fontSize: 28.sp,
        fontWeight: FontWeight.w400,
        // height: 36,
        wordSpacing: 0,
      );

  static TextStyle get headline32 => TextStyle(
        fontSize: 32.sp,
        fontWeight: FontWeight.w400,
        // height: 40,
        wordSpacing: 0,
      );

  static TextStyle get display36 => TextStyle(
        fontSize: 36.sp,
        fontWeight: FontWeight.w400,
        // height: 44,
        wordSpacing: 0,
      );

  static TextStyle get display45 => TextStyle(
        fontSize: 45.sp,
        fontWeight: FontWeight.w400,
        // height: 52,
        wordSpacing: 0,
      );

  static TextStyle get display57 => TextStyle(
        fontSize: 57.sp,
        fontWeight: FontWeight.w400,
        // height: 64,
        wordSpacing: 0,
      );
}
