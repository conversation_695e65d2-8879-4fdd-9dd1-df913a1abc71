import 'package:dio/dio.dart';
// import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:r2/src/auth/data/api_service/auth_api_service.dart';
import 'package:r2/src/auth/data/repository/auth_repo.dart';
import 'package:r2/src/auth/presentation/cubit/auth_cubit.dart';
import 'package:r2/src/bottom_nav_bar/cubit/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:r2/src/reservation/data/api_service/reservation_api_service.dart';
import 'package:r2/src/reservation/data/repository/reservation_repo.dart';
import 'package:r2/src/reservation/presentation/cubit/reservation_cubit.dart';
import 'package:r2/src/rooms/data/api_service/rooms_api_service.dart';
import 'package:r2/src/rooms/data/repository/rooms_repo.dart';
import 'package:r2/src/rooms/presentation/cubit/rooms_cubit.dart';
import 'package:r2/src/branches/data/api_service/branches_api_service.dart';
import 'package:r2/src/branches/data/repository/branches_repo.dart';
import 'package:r2/src/branches/presentation/cubit/halls_cubit.dart';
import 'package:r2/src/menu/data/api_service/menu_api_service.dart';
import 'package:r2/src/menu/data/repository/menu_repo.dart';
import 'package:r2/src/menu/presentation/cubit/menu_cubit.dart';
import 'package:r2/src/splash/data/api_service/splash_api_service.dart';
import 'package:r2/src/splash/data/repository/splash_reposetory.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';
import 'package:r2/src/worker/data/api_service/worker_api_service.dart';
import 'package:r2/src/worker/data/repository/worker_repo.dart';
import 'package:r2/src/worker/presentation/cubit/worker_cubit.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/router/app_router.dart';
import '../../src/localization/data/localization_local_source.dart';
import '../../src/localization/data/localization_use_case.dart';
import '../../src/localization/presentation/cubit/localization_cubit.dart';
import '../api/api_service.dart';
import '../api/dio_factory.dart';

final injector = GetIt.instance;

Future<void> initInjector() async {
  // Splash
  injector.registerLazySingleton(() => SplashReposetory(injector()));
  injector.registerLazySingleton<SplashApiService>(
      () => SplashApiService(injector()));
  injector.registerLazySingleton(() => SplashCubit(injector()));
  // Auth
  injector.registerLazySingleton(() => AuthRepo(injector()));
  injector
      .registerLazySingleton<AuthApiService>(() => AuthApiService(injector()));
  injector.registerLazySingleton(() => AuthCubit(injector()));
  // Rooms
  injector.registerLazySingleton(() => RoomsRepo(injector()));
  injector.registerLazySingleton<RoomsApiService>(
      () => RoomsApiService(injector()));
  injector.registerLazySingleton(() => RoomsCubit(injector()));
// Branches
  injector.registerLazySingleton(() => BranchesRepo(injector()));
  injector.registerLazySingleton<BranchesApiService>(
      () => BranchesApiService(injector()));
  injector.registerLazySingleton(() => HallsCubit(injector()));
  //Reservation
  injector.registerLazySingleton(() => ReservationRepo(injector()));
  injector.registerLazySingleton<ReservationApiService>(
      () => ReservationApiService(injector()));
  injector.registerLazySingleton(() => ReservationCubit(injector()));
  // Menu
   injector.registerLazySingleton(() => MenuRepo(injector()));
  injector.registerLazySingleton<MenuApiService>(() => MenuApiService(injector()));
  injector.registerLazySingleton(() => MenuCubit(injector()));
  // Worker
   injector.registerLazySingleton(() => WorkerRepo(injector()));
  injector.registerLazySingleton<WorkerApiService>(() => WorkerApiService(injector()));
  injector.registerLazySingleton(() => WorkerCubit(injector()));
  // Routes
  final appRouter = AppRouter();
  injector.registerLazySingleton<AppRouter>(() => appRouter);

  //Blocs
  injector.registerLazySingleton(() => BottomNavBarCubit());

  // SharedPreferences
  final sp = await SharedPreferences.getInstance();

  // sp.clear();
  // FlutterSecureStorage().deleteAll();
  injector.registerLazySingleton<SharedPreferences>(() => sp);

  // Localization
  injector.registerLazySingleton(() => LocalizationCubit());
  injector.registerLazySingleton<LocalizationLocaleSource>(
      () => LocalizationLocaleSourceImpl());
  injector.registerLazySingleton<GetLanguageCodeUseCase>(
      () => GetLanguageCodeUseCase());
  injector.registerLazySingleton<SaveLanguageCodeUseCase>(
      () => SaveLanguageCodeUseCase());

  // Dio & ApiService
  Dio dio = DioFactory.instance;
  injector.registerLazySingleton<Dio>(() => dio);
  injector.registerLazySingleton<ApiService>(() => ApiService(injector()));
}
