import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/theme/app_colors.dart';

class DefaultBackButton extends StatelessWidget {
  const DefaultBackButton({
    super.key,
    this.color = AppColors.primary,
    this.onPressed,
  });

  final Color color;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(width: 3, color: AppColors.primary)),
      child: Center(
        child: GestureDetector(
          onTap: onPressed ?? () => context.router.maybePop(),
          child: const Icon(
            Icons.arrow_back,
            weight: 1,
            size: 24,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
