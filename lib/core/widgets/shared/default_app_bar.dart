import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/helpers/extensions.dart';
import 'package:r2/core/widgets/shared/default_back_button.dart';

PreferredSizeWidget? defaultAppBar({
  required BuildContext context,
  String? title,
  List<Widget>? actions,
  Widget? leading,
}) {
  return PreferredSize(
    preferredSize:
        const Size.fromHeight(kToolbarHeight), // Standard AppBar height
    child: Container(
      padding: const EdgeInsets.all(8),
      color: Colors.transparent, // Ensures transparency
      child: AppBar(
        centerTitle: true,
        elevation: 0, // Removes shadow
        surfaceTintColor: Colors.transparent, // Ensures no tint
        backgroundColor: Colors.transparent, // Double-check transparency
        title: title != null
            ? Text(
                title,
                style: context.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              )
            : null,
        leading: leading ??
            (context.router.canPop() ? const DefaultBackButton() : null),
        actions: actions,
        leadingWidth: 35,
      ),
    ),
  );
}
