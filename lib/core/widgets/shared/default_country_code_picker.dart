import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:r2/core/services/injection_container.dart';
import 'package:r2/core/theme/app_colors.dart';
import 'package:r2/src/splash/presentation/cubit/splash_cubit.dart';

class DefaultCountryCodePicker extends StatelessWidget {
  final void Function(CountryCode)? onChanged;
  const DefaultCountryCodePicker({super.key, this.onChanged});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 58,
      decoration: BoxDecoration(
        color: AppColors.darkPurple,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.border,
        ),
      ),
      child: CountryCodePicker(
        initialSelection: injector<SplashCubit>()
                .splashModel
                ?.countries
                ?.firstOrNull
                ?.handle ??
            'eg',
        countryFilter:
            injector<SplashCubit>().splashModel?.countries?.map((code) {
                  return code.handle ?? '';
                }).toList() ??
                [''],
        flagWidth: 50,
        dialogBackgroundColor: AppColors.background1,
        dialogSize: const Size(200, 200),
        hideSearch: true,
        showCountryOnly: false,
        boxDecoration: BoxDecoration(
          color: AppColors.background1,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: AppColors.background1,
              blurRadius: 24.0,
              spreadRadius: 1.0,
            ),
          ],
        ),
        onChanged:onChanged
      ),
    );
  }
}
