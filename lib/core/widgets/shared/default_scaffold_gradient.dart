import 'package:flutter/material.dart';
import 'package:r2/core/theme/app_colors.dart';

class DefaultScaffoldGradient extends StatelessWidget {
  const DefaultScaffoldGradient({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.background1,
            AppColors.background2,
          ],
        ),
      ),
      child: child,
    );
  }
}
