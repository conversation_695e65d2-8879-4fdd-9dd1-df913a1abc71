import 'package:flutter/material.dart';
import 'package:r2/core/theme/text_styles.dart';

class LabeledField extends StatelessWidget {
  final bool isReq;
  final String title;
  final Widget field;
  const LabeledField({
    super.key,
    this.isReq = false,
    required this.title,
    required this.field,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyles.body14,
            ),
            const SizedBox(
              width: 4,
            ),
          ],
        ),
        SizedBox(
          height: isReq ? 2 : 8,
        ),
        field,
      ],
    );
  }
}
