import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    this.child,
    this.onPressed,
    this.width = double.infinity,
    this.height = 50,
    this.backgroundColor = AppColors.primary,
    this.borderColor = Colors.transparent,
    this.foregroundColor = Colors.white,
    this.isLight = true,
    this.isDisabled = false,
  });

  final Widget? child;
  final void Function()? onPressed;
  final double width;
  final double height;
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
  final bool isLight;
  final bool isDisabled;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          if (isLight)
            BoxShadow(
              color: !isDisabled
                  ? AppColors.primary
                  : AppColors.primary.withOpacity(0.25),
              blurRadius: 24.0,
              spreadRadius: 1.0,
            ),
        ],
      ),
      child: ElevatedButton(
        style: Theme.of(context).elevatedButtonTheme.style!.copyWith(
              minimumSize: WidgetStatePropertyAll(Size(width, height)),
              backgroundColor: !isDisabled
                  ? WidgetStatePropertyAll(backgroundColor)
                  : WidgetStatePropertyAll(
                      backgroundColor.withOpacity(0.25),
                    ),
              foregroundColor: !isDisabled
                  ? WidgetStatePropertyAll(foregroundColor)
                  : const WidgetStatePropertyAll(
                      AppColors.grey,
                    ),
              side: WidgetStatePropertyAll(
                BorderSide(
                  color: borderColor,
                ),
              ),
            ),
        onPressed: onPressed,
        child: child,
      ),
    );
  }
}
