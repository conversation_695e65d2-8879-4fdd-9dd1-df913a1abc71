import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:r2/core/theme/app_colors.dart';

class CustomDropDownButton<T> extends StatelessWidget {
  const CustomDropDownButton({
    super.key,
    required this.hintText,
    this.value,
    this.items = const [],
    this.filled = true,
    this.buttonLabel,
    this.buttonValue,
    this.buttonOnPressed,
    this.onSaved,
    this.onChanged,
    this.onValidate,
  });
  final String hintText;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final bool filled;
  final String? buttonLabel;
  final T? buttonValue;
  final void Function()? buttonOnPressed;
  final void Function(T? value)? onSaved;
  final void Function(T? value)? onChanged;
  final String? Function(T? value)? onValidate;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return DropdownButtonFormField<T>(
      dropdownColor: AppColors.darkPurple,
      iconEnabledColor: AppColors.primary,
      hint: Text(
        hintText,
      ),
      decoration: InputDecoration(
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: filled,
        fillColor: AppColors.darkPurple,
      ),
      style: theme.textTheme.bodyMedium!.copyWith(
        color: Colors.white,
        fontWeight: FontWeight.w500,
      ),
      menuMaxHeight: 250.h,
      items: [
        if (buttonLabel != null)
          DropdownMenuItem(
            value: buttonValue,
            enabled: false,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.add,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 4),
                TextButton(
                  onPressed: buttonOnPressed,
                  child: Text(buttonLabel!),
                ),
              ],
            ),
          ),
        ...items,
      ],
      value: value,
      onChanged: onChanged ?? (value) {},
      onSaved: onSaved,
      validator: onValidate,
      isDense: true,
    );
  }
}
