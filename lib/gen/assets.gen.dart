/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/acIcon.svg
  SvgGenImage get acIcon => const SvgGenImage('assets/icons/acIcon.svg');

  /// File path: assets/icons/addLogoIcon.svg
  SvgGenImage get addLogoIcon =>
      const SvgGenImage('assets/icons/addLogoIcon.svg');

  /// File path: assets/icons/coffeIcon.svg
  SvgGenImage get coffeIcon => const SvgGenImage('assets/icons/coffeIcon.svg');

  /// File path: assets/icons/cold.svg
  SvgGenImage get cold => const SvgGenImage('assets/icons/cold.svg');

  /// File path: assets/icons/confirmIcon.svg
  SvgGenImage get confirmIcon =>
      const SvgGenImage('assets/icons/confirmIcon.svg');

  /// File path: assets/icons/correctIcon.svg
  SvgGenImage get correctIcon =>
      const SvgGenImage('assets/icons/correctIcon.svg');

  /// File path: assets/icons/correctIconGreen.svg
  SvgGenImage get correctIconGreen =>
      const SvgGenImage('assets/icons/correctIconGreen.svg');

  /// File path: assets/icons/correctIconPurple.svg
  SvgGenImage get correctIconPurple =>
      const SvgGenImage('assets/icons/correctIconPurple.svg');

  /// File path: assets/icons/dessert.svg
  SvgGenImage get dessert => const SvgGenImage('assets/icons/dessert.svg');

  /// File path: assets/icons/doneIcon.svg
  SvgGenImage get doneIcon => const SvgGenImage('assets/icons/doneIcon.svg');

  /// File path: assets/icons/doorIcon.svg
  SvgGenImage get doorIcon => const SvgGenImage('assets/icons/doorIcon.svg');

  /// File path: assets/icons/editIcon.svg
  SvgGenImage get editIcon => const SvgGenImage('assets/icons/editIcon.svg');

  /// File path: assets/icons/exitIcon.svg
  SvgGenImage get exitIcon => const SvgGenImage('assets/icons/exitIcon.svg');

  /// File path: assets/icons/filterIcon.svg
  SvgGenImage get filterIcon =>
      const SvgGenImage('assets/icons/filterIcon.svg');

  /// File path: assets/icons/fizzy.svg
  SvgGenImage get fizzy => const SvgGenImage('assets/icons/fizzy.svg');

  /// File path: assets/icons/home_icon_active.svg
  SvgGenImage get homeIconActive =>
      const SvgGenImage('assets/icons/home_icon_active.svg');

  /// File path: assets/icons/home_icon_inactive.svg
  SvgGenImage get homeIconInactive =>
      const SvgGenImage('assets/icons/home_icon_inactive.svg');

  /// File path: assets/icons/hot.svg
  SvgGenImage get hot => const SvgGenImage('assets/icons/hot.svg');

  /// File path: assets/icons/imagePickerIcon.svg
  SvgGenImage get imagePickerIcon =>
      const SvgGenImage('assets/icons/imagePickerIcon.svg');

  /// File path: assets/icons/langIcon.svg
  SvgGenImage get langIcon => const SvgGenImage('assets/icons/langIcon.svg');

  /// File path: assets/icons/locationIcon.svg
  SvgGenImage get locationIcon =>
      const SvgGenImage('assets/icons/locationIcon.svg');

  /// File path: assets/icons/maxSizeIcon.svg
  SvgGenImage get maxSizeIcon =>
      const SvgGenImage('assets/icons/maxSizeIcon.svg');

  /// File path: assets/icons/meals.svg
  SvgGenImage get meals => const SvgGenImage('assets/icons/meals.svg');

  /// File path: assets/icons/menuIcon.svg
  SvgGenImage get menuIcon => const SvgGenImage('assets/icons/menuIcon.svg');

  /// File path: assets/icons/minSizeIcon.svg
  SvgGenImage get minSizeIcon =>
      const SvgGenImage('assets/icons/minSizeIcon.svg');

  /// File path: assets/icons/moneyIcon.svg
  SvgGenImage get moneyIcon => const SvgGenImage('assets/icons/moneyIcon.svg');

  /// File path: assets/icons/netflixIcon.svg
  SvgGenImage get netflixIcon =>
      const SvgGenImage('assets/icons/netflixIcon.svg');

  /// File path: assets/icons/pasta.svg
  SvgGenImage get pasta => const SvgGenImage('assets/icons/pasta.svg');

  /// File path: assets/icons/personIcon.svg
  SvgGenImage get personIcon =>
      const SvgGenImage('assets/icons/personIcon.svg');

  /// File path: assets/icons/phoneIcon.svg
  SvgGenImage get phoneIcon => const SvgGenImage('assets/icons/phoneIcon.svg');

  /// File path: assets/icons/profile_icon.png
  AssetGenImage get profileIcon =>
      const AssetGenImage('assets/icons/profile_icon.png');

  /// File path: assets/icons/psConttrollerIcon.svg
  SvgGenImage get psConttrollerIcon =>
      const SvgGenImage('assets/icons/psConttrollerIcon.svg');

  /// File path: assets/icons/psIcon.svg
  SvgGenImage get psIcon => const SvgGenImage('assets/icons/psIcon.svg');

  /// File path: assets/icons/reports_icon_active.svg
  SvgGenImage get reportsIconActive =>
      const SvgGenImage('assets/icons/reports_icon_active.svg');

  /// File path: assets/icons/reports_icon_inactive.svg
  SvgGenImage get reportsIconInactive =>
      const SvgGenImage('assets/icons/reports_icon_inactive.svg');

  /// File path: assets/icons/sandwich.svg
  SvgGenImage get sandwich => const SvgGenImage('assets/icons/sandwich.svg');

  /// File path: assets/icons/setting_icon_active.svg
  SvgGenImage get settingIconActive =>
      const SvgGenImage('assets/icons/setting_icon_active.svg');

  /// File path: assets/icons/setting_icon_inactive.svg
  SvgGenImage get settingIconInactive =>
      const SvgGenImage('assets/icons/setting_icon_inactive.svg');

  /// File path: assets/icons/soup.svg
  SvgGenImage get soup => const SvgGenImage('assets/icons/soup.svg');

  /// File path: assets/icons/subscripIcon.svg
  SvgGenImage get subscripIcon =>
      const SvgGenImage('assets/icons/subscripIcon.svg');

  /// File path: assets/icons/transIcon.svg
  SvgGenImage get transIcon => const SvgGenImage('assets/icons/transIcon.svg');

  /// File path: assets/icons/transIcon2.svg
  SvgGenImage get transIcon2 =>
      const SvgGenImage('assets/icons/transIcon2.svg');

  /// File path: assets/icons/trashIcon.svg
  SvgGenImage get trashIcon => const SvgGenImage('assets/icons/trashIcon.svg');

  /// File path: assets/icons/trashIcon2.svg
  SvgGenImage get trashIcon2 =>
      const SvgGenImage('assets/icons/trashIcon2.svg');

  /// File path: assets/icons/whatsappIcon.svg
  SvgGenImage get whatsappIcon =>
      const SvgGenImage('assets/icons/whatsappIcon.svg');

  /// File path: assets/icons/wifiIcon.svg
  SvgGenImage get wifiIcon => const SvgGenImage('assets/icons/wifiIcon.svg');

  /// File path: assets/icons/workerIconActive.svg
  SvgGenImage get workerIconActive =>
      const SvgGenImage('assets/icons/workerIconActive.svg');

  /// File path: assets/icons/workerIconInactive.svg
  SvgGenImage get workerIconInactive =>
      const SvgGenImage('assets/icons/workerIconInactive.svg');

  /// List of all assets
  List<dynamic> get values => [
        acIcon,
        addLogoIcon,
        coffeIcon,
        cold,
        confirmIcon,
        correctIcon,
        correctIconGreen,
        correctIconPurple,
        dessert,
        doneIcon,
        doorIcon,
        editIcon,
        exitIcon,
        filterIcon,
        fizzy,
        homeIconActive,
        homeIconInactive,
        hot,
        imagePickerIcon,
        langIcon,
        locationIcon,
        maxSizeIcon,
        meals,
        menuIcon,
        minSizeIcon,
        moneyIcon,
        netflixIcon,
        pasta,
        personIcon,
        phoneIcon,
        profileIcon,
        psConttrollerIcon,
        psIcon,
        reportsIconActive,
        reportsIconInactive,
        sandwich,
        settingIconActive,
        settingIconInactive,
        soup,
        subscripIcon,
        transIcon,
        transIcon2,
        trashIcon,
        trashIcon2,
        whatsappIcon,
        wifiIcon,
        workerIconActive,
        workerIconInactive
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/cafe.png
  AssetGenImage get cafe => const AssetGenImage('assets/images/cafe.png');

  /// File path: assets/images/dummy_data.png
  AssetGenImage get dummyData =>
      const AssetGenImage('assets/images/dummy_data.png');

  /// File path: assets/images/hot.png
  AssetGenImage get hot => const AssetGenImage('assets/images/hot.png');

  /// File path: assets/images/login.png
  AssetGenImage get login => const AssetGenImage('assets/images/login.png');

  /// File path: assets/images/otp.png
  AssetGenImage get otp => const AssetGenImage('assets/images/otp.png');

  /// File path: assets/images/ps.png
  AssetGenImage get ps => const AssetGenImage('assets/images/ps.png');

  /// File path: assets/images/r2_logo.png
  AssetGenImage get r2Logo => const AssetGenImage('assets/images/r2_logo.png');

  /// File path: assets/images/register.png
  AssetGenImage get register =>
      const AssetGenImage('assets/images/register.png');

  /// File path: assets/images/splash.png
  AssetGenImage get splash => const AssetGenImage('assets/images/splash.png');

  /// File path: assets/images/welcome.png
  AssetGenImage get welcome => const AssetGenImage('assets/images/welcome.png');

  /// List of all assets
  List<AssetGenImage> get values =>
      [cafe, dummyData, hot, login, otp, ps, r2Logo, register, splash, welcome];
}

class Assets {
  Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
